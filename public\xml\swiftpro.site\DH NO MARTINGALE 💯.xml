<xml xmlns="https://developers.google.com/blockly/xml" is_dbot="true" collection="false">
  <block type="trade_definition" id="Uzj^,=$W(gin;z00cQ~9" deletable="false" x="0" y="0">
    <statement name="TRADE_OPTIONS">
      <block type="trade_definition_market" id="Yd%lY,{v)xE:AK1{L6e?" deletable="false" movable="false">
        <field name="MARKET_LIST">synthetic_index</field>
        <field name="SUBMARKET_LIST">random_index</field>
        <field name="SYMBOL_LIST">1HZ10V</field>
        <next>
          <block type="trade_definition_tradetype" id="G{T+YUy?78*FG=9]yA98" deletable="false" movable="false">
            <field name="TRADETYPECAT_LIST">callput</field>
            <field name="TRADETYPE_LIST">callput</field>
            <next>
              <block type="trade_definition_contracttype" id="XgH;7xMa/l(gwI,eh1u}" deletable="false" movable="false">
                <field name="TYPE_LIST">both</field>
                <next>
                  <block type="trade_definition_candleinterval" id="2:m[90T8{f%a7sv,bk`4" deletable="false" movable="false">
                    <field name="CANDLEINTERVAL_LIST">60</field>
                    <next>
                      <block type="trade_definition_restartbuysell" id="X%NXQlt$v_.M{l##2|j+" deletable="false" movable="false">
                        <field name="TIME_MACHINE_ENABLED">FALSE</field>
                        <next>
                          <block type="trade_definition_restartonerror" id="8-`.;c9Z;uDG8Ojocd.s" deletable="false" movable="false">
                            <field name="RESTARTONERROR">TRUE</field>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="SUBMARKET">
      <block type="trade_definition_tradeoptions" id=":Bt1/Xqu[/Je1ZjG`1(j">
        <mutation xmlns="http://www.w3.org/1999/xhtml" has_first_barrier="false" has_second_barrier="false" has_prediction="false"></mutation>
        <field name="DURATIONTYPE_LIST">t</field>
        <value name="DURATION">
          <shadow type="math_number_positive" id="Z#$VCq$?wfkp$,bki9am">
            <field name="NUM">1</field>
          </shadow>
        </value>
        <value name="AMOUNT">
          <shadow type="math_number_positive" id="ocRVs@o,-Bhp[_/e=gkQ">
            <field name="NUM">0.35</field>
          </shadow>
        </value>
      </block>
    </statement>
  </block>
  <block type="during_purchase" id="%2h)SN+~aPh1+b%OHjH@" x="720" y="0">
    <statement name="DURING_PURCHASE_STACK">
      <block type="controls_if" id="!ZThZay#XeRlH(]v)E$h">
        <value name="IF0">
          <block type="check_sell" id="s=jFoRFE;q)*#pcD!H`0"></block>
        </value>
      </block>
    </statement>
  </block>
  <block type="after_purchase" id="]pmwR%q4dv1PJa55m?Xr" x="720" y="248">
    <statement name="AFTERPURCHASE_STACK">
      <block type="trade_again" id="`ku;{K+m_G0_az?2OZZ`"></block>
    </statement>
  </block>
  <block type="before_purchase" id="t!5Z$0ShdmjxS;4mS3C," deletable="false" x="0" y="576">
    <statement name="BEFOREPURCHASE_STACK">
      <block type="purchase" id="LbHDEKdz/G_s`~O#Pf|~">
        <field name="PURCHASE_LIST">CALL</field>
      </block>
    </statement>
  </block>
</xml>