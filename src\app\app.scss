.bot {
    --bot-content-height: calc(100vh - 140px);
    --bot-content-width: calc(100vw - 366px);
    --drawer-content-height: calc(100vh - 394px);
    --drawer-content-height-no-stat: calc(100vh - 233px);
    --drawer-scroll-height: calc(100vh - 365px);
    --drawer-content-height-mobile: calc(100% - 233px);
    --tab-content-height: calc(100vh - 16.6rem);
    --tab-content-height-mobile: calc(100vh - 6rem);
    --zindex-drawer: 5;
    --zindex-modal: 6;
    --zindex-draggable-modal: 7;
    --zindex-snackbar: 8;

    &-dashboard {
        background: var(--general-section-1);
    }
}

.modal-root {
    position: absolute;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    bottom: 0;
    left: 0;
    background: rgb(0 0 0 / 72%);
    right: 0;
    opacity: 0;
    z-index: -1;
}

.modal-root:not(:empty) {
    display: flex;
    opacity: 1;
    z-index: 9998;
    touch-action: none;
}

strong {
    font-weight: bold;
}
