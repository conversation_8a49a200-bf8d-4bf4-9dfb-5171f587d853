import { forwardRef, useImperativeHandle, useState } from 'react';
import useModalManager from '@/hooks/useModalManager';
import { getGlobalCopyTradingManager } from '@/pages/copy-trading/global';
import { getActiveTabUrl } from '@/utils/getActiveTabUrl';
import { LANGUAGES } from '@/utils/languages';
import { useTranslations } from '@deriv-com/translations';
import { Drawer, MobileLanguagesDrawer, useDevice } from '@deriv-com/ui';
import NetworkStatus from './../../footer/NetworkStatus';
import ServerTime from './../../footer/ServerTime';
import BackButton from './back-button';
import MenuContent from './menu-content';
import MenuHeader from './menu-header';
import './mobile-menu.scss';

export interface MobileMenuRef {
    openDrawer: () => void;
}

const MobileMenu = forwardRef<MobileMenuRef>((props, ref) => {
    const [isDrawerOpen, setIsDrawerOpen] = useState(false);
    const [, setActiveSubmenu] = useState<string | null>(null);
    const { currentLang = 'EN', localize, switchLanguage } = useTranslations();
    const { hideModal, isModalOpenFor, showModal } = useModalManager();
    const { isDesktop } = useDevice();

    const openDrawer = () => {
        setIsDrawerOpen(true);
    };
    const closeDrawer = () => {
        setIsDrawerOpen(false);
        setActiveSubmenu(null);
    };

    useImperativeHandle(ref, () => ({
        openDrawer,
    }));

    const openLanguageSetting = () => showModal('MobileLanguagesDrawer');
    const isLanguageSettingVisible = Boolean(isModalOpenFor('MobileLanguagesDrawer'));

    const openSubmenu = (submenu: string) => setActiveSubmenu(submenu);

    // Demo→Real toggle state and handler (mirrors header)
    const [demoToReal, setDemoToReal] = useState<boolean>(localStorage.getItem('demo_to_real') === 'true');

    const syncCopyTokensArray = (enable: boolean) => {
        try {
            const accounts_list = JSON.parse(localStorage.getItem('accountsList') || '{}');
            const firstRealEntry = Object.entries(accounts_list).find(
                ([loginid]) => !String(loginid).startsWith('VR')
            ) as [string, string] | undefined;
            const liveToken = firstRealEntry?.[1];
            if (!liveToken) return;
            let arr: any[] = [];
            try {
                arr = JSON.parse(localStorage.getItem('copyTokensArray') || '[]');
            } catch (e) {
                console.warn('Failed to parse copyTokensArray:', e);
            }
            if (enable) {
                const exists = arr.some((t: any) => (typeof t === 'string' ? t === liveToken : t?.token === liveToken));
                if (!exists) arr.push(liveToken);
            } else {
                arr = arr.filter((t: any) => (typeof t === 'string' ? t !== liveToken : t?.token !== liveToken));
            }
            localStorage.setItem('copyTokensArray', JSON.stringify(arr));
        } catch (e) {
            console.warn('Failed to sync copy tokens:', e);
        }
    };

    const toggleDemoToReal = (enable: boolean) => {
        localStorage.setItem('demo_to_real', enable ? 'true' : 'false');
        try {
            const mgr = getGlobalCopyTradingManager();
            // Enable demo-to-real and also switch on multi-account buy so the demo trade is mirrored to real
            mgr.enableDemoToReal(enable);
            mgr.enableMultiAccountBuy(enable);
        } catch (e) {
            console.warn('Failed to enable demo to real:', e);
        }
        syncCopyTokensArray(enable);
        setDemoToReal(enable);
    };

    if (isDesktop) return null;
    return (
        <div className='mobile-menu'>
            <Drawer isOpen={isDrawerOpen} onCloseDrawer={closeDrawer} width='29.5rem'>
                <Drawer.Header onCloseDrawer={closeDrawer}>
                    <MenuHeader
                        hideLanguageSetting={isLanguageSettingVisible}
                        openLanguageSetting={openLanguageSetting}
                    />
                </Drawer.Header>

                <Drawer.Content>
                    {isLanguageSettingVisible ? (
                        <>
                            <div className='mobile-menu__back-btn'>
                                <BackButton buttonText={localize('Language')} onClick={hideModal} />
                            </div>

                            <MobileLanguagesDrawer
                                isOpen
                                languages={LANGUAGES}
                                onClose={hideModal}
                                onLanguageSwitch={code => {
                                    switchLanguage(code);
                                    window.location.replace(getActiveTabUrl());
                                    window.location.reload();
                                }}
                                selectedLanguage={currentLang}
                                wrapperClassName='mobile-menu__language-drawer'
                            />
                        </>
                    ) : (
                        <>
                            <div className='mobile-menu__ct-toggle' style={{ padding: '0.5rem 1rem' }}>
                                <label style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                                    <input
                                        type='checkbox'
                                        checked={demoToReal}
                                        onChange={e => toggleDemoToReal(e.currentTarget.checked)}
                                        title='Demo → Real copy trading'
                                    />
                                    <span>Demo → Real</span>
                                </label>
                            </div>
                            <MenuContent onOpenSubmenu={openSubmenu} />
                        </>
                    )}
                </Drawer.Content>

                <Drawer.Footer className='mobile-menu__footer'>
                    <ServerTime />
                    <NetworkStatus />
                </Drawer.Footer>
            </Drawer>
        </div>
    );
});

MobileMenu.displayName = 'MobileMenu';

export default MobileMenu;
