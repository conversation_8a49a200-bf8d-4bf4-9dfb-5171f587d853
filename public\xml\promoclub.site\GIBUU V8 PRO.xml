<xml xmlns="https://developers.google.com/blockly/xml" is_dbot="true" collection="false">
  <variables>
    <variable id="W4$:ZQCmEz#8+=4ysv5Y">STOP LOSS</variable>
    <variable id="j}8O`Vs+RJljIwPu-_:_">STAKE</variable>
    <variable id="]W!z6/f^2`-6)La-t-4/">text</variable>
    <variable id="gfO@8Wi$VN.r;]DUW$],">text1</variable>
    <variable id="6w)9zvDM;]3$SF:gjkST">text2</variable>
    <variable id="mXtFswo{p,|%W1:V-$+r">SET TARGET</variable>
    <variable id="%L?;380E6Lr^3b.%}t5Q">win amount</variable>
    <variable id="n;]SWgIZo_%zMm%o`mb+">text3</variable>
    <variable id="r;j5hdLRm`b6LFCDue7-">martingale</variable>
  </variables>
  <block type="trade_definition" id="/EV7H`5de7l-wNfuJ,ac" deletable="false" x="0" y="60">
    <statement name="TRADE_OPTIONS">
      <block type="trade_definition_market" id="qbjYsBJ?h,Jy/-tTCuM#" deletable="false" movable="false">
        <field name="MARKET_LIST">synthetic_index</field>
        <field name="SUBMARKET_LIST">random_index</field>
        <field name="SYMBOL_LIST">1HZ100V</field>
        <next>
          <block type="trade_definition_tradetype" id="#^c%qIWO[x60dWw145Wb" deletable="false" movable="false">
            <field name="TRADETYPECAT_LIST">callput</field>
            <field name="TRADETYPE_LIST">higherlower</field>
            <next>
              <block type="trade_definition_contracttype" id="feG_JtO4xh]sAd/sG|6u" deletable="false" movable="false">
                <field name="TYPE_LIST">CALL</field>
                <next>
                  <block type="trade_definition_candleinterval" id="liWLntd03[qF___W(o5[" deletable="false" movable="false">
                    <field name="CANDLEINTERVAL_LIST">14400</field>
                    <next>
                      <block type="trade_definition_restartbuysell" id="34Gh)e_^YZt5mxtG=$wt" deletable="false" movable="false">
                        <field name="TIME_MACHINE_ENABLED">FALSE</field>
                        <next>
                          <block type="trade_definition_restartonerror" id="NYh{Gh.c#8b3?Fj@$Cu~" deletable="false" movable="false">
                            <field name="RESTARTONERROR">TRUE</field>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="INITIALIZATION">
      <block type="text_print" id="~:`Gf|s^CKyR8P*Tk/Xf">
        <value name="TEXT">
          <shadow type="text" id="3_fs#!:Bfm,C|w!O~,2+">
            <field name="TEXT">GIBUU PRO V8</field>
          </shadow>
        </value>
        <next>
          <block type="variables_set" id="@^e~XHlWF(mTsq3cNfur">
            <field name="VAR" id="W4$:ZQCmEz#8+=4ysv5Y">STOP LOSS</field>
            <value name="VALUE">
              <block type="math_number" id="Aof*5/U,qWr5hO!SeW3[">
                <field name="NUM">9999999</field>
              </block>
            </value>
            <next>
              <block type="variables_set" id="t@s-yG#1F9h`bsW]C-?+">
                <field name="VAR" id="mXtFswo{p,|%W1:V-$+r">SET TARGET</field>
                <value name="VALUE">
                  <block type="math_number" id="8v_T@]aLNGZ,D)xsl[A%">
                    <field name="NUM">999999</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="[E+VTI$M:Y*@HBwxmPbD">
                    <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">STAKE</field>
                    <value name="VALUE">
                      <block type="math_number" id="1v0CU?|y|,.z!/UCT]S%">
                        <field name="NUM">10</field>
                      </block>
                    </value>
                    <next>
                      <block type="variables_set" id="y*8d3Wt47OzJM/AVo%,t">
                        <field name="VAR" id="%L?;380E6Lr^3b.%}t5Q">win amount</field>
                        <value name="VALUE">
                          <block type="math_number" id="fAxoh~8Bg#}{!|n.((PS">
                            <field name="NUM">10</field>
                          </block>
                        </value>
                        <next>
                          <block type="variables_set" id="!)tKWP=-yOlmkdk*0Dk3">
                            <field name="VAR" id="r;j5hdLRm`b6LFCDue7-">martingale</field>
                            <value name="VALUE">
                              <block type="math_number" id="t0AC?:snXi!tT@WLd~82">
                                <field name="NUM">0.5</field>
                              </block>
                            </value>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="SUBMARKET">
      <block type="trade_definition_tradeoptions" id="%|Z+mihBP/oNkR!/u2+s">
        <mutation xmlns="http://www.w3.org/1999/xhtml" has_first_barrier="true" has_second_barrier="false" has_prediction="false"></mutation>
        <field name="DURATIONTYPE_LIST">t</field>
        <field name="BARRIEROFFSETTYPE_LIST">+</field>
        <value name="DURATION">
          <block type="math_number" id="c3}B)%|c9rq]v%YU*WZc">
            <field name="NUM">5</field>
          </block>
        </value>
        <value name="AMOUNT">
          <block type="variables_get" id="v~`kwLseaqS(Mkb:$VwJ">
            <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">STAKE</field>
          </block>
        </value>
        <value name="BARRIEROFFSET">
          <shadow type="math_number_positive" id="|Fi))aalURBJ-6ql,MHP">
            <field name="NUM">0.1</field>
          </shadow>
        </value>
      </block>
    </statement>
  </block>
  <block type="during_purchase" id="XgSFW9Vsh./:dIQZlqDJ" collapsed="true" x="956" y="60"></block>
  <block type="after_purchase" id="!@ZfOpM5AUV=H{C;|+Y{" x="956" y="156">
    <statement name="AFTERPURCHASE_STACK">
      <block type="controls_if" id="W*7aRmk{2r[lO|vcc_$D">
        <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
        <value name="IF0">
          <block type="contract_check_result" id=".S*72cic*a~=.H3~2dU]">
            <field name="CHECK_RESULT">win</field>
          </block>
        </value>
        <statement name="DO0">
          <block type="text_join" id="yqL,;I0KP/B3.DoOrbH[">
            <field name="VARIABLE" id="]W!z6/f^2`-6)La-t-4/">text</field>
            <statement name="STACK">
              <block type="text_statement" id="HY33q1:`A?A#XL1W{Nry">
                <value name="TEXT">
                  <shadow type="text" id="~=b6CR.N#3jH?.fQMz+[">
                    <field name="TEXT"></field>
                  </shadow>
                  <block type="text" id="4d@3GX)D)AX)-YliGP)+">
                    <field name="TEXT">Profit: $</field>
                  </block>
                </value>
                <next>
                  <block type="text_statement" id="P#}`@8/2X|gV6W5w9uxe">
                    <value name="TEXT">
                      <shadow type="text" id="?h+]Bz`pvPN]N%Yn[q^_">
                        <field name="TEXT"></field>
                      </shadow>
                      <block type="read_details" id="?D}/_T{zRDmRQ:E^3Q7!">
                        <field name="DETAIL_INDEX">4</field>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </statement>
            <next>
              <block type="notify" id="z|l);)9=NL4OZ%wvqjap">
                <field name="NOTIFICATION_TYPE">success</field>
                <field name="NOTIFICATION_SOUND">silent</field>
                <value name="MESSAGE">
                  <block type="variables_get" id="[.jp)0?IY.ho%!hU;jp`">
                    <field name="VAR" id="]W!z6/f^2`-6)La-t-4/">text</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="9q6s.BJ;)b2,g=G`,te^">
                    <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">STAKE</field>
                    <value name="VALUE">
                      <block type="variables_get" id="lFF7)SzR,QdGF[;%=t8s">
                        <field name="VAR" id="%L?;380E6Lr^3b.%}t5Q">win amount</field>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </statement>
        <statement name="ELSE">
          <block type="text_join" id="dLyEh*M^nkZ1N5z20oWg">
            <field name="VARIABLE" id="gfO@8Wi$VN.r;]DUW$],">text1</field>
            <statement name="STACK">
              <block type="text_statement" id="3g!CK5laE.8U]le5a:(D">
                <value name="TEXT">
                  <shadow type="text" id="LnynSXin))c2|Dui;TmL">
                    <field name="TEXT"></field>
                  </shadow>
                  <block type="text" id="LEYeY5|yT*mo5.N!h7Qi">
                    <field name="TEXT">Loss: $</field>
                  </block>
                </value>
                <next>
                  <block type="text_statement" id="qwR8jdJqQIn11lEBMA^B">
                    <value name="TEXT">
                      <shadow type="text" id="0t[m[paQs(6)0Stzmt5D">
                        <field name="TEXT"></field>
                      </shadow>
                      <block type="math_single" id=".:u]mY?[}[KQ+_uJXJ$U">
                        <field name="OP">ABS</field>
                        <value name="NUM">
                          <shadow type="math_number" id=",i3@UYn7j6+Z$nbfGoRF">
                            <field name="NUM">9</field>
                          </shadow>
                          <block type="read_details" id="*0DRGcQe|w=DvVOofh=7">
                            <field name="DETAIL_INDEX">4</field>
                          </block>
                        </value>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </statement>
            <next>
              <block type="notify" id="GS8~Q`F|9,`:v5.$~cf[">
                <field name="NOTIFICATION_TYPE">warn</field>
                <field name="NOTIFICATION_SOUND">silent</field>
                <value name="MESSAGE">
                  <block type="variables_get" id="Xe:Z$4aLd9x4+GAa|5eW">
                    <field name="VAR" id="gfO@8Wi$VN.r;]DUW$],">text1</field>
                  </block>
                </value>
                <next>
                  <block type="math_change" id=",/[w+KV-:#U*q%b%RAl/">
                    <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">STAKE</field>
                    <value name="DELTA">
                      <shadow type="math_number" id="C_A:uMYI9Toc^XucUalk">
                        <field name="NUM">1</field>
                      </shadow>
                      <block type="math_arithmetic" id="^K=8HyMxx32Fhui?]FuO">
                        <field name="OP">MULTIPLY</field>
                        <value name="A">
                          <shadow type="math_number" id="_22*kr0rz|w(f@lJ-cK:">
                            <field name="NUM">1</field>
                          </shadow>
                          <block type="math_single" id="UBT+^$#BJ[1mC:9y/n^x">
                            <field name="OP">ABS</field>
                            <value name="NUM">
                              <shadow type="math_number" id="Sa}~{P#N-e]?(Zj82{m!">
                                <field name="NUM">9</field>
                              </shadow>
                              <block type="read_details" id="[}yaVb)xa2jX!G-[Fs@|">
                                <field name="DETAIL_INDEX">4</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <value name="B">
                          <shadow type="math_number" id="Nuj0-#5Q2O+^]8piP9@)">
                            <field name="NUM">1</field>
                          </shadow>
                          <block type="variables_get" id="*d$pXbHO3KllUC}WCgTR">
                            <field name="VAR" id="r;j5hdLRm`b6LFCDue7-">martingale</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <next>
                      <block type="controls_if" id="NyevUP-eJoGf2/32~S/M">
                        <value name="IF0">
                          <block type="logic_compare" id="7_3[,e@?Pd|FIN_~j}i/">
                            <field name="OP">GTE</field>
                            <value name="A">
                              <block type="math_single" id="EDJ65y%qHL~l8.VhcH_v">
                                <field name="OP">ABS</field>
                                <value name="NUM">
                                  <shadow type="math_number" id="8:*K4TN})6gNgSqW:NCX">
                                    <field name="NUM">9</field>
                                  </shadow>
                                </value>
                              </block>
                            </value>
                            <value name="B">
                              <block type="variables_get" id="|4(4bXb4+f-Tko:5M^ih">
                                <field name="VAR" id="W4$:ZQCmEz#8+=4ysv5Y">STOP LOSS</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <statement name="DO0">
                          <block type="variables_set" id="*h:RS2/=pxv^GRG|S=yR">
                            <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">STAKE</field>
                            <value name="VALUE">
                              <block type="variables_get" id="eY{@/n%h+*~lwqMGEx04">
                                <field name="VAR" id="%L?;380E6Lr^3b.%}t5Q">win amount</field>
                              </block>
                            </value>
                          </block>
                        </statement>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </statement>
        <next>
          <block type="text_join" id="P!:`a0:f=SNvAmX7haK7">
            <field name="VARIABLE" id="6w)9zvDM;]3$SF:gjkST">text2</field>
            <statement name="STACK">
              <block type="text_statement" id="URI+Sg5T]obk|oFCa,~Z">
                <value name="TEXT">
                  <shadow type="text" id="kaC@MGM{O)*^_NNS^2hW">
                    <field name="TEXT"></field>
                  </shadow>
                  <block type="text" id="?qj8-*/,_O/w2pTBuqS/">
                    <field name="TEXT">Total Profit: $</field>
                  </block>
                </value>
                <next>
                  <block type="text_statement" id="{a_j$$/.NhJT+uhw;|zR">
                    <value name="TEXT">
                      <shadow type="text" id="mpU0:=Z`2!/@CP?(O(~H">
                        <field name="TEXT"></field>
                      </shadow>
                      <block type="total_profit" id="1aD!uR$dh3etw!Wj](1K"></block>
                    </value>
                  </block>
                </next>
              </block>
            </statement>
            <next>
              <block type="notify" id="iO`%^Pd]LcNX@t%N[UGW">
                <field name="NOTIFICATION_TYPE">info</field>
                <field name="NOTIFICATION_SOUND">silent</field>
                <value name="MESSAGE">
                  <block type="variables_get" id="9xqovc$au9LL]LYxK6so">
                    <field name="VAR" id="6w)9zvDM;]3$SF:gjkST">text2</field>
                  </block>
                </value>
                <next>
                  <block type="controls_if" id="f;Y(`z?%k/Ot9@PI)os:">
                    <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                    <value name="IF0">
                      <block type="logic_compare" id="=Sa-0Q0M8=63D@zokT=Z">
                        <field name="OP">LT</field>
                        <value name="A">
                          <block type="total_profit" id="Hx+W`p9-Z!O_P__[+4J@"></block>
                        </value>
                        <value name="B">
                          <block type="variables_get" id="8%ChL?bBEKa7RU{hI5S6">
                            <field name="VAR" id="mXtFswo{p,|%W1:V-$+r">SET TARGET</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="trade_again" id="ZIoC{q.02?+BS8X]6FoF"></block>
                    </statement>
                    <statement name="ELSE">
                      <block type="text_join" id="Mv#xezHr-/8BK*;Z=~r/">
                        <field name="VARIABLE" id="n;]SWgIZo_%zMm%o`mb+">text3</field>
                        <statement name="STACK">
                          <block type="text_statement" id="T:D=n^*IrC2rOB!7;+6]">
                            <value name="TEXT">
                              <shadow type="text" id="T#}8UVL3M:8ZlU/Spd)2">
                                <field name="TEXT"></field>
                              </shadow>
                              <block type="text" id="l=O8?*aIy9j|%{)YHFdF">
                                <field name="TEXT">FOR STRATEGIES CALL[+254737822783] $</field>
                              </block>
                            </value>
                            <next>
                              <block type="text_statement" id="kKqyggh?cn)[NXz!_Zir">
                                <value name="TEXT">
                                  <shadow type="text" id="q+lO5[N0:@6[aMGVOXuX">
                                    <field name="TEXT"></field>
                                  </shadow>
                                  <block type="total_profit" id="k3b#@%~[,1P}$yF)sa2U"></block>
                                </value>
                              </block>
                            </next>
                          </block>
                        </statement>
                        <next>
                          <block type="text_print" id="R!G6-0:fk?5`Qn|PSWBH">
                            <value name="TEXT">
                              <shadow type="text" id="P`Y`70VJ1|2_Gu*))(Gi">
                                <field name="TEXT">abc</field>
                              </shadow>
                              <block type="variables_get" id="TVejp+5hD-6}[hK*~O3$">
                                <field name="VAR" id="n;]SWgIZo_%zMm%o`mb+">text3</field>
                              </block>
                            </value>
                          </block>
                        </next>
                      </block>
                    </statement>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
  </block>
  <block type="before_purchase" id=";.F5x[r]ggp)(WhUJ^+B" collapsed="true" deletable="false" x="0" y="928">
    <statement name="BEFOREPURCHASE_STACK">
      <block type="apollo_purchase" id="SyCr[FK!d[B96%5:P_bF">
        <field name="PURCHASE_LIST">CALL</field>
      </block>
    </statement>
  </block>
  <block type="read_details" id="]Lm}]0-|g|!l_zcuGt_I" disabled="true" x="0" y="1766">
    <field name="DETAIL_INDEX">4</field>
  </block>
  <block type="last_digit" id="ffB%fW7pi)tvlBq3#()Q" disabled="true" x="0" y="1856"></block>
  <block type="text" id="+POkarC]rXFstljT/{Ar" disabled="true" x="0" y="1944">
    <field name="TEXT">STILL</field>
  </block>
</xml>