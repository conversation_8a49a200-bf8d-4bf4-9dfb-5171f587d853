<xml xmlns="https://developers.google.com/blockly/xml" is_dbot="true" collection="false">
  <variables>
    <variable id="gou;:qzRHuz3Fh+MMi-%">stake</variable>
    <variable id="tlTJ4z_xt:Sz8w1N!wMF">Tick 1</variable>
    <variable id="rL5puODwmVGP1sDiCb3_">Prediction</variable>
    <variable id="d-=QN$@L|S!N,B}sTG(D">Tick 2</variable>
    <variable id="9t|Z37+SJpUDhQ|6KIbN">duke</variable>
    <variable id="(%#TMH^XDwW!Ai|}It{l">win stake</variable>
    <variable id="B2UC}i;Z?$=@iPS5p[N1">Tick 3</variable>
  </variables>
  <block type="trade_definition" id="Tdd`(M4chg{[!L|@Yx)_" deletable="false" x="0" y="60">
    <statement name="TRADE_OPTIONS">
      <block type="trade_definition_market" id="KnPGJDI8}S#]-l1gZ7$)" deletable="false" movable="false">
        <field name="MARKET_LIST">synthetic_index</field>
        <field name="SUBMARKET_LIST">random_index</field>
        <field name="SYMBOL_LIST">1HZ10V</field>
        <next>
          <block type="trade_definition_tradetype" id="P]?W4Z}T|%]aeWpCqM[/" deletable="false" movable="false">
            <field name="TRADETYPECAT_LIST">digits</field>
            <field name="TRADETYPE_LIST">overunder</field>
            <next>
              <block type="trade_definition_contracttype" id="gD^.]/]Jm!2:0MYxk=cz" deletable="false" movable="false">
                <field name="TYPE_LIST">both</field>
                <next>
                  <block type="trade_definition_candleinterval" id="Zkn7o~m}5,vbq~@z$xDX" deletable="false" movable="false">
                    <field name="CANDLEINTERVAL_LIST">60</field>
                    <next>
                      <block type="trade_definition_restartbuysell" id="R]Cg{eCK1.[X(Hx}Oqdh" deletable="false" movable="false">
                        <field name="TIME_MACHINE_ENABLED">FALSE</field>
                        <next>
                          <block type="trade_definition_restartonerror" id="_n.c;Dog2E?VuoRcgs8j" deletable="false" movable="false">
                            <field name="RESTARTONERROR">TRUE</field>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="INITIALIZATION">
      <block type="variables_set" id="R8lMRtlqm_8|vlCBh~nv">
        <field name="VAR" id="gou;:qzRHuz3Fh+MMi-%">stake</field>
        <value name="VALUE">
          <block type="math_number" id="TPbEe(:DOj,)WryHp(c9">
            <field name="NUM">45</field>
          </block>
        </value>
        <next>
          <block type="variables_set" id="BdthTvw|b]]zF1d:(N:k">
            <field name="VAR" id="rL5puODwmVGP1sDiCb3_">Prediction</field>
            <value name="VALUE">
              <block type="math_number" id="1/0%0,!xV/F0xR%%]xr_">
                <field name="NUM">8</field>
              </block>
            </value>
            <next>
              <block type="variables_set" id="_dJ!GD~9`/Iy$g#((h2i" collapsed="true">
                <field name="VAR" id="9t|Z37+SJpUDhQ|6KIbN">duke</field>
                <value name="VALUE">
                  <block type="math_number" id="[?4QL+8d^vsXUa)_LA{g">
                    <field name="NUM">1</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="ZllynWuzrU;/CPgwvNmj" collapsed="true">
                    <field name="VAR" id="(%#TMH^XDwW!Ai|}It{l">win stake</field>
                    <value name="VALUE">
                      <block type="variables_get" id="|Z.k;oOH~5xv(liAbf-!">
                        <field name="VAR" id="gou;:qzRHuz3Fh+MMi-%">stake</field>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="SUBMARKET">
      <block type="trade_definition_tradeoptions" id="=NVq~@981L0hwq+h8;MP" collapsed="true">
        <mutation xmlns="http://www.w3.org/1999/xhtml" has_first_barrier="false" has_second_barrier="false" has_prediction="true"></mutation>
        <field name="DURATIONTYPE_LIST">t</field>
        <value name="DURATION">
          <shadow type="math_number_positive" id="DxRN(~y[[D{gM:C*:K^K">
            <field name="NUM">1</field>
          </shadow>
        </value>
        <value name="AMOUNT">
          <shadow type="math_number_positive" id="!YoGjxve7E+ex[P52N8[">
            <field name="NUM">0.35</field>
          </shadow>
          <block type="variables_get" id="jQ@C9E|mzWQ8**lUO3FW">
            <field name="VAR" id="gou;:qzRHuz3Fh+MMi-%">stake</field>
          </block>
        </value>
        <value name="PREDICTION">
          <shadow type="math_number_positive" id="9EvrnMB@zAntVPrN8N%c" inline="true">
            <field name="NUM">1</field>
          </shadow>
          <block type="variables_get" id="pR$xU);{swZMK)}oEOi+" collapsed="true">
            <field name="VAR" id="rL5puODwmVGP1sDiCb3_">Prediction</field>
          </block>
        </value>
      </block>
    </statement>
  </block>
  <block type="after_purchase" id="{d6yBYDV,i)-tqh^Is3^" collapsed="true" x="714" y="60">
    <statement name="AFTERPURCHASE_STACK">
      <block type="controls_if" id="n0juy!%PA(W^1qpHlQtV" collapsed="true">
        <mutation xmlns="http://www.w3.org/1999/xhtml" elseif="1" else="1"></mutation>
        <value name="IF0">
          <block type="logic_compare" id="i~U9shiCYibU$4z])dwX" collapsed="true">
            <field name="OP">GTE</field>
            <value name="A">
              <block type="total_profit" id="p-Qo!TfUY9K25`YP-#hI"></block>
            </value>
            <value name="B">
              <block type="variables_get" id="s_-pXv?XfqM;(e%-qv,S">
                <field name="VAR" id="(%#TMH^XDwW!Ai|}It{l">win stake</field>
              </block>
            </value>
          </block>
        </value>
        <statement name="DO0">
          <block type="set_tp" id=",sy/9-h{tSv9QK7,H^2#"></block>
        </statement>
        <value name="IF1">
          <block type="logic_operation" id="]_ROk-v;-.*!Y]fM4?M^" collapsed="true">
            <field name="OP">AND</field>
            <value name="A">
              <block type="logic_compare" id="CBz=$E[wM;ehHI}`Wsl=" collapsed="true">
                <field name="OP">GTE</field>
                <value name="A">
                  <block type="total_profit" id="qYQI3ULkM_|wg/#$#_uj"></block>
                </value>
                <value name="B">
                  <block type="math_number" id="g%|ycfGF38;3;a#=r6NL">
                    <field name="NUM">0.01</field>
                  </block>
                </value>
              </block>
            </value>
            <value name="B">
              <block type="contract_check_result" id="$8{;jT=}7xunR7CJ34yn">
                <field name="CHECK_RESULT">loss</field>
              </block>
            </value>
          </block>
        </value>
        <statement name="DO1">
          <block type="set_sl" id="srS*nqHU_PGZ2VM7al}?"></block>
        </statement>
        <statement name="ELSE">
          <block type="controls_if" id="N]J%q-zN#c~lo~aLy*=4" collapsed="true">
            <mutation xmlns="http://www.w3.org/1999/xhtml" elseif="3"></mutation>
            <value name="IF0">
              <block type="logic_compare" id="{`BZds[.wAKpVZBl]VT@">
                <field name="OP">GT</field>
                <value name="A">
                  <block type="total_profit" id="S|^mLtT.o@])H9Sk~tA)"></block>
                </value>
                <value name="B">
                  <block type="math_single" id="y:7[P6?^32(sJcpzf$y:">
                    <field name="OP">NEG</field>
                    <value name="NUM">
                      <shadow type="math_number" id="qn8V[IcND}JD+v6VyOZ$">
                        <field name="NUM">9</field>
                      </shadow>
                      <block type="variables_get" id="KPuw~ftq~?QB%^tRfyBB">
                        <field name="VAR" id="(%#TMH^XDwW!Ai|}It{l">win stake</field>
                      </block>
                    </value>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO0">
              <block type="variables_set" id="nBq@rh@Ss0mR7[CcEQN+">
                <field name="VAR" id="gou;:qzRHuz3Fh+MMi-%">stake</field>
                <value name="VALUE">
                  <block type="variables_get" id="Apw#8B=E)83pttm?!,Z7">
                    <field name="VAR" id="(%#TMH^XDwW!Ai|}It{l">win stake</field>
                  </block>
                </value>
              </block>
            </statement>
            <value name="IF1">
              <block type="logic_operation" id="opSTq4hbeu=rx,qcKS1^">
                <field name="OP">AND</field>
                <value name="A">
                  <block type="logic_compare" id=",ez;-yxF:lrpBE?T`BC2">
                    <field name="OP">LTE</field>
                    <value name="A">
                      <block type="total_profit" id="-|NE3],#oebQ-Bf/e*.#"></block>
                    </value>
                    <value name="B">
                      <block type="math_single" id="V#brfpZ}bcSJM69eNG7B">
                        <field name="OP">NEG</field>
                        <value name="NUM">
                          <shadow type="math_number" id="qn8V[IcND}JD+v6VyOZ$">
                            <field name="NUM">9</field>
                          </shadow>
                          <block type="variables_get" id=".Ai{(sqwtxopE0;W?;ro">
                            <field name="VAR" id="(%#TMH^XDwW!Ai|}It{l">win stake</field>
                          </block>
                        </value>
                      </block>
                    </value>
                  </block>
                </value>
                <value name="B">
                  <block type="contract_check_result" id="_F~h?ZjyWGO(5Q4-Y]mR">
                    <field name="CHECK_RESULT">loss</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO1">
              <block type="variables_set" id="~AGGIoy]y#ED]I{:qTGo">
                <field name="VAR" id="gou;:qzRHuz3Fh+MMi-%">stake</field>
                <value name="VALUE">
                  <block type="math_arithmetic" id="jqPf{H,,EzFE4ob:W{c(">
                    <field name="OP">MULTIPLY</field>
                    <value name="A">
                      <shadow type="math_number" id="n0~.rp1}Q*`lVIQHJz;s">
                        <field name="NUM">1</field>
                      </shadow>
                      <block type="variables_get" id="1pODHQ2{[azc1eG04?^T">
                        <field name="VAR" id="gou;:qzRHuz3Fh+MMi-%">stake</field>
                      </block>
                    </value>
                    <value name="B">
                      <shadow type="math_number" id="tnOl^O^3#J5@hETg{IJ7">
                        <field name="NUM">2.1</field>
                      </shadow>
                    </value>
                  </block>
                </value>
              </block>
            </statement>
            <value name="IF2">
              <block type="logic_compare" id="0I1Nu}D0C-0$]6u]YExg">
                <field name="OP">LT</field>
                <value name="A">
                  <block type="total_profit" id="i~|C6KdEmgvI}gYxu:rA"></block>
                </value>
                <value name="B">
                  <block type="math_number" id="=V1DQ$C3l32.jxp)8!U[">
                    <field name="NUM">0</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO2">
              <block type="variables_set" id="5LBkbb_GCKF)(}X]w8xY">
                <field name="VAR" id="gou;:qzRHuz3Fh+MMi-%">stake</field>
                <value name="VALUE">
                  <block type="variables_get" id=",dDWs+*OXsJu;Nsq+_?E">
                    <field name="VAR" id="gou;:qzRHuz3Fh+MMi-%">stake</field>
                  </block>
                </value>
              </block>
            </statement>
            <value name="IF3">
              <block type="logic_compare" id="BEo9hGwMF=Pf8|-A,y?c">
                <field name="OP">GT</field>
                <value name="A">
                  <block type="total_profit" id="?TKL{3[AwZXR|r_s;{`t"></block>
                </value>
                <value name="B">
                  <block type="math_number" id="hhyPKO/UQO8VK^(Fmg@z">
                    <field name="NUM">0</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO3">
              <block type="variables_set" id="(;RtGG?pqZr;24)qQJL^">
                <field name="VAR" id="gou;:qzRHuz3Fh+MMi-%">stake</field>
                <value name="VALUE">
                  <block type="variables_get" id="FsZSDD?UETMTu)jFr0|k">
                    <field name="VAR" id="(%#TMH^XDwW!Ai|}It{l">win stake</field>
                  </block>
                </value>
              </block>
            </statement>
            <next>
              <block type="trade_again" id="R;=Dx{`,Y#0hrp.6=X6|"></block>
            </next>
          </block>
        </statement>
      </block>
    </statement>
  </block>
  <block type="before_purchase" id="DD1lUD6Le=7WOqS!/[$=" collapsed="true" deletable="false" x="-3" y="860">
    <statement name="BEFOREPURCHASE_STACK">
      <block type="controls_if" id="VD91k5fg)Lnt/rZR{Hvc" collapsed="true">
        <value name="IF0">
          <block type="check_direction" id="f6:Ky=CII/4.hl-Jlw`1">
            <field name="CHECK_DIRECTION">fall</field>
          </block>
        </value>
        <statement name="DO0">
          <block type="controls_if" id="yqL80gJ2P|k[vUmx_:4%">
            <value name="IF0">
              <block type="logic_compare" id="3G`wrMMhpQ,RO:.ZbF,8">
                <field name="OP">GTE</field>
                <value name="A">
                  <block type="variables_get" id="%m07XV`{5DRpIdx546h7">
                    <field name="VAR" id="tlTJ4z_xt:Sz8w1N!wMF">Tick 1</field>
                  </block>
                </value>
                <value name="B">
                  <block type="math_number" id="-oqF_HmiRK7JteO8!#7(">
                    <field name="NUM">2</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO0">
              <block type="controls_if" id="vk!fD(UZu~Y=}0Uch$i_">
                <value name="IF0">
                  <block type="logic_compare" id=",K2dP0s9hp8B|v4BDqWg">
                    <field name="OP">GTE</field>
                    <value name="A">
                      <block type="variables_get" id="oB[L,qewVwB3yCYKltfi">
                        <field name="VAR" id="d-=QN$@L|S!N,B}sTG(D">Tick 2</field>
                      </block>
                    </value>
                    <value name="B">
                      <block type="math_number" id="Z}R-$sZ.Zz%bOg@wED5$">
                        <field name="NUM">2</field>
                      </block>
                    </value>
                  </block>
                </value>
                <statement name="DO0">
                  <block type="controls_if" id=";Ga=*BQF+;oct|Zk%1uw">
                    <value name="IF0">
                      <block type="logic_compare" id=":!=.kO)hf/];f]$aRIbW">
                        <field name="OP">GTE</field>
                        <value name="A">
                          <block type="variables_get" id="m=ow^{)h#E*X!WjDq4r1">
                            <field name="VAR" id="B2UC}i;Z?$=@iPS5p[N1">Tick 3</field>
                          </block>
                        </value>
                        <value name="B">
                          <block type="math_number" id="Od]2oNAS)k8I1QOasX@E">
                            <field name="NUM">2</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="controls_if" id="Baw8120tD7wcew^(i{1.">
                        <value name="IF0">
                          <block type="logic_compare" id="}lgzsMduzX*0rx@=YOjs">
                            <field name="OP">LTE</field>
                            <value name="A">
                              <block type="variables_get" id="]B-`F;W_lF,Ne$0Qbhbd">
                                <field name="VAR" id="tlTJ4z_xt:Sz8w1N!wMF">Tick 1</field>
                              </block>
                            </value>
                            <value name="B">
                              <block type="math_number" id="s{$8whHlvzytI)e`%c)U">
                                <field name="NUM">7</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <statement name="DO0">
                          <block type="controls_if" id="g;4X,T:N1+e*J!+Jg++r">
                            <value name="IF0">
                              <block type="logic_compare" id="ETNU#`75.RR;LYJ[%6gQ">
                                <field name="OP">LTE</field>
                                <value name="A">
                                  <block type="variables_get" id="$GXH1=ui[0|XW|E9e`47">
                                    <field name="VAR" id="d-=QN$@L|S!N,B}sTG(D">Tick 2</field>
                                  </block>
                                </value>
                                <value name="B">
                                  <block type="math_number" id="UE-@[wsf8R`~Uipy^9)9">
                                    <field name="NUM">7</field>
                                  </block>
                                </value>
                              </block>
                            </value>
                            <statement name="DO0">
                              <block type="controls_if" id="Sn*:QiJQ)1#2[^/Q27PT">
                                <value name="IF0">
                                  <block type="logic_compare" id="KT!g/)2WLsE$O8;7||`o">
                                    <field name="OP">LTE</field>
                                    <value name="A">
                                      <block type="variables_get" id="[rO$5%CIlE:ZimO3HBbl">
                                        <field name="VAR" id="B2UC}i;Z?$=@iPS5p[N1">Tick 3</field>
                                      </block>
                                    </value>
                                    <value name="B">
                                      <block type="math_number" id="erOj[){~Uw3i%%MM(-S-">
                                        <field name="NUM">7</field>
                                      </block>
                                    </value>
                                  </block>
                                </value>
                                <statement name="DO0">
                                  <block type="apollo_purchase" id="h6VcbFYne6Hs.@CL[!ON">
                                    <field name="PURCHASE_LIST">DIGITUNDER</field>
                                  </block>
                                </statement>
                              </block>
                            </statement>
                          </block>
                        </statement>
                      </block>
                    </statement>
                  </block>
                </statement>
              </block>
            </statement>
          </block>
        </statement>
      </block>
    </statement>
  </block>
  <block type="tick_analysis" id="G54GQ[omARJz8v1%on34" collapsed="true" x="-3" y="1060">
    <statement name="TICKANALYSIS_STACK">
      <block type="variables_set" id="@N-7x?$Gyvsc5d@;lGb=" collapsed="true">
        <field name="VAR" id="tlTJ4z_xt:Sz8w1N!wMF">Tick 1</field>
        <value name="VALUE">
          <block type="lists_getIndex" id="dAd?t3[9hNrsB`7)_f^m">
            <mutation xmlns="http://www.w3.org/1999/xhtml" statement="false" at="true"></mutation>
            <field name="MODE">GET</field>
            <field name="WHERE">FROM_END</field>
            <value name="VALUE">
              <block type="lastDigitList" id="]!k:G3R!AzvnU+Bn)nJ-"></block>
            </value>
            <value name="AT">
              <block type="math_number" id="yZ_*-YBB~)Xb;yz#h9IP">
                <field name="NUM">1</field>
              </block>
            </value>
          </block>
        </value>
        <next>
          <block type="variables_set" id="Q$j7bR(MB=6D3TN=x@n$" collapsed="true">
            <field name="VAR" id="d-=QN$@L|S!N,B}sTG(D">Tick 2</field>
            <value name="VALUE">
              <block type="lists_getIndex" id="CS|BMOAz24AaDX[Z_ZK_">
                <mutation xmlns="http://www.w3.org/1999/xhtml" statement="false" at="true"></mutation>
                <field name="MODE">GET</field>
                <field name="WHERE">FROM_END</field>
                <value name="VALUE">
                  <block type="lastDigitList" id="MtcIyZY}7^%7~`(y4dq^"></block>
                </value>
                <value name="AT">
                  <block type="math_number" id="xxofgV=}^d8XS,:e3e*V">
                    <field name="NUM">2</field>
                  </block>
                </value>
              </block>
            </value>
            <next>
              <block type="variables_set" id="b4M_W_$#$c)IxO7m[3ps" collapsed="true">
                <field name="VAR" id="B2UC}i;Z?$=@iPS5p[N1">Tick 3</field>
                <value name="VALUE">
                  <block type="lists_getIndex" id="4vux23[)]xkD[+l!wa+g">
                    <mutation xmlns="http://www.w3.org/1999/xhtml" statement="false" at="true"></mutation>
                    <field name="MODE">GET</field>
                    <field name="WHERE">FROM_END</field>
                    <value name="VALUE">
                      <block type="lastDigitList" id="uVI9N;L2SV7CZ5M3!akj"></block>
                    </value>
                    <value name="AT">
                      <block type="math_number" id="ozj1,b$,jB=iID5PNPl3">
                        <field name="NUM">3</field>
                      </block>
                    </value>
                  </block>
                </value>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
  </block>
</xml>