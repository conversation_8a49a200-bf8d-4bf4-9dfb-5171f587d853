.iframe-wrapper {
    width: 100%;
    height: var(--tab-content-height);
    display: flex;
    flex-direction: column;
    position: relative;
    flex: 1;

    // Mobile and tablet devices (including all iPad sizes)
    @media (max-width: 1400px) {
        height: var(--tab-content-height-mobile);
        min-height: var(--tab-content-height-mobile);
        width: 100vw;
        margin-left: calc(-50vw + 50%);
        margin-right: calc(-50vw + 50%);
    }

    // Specific handling for touch devices (iPad, tablets, phones)
    @media (pointer: coarse) {
        height: var(--tab-content-height-mobile);
        min-height: var(--tab-content-height-mobile);
        width: 100vw;
        margin-left: calc(-50vw + 50%);
        margin-right: calc(-50vw + 50%);
    }

    &__frame {
        width: 100%;
        height: 100%;
        border: none;
        border-radius: 0;
        background: var(--general-main-1);
        flex: 1;

        // Mobile and tablet devices (including all iPad sizes)
        @media (max-width: 1400px) {
            width: 100%;
            height: 100%;
            min-height: 100%;
        }

        // Specific handling for touch devices (iPad, tablets, phones)
        @media (pointer: coarse) {
            width: 100%;
            height: 100%;
            min-height: 100%;
        }
    }
}
