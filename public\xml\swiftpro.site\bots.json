[{"name": "Wealth Generator V2.xml", "file": "Wealth Generator V2.xml"}, {"name": "COOLKID.xml", "file": "COOLKID.xml"}, {"name": "Candle mine version 3.1.xml", "file": "Candle mine version 3.1.xml"}, {"name": "DBOTPRINTER  SPEED BOT.xml", "file": "DBOTPRINTER  SPEED BOT.xml"}, {"name": "DERIVKING AI ROBOT.xml", "file": "DERIVKING AI ROBOT.xml"}, {"name": "DH NO MARTINGALE 💯.xml", "file": "DH NO MARTINGALE 💯.xml"}, {"name": "Dbotprinter Auto Switch EVEN ODD BOT.xml", "file": "Dbotprinter Auto Switch EVEN ODD BOT.xml"}, {"name": "Dbotprinter OVER UNDER BOT.xml", "file": "Dbotprinter OVER UNDER BOT.xml"}, {"name": "Deriv wizard 1.xml", "file": "Deriv wizard 1.xml"}, {"name": "Digit hyper.xml", "file": "Digit hyper.xml"}, {"name": "Dollar dispenser (1).xml", "file": "Dollar dispenser (1).xml"}, {"name": "Dollar hunter HnR (1).xml", "file": "Dollar hunter HnR (1).xml"}, {"name": "ENHANCED_Digit_Switcher🤖VERSION5.xml", "file": "ENHANCED_Digit_Switcher🤖VERSION5.xml"}, {"name": "Even odd speed bot.xml", "file": "Even odd speed bot.xml"}, {"name": "Expert Speed Bot By CHOSEN DOLLAR PRINTER FX📉📉📉📈📈📈.xml", "file": "Expert Speed Bot By CHOSEN DOLLAR PRINTER FX📉📉📉📈📈📈.xml"}, {"name": "FALCON BOT.xml", "file": "FALCON BOT.xml"}, {"name": "GIBUU V8 PRO.xml", "file": "GIBUU V8 PRO.xml"}, {"name": "Kwote Kwote Bot.xml", "file": "Kwote Kwote Bot.xml"}, {"name": "M27 Original version.xml", "file": "M27 Original version.xml"}, {"name": "MATCHES AND DIFFERS BOT.xml", "file": "MATCHES AND DIFFERS BOT.xml"}, {"name": "MATRIX 001[💵BY NJAGI OFFICIAL 💵].xml", "file": "MATRIX 001[💵BY NJAGI OFFICIAL 💵].xml"}, {"name": "MEGA PRO BOT.xml", "file": "MEGA PRO BOT.xml"}, {"name": "Mask evenodd bot.xml", "file": "Mask evenodd bot.xml"}, {"name": "<PERSON><PERSON><PERSON> By The Risk Manager 1.xml", "file": "<PERSON><PERSON><PERSON> By The Risk Manager 1.xml"}, {"name": "NIGHT  CAP PRINTER BOT.xml", "file": "NIGHT  CAP PRINTER BOT.xml"}, {"name": "Ne ENHANCED_AUTO_C4_VOLT_🇬🇧_3_🇬🇧_AI_PREMIUM_🤖_1.xml", "file": "Ne ENHANCED_AUTO_C4_VOLT_🇬🇧_3_🇬🇧_AI_PREMIUM_🤖_1.xml"}, {"name": "Over-Destroyer💀by state fx.xml", "file": "Over-Destroyer💀by state fx.xml"}, {"name": "SCAPLEX   Ai  SN4 (1) (1).xml", "file": "SCAPLEX   Ai  SN4 (1) (1).xml"}, {"name": "SCAUCER SPEED BOT 🇱🇷.xml", "file": "SCAUCER SPEED BOT 🇱🇷.xml"}, {"name": "STATE.HnR.xml", "file": "STATE.HnR.xml"}, {"name": "STATES_Digit_Switcher🤖V2.xml", "file": "STATES_Digit_Switcher🤖V2.xml"}, {"name": "THE DOLLAR PRO.xml", "file": "THE DOLLAR PRO.xml"}, {"name": "THE TREND LOVER.xml", "file": "THE TREND LOVER.xml"}, {"name": "TRADE CITY BOT VERSION 2.1.xml", "file": "TRADE CITY BOT VERSION 2.1.xml"}, {"name": "TRADE CITY BOT Version 1.2.xml", "file": "TRADE CITY BOT Version 1.2.xml"}, {"name": "The Over 19 switcher5.xml", "file": "The Over 19 switcher5.xml"}, {"name": "ULTRA AI 2025.xml", "file": "ULTRA AI 2025.xml"}, {"name": "Under Market AI Bot.xml", "file": "Under Market AI Bot.xml"}, {"name": "Upgraded Candlemine  (1).xml", "file": "Upgraded Candlemine  (1).xml"}, {"name": "dec  entry point.xml", "file": "dec  entry point.xml"}, {"name": "mask matches speed bot 📈.xml", "file": "mask matches speed bot 📈.xml"}, {"name": "master G8 (OVER UNDER) BY STATE FX VERSION 1 (2026).xml", "file": "master G8 (OVER UNDER) BY STATE FX VERSION 1 (2026).xml"}, {"name": "💫PrimeTitan V1  (1).xml", "file": "💫PrimeTitan V1  (1).xml"}, {"name": "💫PrimeTitan V1  .xml", "file": "💫PrimeTitan V1  .xml"}]