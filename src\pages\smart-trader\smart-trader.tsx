import React from 'react';
import { observer } from 'mobx-react-lite';
import IframeWrapper from '@/components/iframe-wrapper';

const SmartTrader = observer(() => {
    return (
        <IframeWrapper
            src='https://tracktool.netlify.app/'
            title='Smart Trader'
            className='smart-trader-container'
        />
    );




                        <div className='smart-trader__row smart-trader__row--compact'>
                            <div className='smart-trader__field'>
                                <label htmlFor='st-ticks'>{localize('Ticks')}</label>
                                <input id='st-ticks' type='number' min={1} max={1} value={1} disabled />
                            </div>
                            <div className='smart-trader__field'>
                                <label htmlFor='st-stake'>{localize('Stake')}</label>
                                <input
                                    id='st-stake'
                                    type='number'
                                    step='0.01'
                                    min={0.35}
                                    value={stake}
                                    onChange={e => setStake(Number(e.target.value))}
                                />

                                {/* Strategy controls */}
                                {tradeType === 'DIGITMATCH' || tradeType === 'DIGITDIFF' ? (
                                    <div className='smart-trader__row smart-trader__row--two'>
                                        <div className='smart-trader__field'>
                                            <label htmlFor='st-md-pred'>
                                                {localize('Match/Diff prediction digit')}
                                            </label>
                                            <input
                                                id='st-md-pred'
                                                type='number'
                                                min={0}
                                                max={9}
                                                value={mdPrediction}
                                                onChange={e => {
                                                    const v = Math.max(0, Math.min(9, Number(e.target.value)));
                                                    setMdPrediction(v);
                                                }}
                                            />
                                        </div>
                                        <div className='smart-trader__field'>
                                            <label htmlFor='st-martingale'>{localize('Martingale multiplier')}</label>
                                            <input
                                                id='st-martingale'
                                                type='number'
                                                min={1}
                                                step='0.1'
                                                value={martingaleMultiplier}
                                                onChange={e =>
                                                    setMartingaleMultiplier(Math.max(1, Number(e.target.value)))
                                                }
                                            />
                                        </div>
                                    </div>
                                ) : (
                                    <div className='smart-trader__row smart-trader__row--compact'>
                                        <div className='smart-trader__field'>
                                            <label htmlFor='st-ou-pred-pre'>
                                                {localize('Over/Under prediction (pre-loss)')}
                                            </label>
                                            <input
                                                id='st-ou-pred-pre'
                                                type='number'
                                                min={0}
                                                max={9}
                                                value={ouPredPreLoss}
                                                onChange={e =>
                                                    setOuPredPreLoss(Math.max(0, Math.min(9, Number(e.target.value))))
                                                }
                                            />
                                        </div>
                                        <div className='smart-trader__field'>
                                            <label htmlFor='st-ou-pred-post'>
                                                {localize('Over/Under prediction (after loss)')}
                                            </label>
                                            <input
                                                id='st-ou-pred-post'
                                                type='number'
                                                min={0}
                                                max={9}
                                                value={ouPredPostLoss}
                                                onChange={e =>
                                                    setOuPredPostLoss(Math.max(0, Math.min(9, Number(e.target.value))))
                                                }
                                            />
                                        </div>
                                        <div className='smart-trader__field'>
                                            <label htmlFor='st-martingale'>{localize('Martingale multiplier')}</label>
                                            <input
                                                id='st-martingale'
                                                type='number'
                                                min={1}
                                                step='0.1'
                                                value={martingaleMultiplier}
                                                onChange={e =>
                                                    setMartingaleMultiplier(Math.max(1, Number(e.target.value)))
                                                }
                                            />
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        <div className='smart-trader__digits'>
                            {digits.map((d, idx) => (
                                <div
                                    key={`${idx}-${d}`}
                                    className={`smart-trader__digit ${d === lastDigit ? 'is-current' : ''} ${getHintClass(d)}`}
                                >
                                    {d}
                                </div>
                            ))}
                        </div>
                        <div className='smart-trader__footer-bar'>
                            <div className='smart-trader__footer-item'>
                                {localize('Total Profit/Loss:')} {Number(store?.summary_card?.profit || 0).toFixed(2)}
                            </div>
                            <div className='smart-trader__footer-item'>
                                {localize('Last Digit:')} {lastDigit ?? '-'}
                            </div>
                            <div className='smart-trader__footer-item'>
                                {localize('Consecutive Wins:')} {consecWins} {localize('Consecutive Losses:')}{' '}
                                {consecLosses}
                            </div>
                        </div>

                        <div className='smart-trader__cta'>
                            <button
                                className='smart-trader__cta-once'
                                onClick={() => onRun()}
                                disabled={is_running || !symbol}
                            >
                                {localize('Trade once')}
                            </button>
                            <button className='smart-trader__cta-auto' onClick={onRun} disabled={is_running || !symbol}>
                                {localize('Start auto trading')}
                            </button>
                        </div>

                        {status && (
                            <div className='smart-trader__status'>
                                <Text size='xs' color={/error|fail/i.test(status) ? 'loss-danger' : 'prominent'}>
                                    {status}
                                </Text>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
});

export default SmartTrader;
