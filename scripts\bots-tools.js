#!/usr/bin/env node

/**
 * Bots Management CLI
 * Manage domain-specific bots manifests and files under public/xml
 */

const fs = require('fs');
const path = require('path');

const ROOT = process.cwd();
const XML_DIR = path.join(ROOT, 'public', 'xml');

// Domains from config/domain_app_ids (www.* variants are handled at runtime by stripping the prefix)
const DOMAINS = [
    'tradermaster.site',
    'promoclub.site',
    'gletraders.site',
    'd-analysis.site',
    'kingstraders.site',
    'wallacetraders.site',
    'legoo.site',
    'dbotprinters.site',
    'kenyanhennessy.site',
    'masterhunter.site',
    'tytotradinghub.site',
    'swiftpro.site',
    'developmentviewport.netlify.app',
];

function ensureDir(p) {
    if (!fs.existsSync(p)) fs.mkdirSync(p, { recursive: true });
}

function readJSON(p, fallback) {
    if (!fs.existsSync(p)) return fallback;
    try {
        return JSON.parse(fs.readFileSync(p, 'utf8'));
    } catch (e) {
        return fallback;
    }
}

function writeJSON(p, data) {
    fs.writeFileSync(p, JSON.stringify(data, null, 2));
}

function list() {
    console.log('📦 Domain Bots Overview');
    console.log('────────────────────────');
    DOMAINS.forEach(domain => {
        const dir = path.join(XML_DIR, domain);
        const manifest = path.join(dir, 'bots.json');
        const hasDir = fs.existsSync(dir);
        const bots = readJSON(manifest, []);
        console.log(`• ${domain}: ${hasDir ? 'folder ✔' : 'folder ✖'} | bots: ${bots.length}`);
    });
}

function init() {
    ensureDir(XML_DIR);
    let created = 0;
    DOMAINS.forEach(domain => {
        const dir = path.join(XML_DIR, domain);
        const manifest = path.join(dir, 'bots.json');
        if (!fs.existsSync(dir)) {
            ensureDir(dir);
            created++;
        }
        if (!fs.existsSync(manifest)) {
            writeJSON(manifest, []);
        }
    });
    console.log(`✅ Init complete. Created ${created} new domain folders. All manifests ensured.`);
}

function generateDefault() {
    // Generate default manifest from available XMLs in public/xml (top-level)
    const files = fs.readdirSync(XML_DIR).filter(f => f.toLowerCase().endsWith('.xml'));
    const manifest = files.map(file => ({ name: path.basename(file, '.xml'), file }));
    writeJSON(path.join(XML_DIR, 'bots.json'), manifest);
    console.log(`✅ Generated default bots.json with ${manifest.length} entries from top-level XMLs.`);
}

function add(domain, filePath, displayName) {
    if (!DOMAINS.includes(domain)) {
        console.error(`❌ Unknown domain: ${domain}`);
        process.exit(1);
    }
    const dir = path.join(XML_DIR, domain);
    ensureDir(dir);
    const filename = path.basename(filePath);
    const target = path.join(dir, filename);

    // Copy the XML into domain folder if not already there
    if (fs.existsSync(filePath)) {
        fs.copyFileSync(filePath, target);
    } else {
        // If path is relative to XML_DIR
        const local = path.join(XML_DIR, filename);
        if (fs.existsSync(local)) {
            fs.copyFileSync(local, target);
        } else {
            console.error(`❌ File not found: ${filePath}`);
            process.exit(1);
        }
    }

    // Update manifest
    const manifestPath = path.join(dir, 'bots.json');
    const manifest = readJSON(manifestPath, []);
    if (!manifest.some(b => b.file === filename)) {
        manifest.push({ name: displayName || path.basename(filename, '.xml'), file: filename });
        writeJSON(manifestPath, manifest);
        console.log(`✅ Added bot to ${domain}: ${displayName || filename}`);
    } else {
        console.log(`ℹ️  Bot already listed in ${domain}: ${filename}`);
    }
}

function clearDomain(domain) {
    if (!DOMAINS.includes(domain)) {
        console.error(`❌ Unknown domain: ${domain}`);
        process.exit(1);
    }
    const dir = path.join(XML_DIR, domain);
    ensureDir(dir);
    // Remove XML files in the domain folder
    if (fs.existsSync(dir)) {
        const files = fs.readdirSync(dir);
        files.forEach(f => {
            const fp = path.join(dir, f);
            if (fs.statSync(fp).isFile() && f.toLowerCase().endsWith('.xml')) {
                fs.unlinkSync(fp);
            }
        });
    }
    // Reset manifest
    const manifestPath = path.join(dir, 'bots.json');
    writeJSON(manifestPath, []);
    console.log(`🧹 Cleared bots for ${domain}`);
}

function clearAll() {
    DOMAINS.forEach(clearDomain);
    console.log('✅ Cleared bots for all domains.');
}

function syncManifests() {
    console.log('🔄 Syncing manifests with XML files...');
    let updated = 0;

    DOMAINS.forEach(domain => {
        const dir = path.join(XML_DIR, domain);
        const manifestPath = path.join(dir, 'bots.json');

        if (!fs.existsSync(dir)) return;

        // Get all XML files in the domain directory
        const xmlFiles = fs
            .readdirSync(dir)
            .filter(f => f.toLowerCase().endsWith('.xml'))
            .sort();

        if (xmlFiles.length === 0) return;

        // Read current manifest
        const currentManifest = readJSON(manifestPath, []);
        const currentFiles = new Set(currentManifest.map(b => b.file));

        // Create new manifest entries for missing XML files
        const newEntries = xmlFiles
            .filter(file => !currentFiles.has(file))
            .map(file => ({
                // Name must be exact filename per requirement
                name: file,
                file: file,
            }));

        if (newEntries.length > 0) {
            const updatedManifest = [...currentManifest, ...newEntries];
            writeJSON(manifestPath, updatedManifest);
            console.log(`  ✅ ${domain}: Added ${newEntries.length} bots to manifest`);
            updated++;
        } else {
            console.log(`  ℹ️  ${domain}: Manifest already up to date`);
        }
    });

    console.log(`🎉 Sync complete. Updated ${updated} domain manifests.`);
}

function help() {
    console.log(`Bots Management CLI

Usage:
  node scripts/bots-tools.js init                   Ensure folders and empty manifests for all domains
  node scripts/bots-tools.js list                   List domains and bot counts
  node scripts/bots-tools.js generate-default       Build public/xml/bots.json from top-level XMLs
  node scripts/bots-tools.js sync                   Sync manifests with existing XML files in domain folders
  node scripts/bots-tools.js add <domain> <file> [name]  Add a bot XML to a domain and update manifest
  node scripts/bots-tools.js clear-all               Remove all domain bots and reset manifests
  node scripts/bots-tools.js clear <domain>          Remove all bots for a single domain

Examples:
  node scripts/bots-tools.js init
  node scripts/bots-tools.js list
  node scripts/bots-tools.js sync
  node scripts/bots-tools.js add legoo.site "public/xml/legoospeedbot.xml" "Legoo Speed Bot"
  node scripts/bots-tools.js add promoclub.site "public/xml/TC Bot 1.1.xml" "TC Bot 1.1"
`);
}

const [, , cmd, ...args] = process.argv;

switch (cmd) {
    case 'list':
        list();
        break;
    case 'init':
        init();
        break;
    case 'generate-default':
        generateDefault();
        break;
    case 'sync':
        syncManifests();
        break;
    case 'add':
        add(args[0], args[1], args[2]);
        break;
    case 'clear-all':
        clearAll();
        break;
    case 'clear':
        clearDomain(args[0]);
        break;
    default:
        help();
}
