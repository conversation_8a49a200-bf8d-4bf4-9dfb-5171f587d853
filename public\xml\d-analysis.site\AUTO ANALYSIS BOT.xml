<xml xmlns="http://www.w3.org/1999/xhtml" collection="false">
  <variables>
    <variable type="" id="K4Af%s#5YtdmP~mgXUZ[">__inputTickNumber</variable>
    <variable type="" id="DXXuZObE_I:d(su=a=4,">__inputTickList</variable>
    <variable type="" id=".*P?OVjsooZJSxF$d`5H">__inputNeededLength</variable>
    <variable type="" id="VP/k^|S)8?7[So3jO$G6">__inputListToDetermineMaxAndMin</variable>
    <variable type="" id="C#L6A2g4X]W#7~3$tsTO">SHXMMY</variable>
    <variable type="" id="MH1i7DED25hPWh[:OiYQ">__stringValueOfTheTick</variable>
    <variable type="" id="O.@0Cs.c},T68x-]w]wt">__stringLastDigit</variable>
    <variable type="" id="a1Y]8G9rw4qBh^GU1wF,">LastDigitStatsList</variable>
    <variable type="" id="F0f:u:apL@r?C6a`(878">MaxInTheLastDigitStats</variable>
    <variable type="" id="_w6Z!ZDm$W8UX8wv4+]/">ticks</variable>
    <variable type="" id=",v`b(KjR9Bzv7hP03x[I">stake</variable>
    <variable type="" id="8)`M1B-n]Fjx|utsb-6+">BRANNY KE</variable>
    <variable type="" id="j52H4Nz#zmWBfbncQqXu">__delemiterPosition</variable>
    <variable type="" id="@zooM()x78^W5#^!659p">__chunkedList</variable>
    <variable type="" id="R#L7L=Cf{`Q;]yq5({`]">fractionPartLength</variable>
    <variable type="" id="S=xTw3TcL_@WcEtyE{Tt">MinInTheLastDigitStats</variable>
    <variable type="" id="Lt*{a`|.Tb%wL^5iz?jx">Take Profit</variable>
    <variable type="" id="Y1JG^y5Y@B`XTnwro,vH">__fractionPart</variable>
    <variable type="" id="Y7AZ/PW`e~/(j*}?zclu">i</variable>
    <variable type="" id=";,D?u9:B;4DnuF2|eg8M">number of ticks</variable>
    <variable type="" id="y|FpW3?-:Yg%z!CW-2a:">fractionDelemiter</variable>
    <variable type="" id="V~Gk-8@zjklU:9}9HGlf">j</variable>
    <variable type="" id="2pYqfcD38xOAD-dR0@Hs">Stop loss</variable>
    <variable type="" id="%?V?F3cF%S]*:Ui45YJR">__lastDigitOfTheTick</variable>
    <variable type="" id="BR__M7#?v;3wMK#tO/Dw">amountOfTicksToCheck</variable>
    <variable type="" id="k!;8Kf1h2|{!8DrQFE2`">__statsElement</variable>
    <variable type="" id="t^~L#g{=[+g@JOk_I_oM">MaxStatsIdx</variable>
    <variable type="" id="W5O+hPKIq4x)v|_)$`8A">MinStatsIdx</variable>
    <variable type="" id="z;t%;E`Xqj#?hJ$4ZX+[">Martingale Factor</variable>
    <variable type="" id="ej?}H%~K`Saq-#Gvy.w`">win stake</variable>
  </variables>
  <block type="trade" id="xgH69|xFn9=70w.*3Vo@" x="0" y="0">
    <field name="MARKET_LIST">synthetic_index</field>
    <field name="SUBMARKET_LIST">random_index</field>
    <field name="SYMBOL_LIST">R_100</field>
    <field name="TRADETYPECAT_LIST">digits</field>
    <field name="TRADETYPE_LIST">evenodd</field>
    <field name="TYPE_LIST">both</field>
    <field name="CANDLEINTERVAL_LIST">60</field>
    <field name="TIME_MACHINE_ENABLED">FALSE</field>
    <field name="RESTARTONERROR">TRUE</field>
    <statement name="INITIALIZATION">
      <block type="variables_set" id="1:I[/R+9ttEW,U)Xyp41" collapsed="true">
        <field name="VAR" id="C#L6A2g4X]W#7~3$tsTO" variabletype="">SHXMMY</field>
        <value name="VALUE">
          <block type="text" id="(tOn5lFa%%sA2jIx}ZSY" collapsed="true">
            <field name="TEXT">BBA</field>
          </block>
        </value>
        <next>
          <block type="variables_set" id="xBVrk+n$%-waVFW_vTKI" collapsed="true">
            <field name="VAR" id="8)`M1B-n]Fjx|utsb-6+" variabletype="">BRANNY KE</field>
            <value name="VALUE">
              <block type="logic_boolean" id="7-*vbxb(.)e^_K0Hn.cE" collapsed="true">
                <field name="BOOL">TRUE</field>
              </block>
            </value>
            <next>
              <block type="variables_set" id="2^fHNaNfkFT8odCn_Oeh">
                <field name="VAR" id="Lt*{a`|.Tb%wL^5iz?jx" variabletype="">Take Profit</field>
                <value name="VALUE">
                  <block type="math_number" id="$.BWyn7|ObQA8H!0Ha#P">
                    <field name="NUM">5</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="!56-x*J$mO%fw9lX1o0K">
                    <field name="VAR" id="2pYqfcD38xOAD-dR0@Hs" variabletype="">Stop loss</field>
                    <value name="VALUE">
                      <block type="math_number" id="|n4[9D~q@SyGmljMCs`@">
                        <field name="NUM">50</field>
                      </block>
                    </value>
                    <next>
                      <block type="variables_set" id="@kH57Ut?2Qr$nA@%[#hZ">
                        <field name="VAR" id=",v`b(KjR9Bzv7hP03x[I" variabletype="">stake</field>
                        <value name="VALUE">
                          <block type="math_number" id="2}-44|Dwh:srml|VSFZt">
                            <field name="NUM">1</field>
                          </block>
                        </value>
                        <next>
                          <block type="variables_set" id="YO,f!7V?u{W|%IrJRZJm">
                            <field name="VAR" id="z;t%;E`Xqj#?hJ$4ZX+[" variabletype="">Martingale Factor</field>
                            <value name="VALUE">
                              <block type="math_number" id=")8oIvSoy5;8yYxKE!w[+">
                                <field name="NUM">2</field>
                              </block>
                            </value>
                            <next>
                              <block type="variables_set" id="Zta^v8_%/,R3meayGNBc" collapsed="true">
                                <field name="VAR" id="ej?}H%~K`Saq-#Gvy.w`" variabletype="">win stake</field>
                                <value name="VALUE">
                                  <block type="variables_get" id="uNW/zPE/_^:{:y.[o%b1">
                                    <field name="VAR" id=",v`b(KjR9Bzv7hP03x[I" variabletype="">stake</field>
                                  </block>
                                </value>
                                <next>
                                  <block type="variables_set" id="l{7MTOg.t=odps!LkkAS" collapsed="true">
                                    <field name="VAR" id="_w6Z!ZDm$W8UX8wv4+]/" variabletype="">ticks</field>
                                    <value name="VALUE">
                                      <block type="math_number" id="OvS[V==!7Z:(d3@^6,%~">
                                        <field name="NUM">1</field>
                                      </block>
                                    </value>
                                    <next>
                                      <block type="variables_set" id="zw921b?,pn@R6KFm[T4F" collapsed="true">
                                        <field name="VAR" id=";,D?u9:B;4DnuF2|eg8M" variabletype="">number of ticks</field>
                                        <value name="VALUE">
                                          <block type="math_number" id="DMQIN1b]k=.[=r0+T}XH">
                                            <field name="NUM">50</field>
                                          </block>
                                        </value>
                                        <next>
                                          <block type="procedures_callnoreturn" id="r1_b#PD/bua;T)DaDOIP">
                                            <mutation name="initVars"></mutation>
                                          </block>
                                        </next>
                                      </block>
                                    </next>
                                  </block>
                                </next>
                              </block>
                            </next>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="SUBMARKET">
      <block type="tradeOptions" id="x=V33~4Lb|(sLv`J[:Eb">
        <field name="DURATIONTYPE_LIST">t</field>
        <field name="CURRENCY_LIST">USD</field>
        <field name="BARRIEROFFSETTYPE_LIST">+</field>
        <field name="SECONDBARRIEROFFSETTYPE_LIST">-</field>
        <value name="DURATION">
          <shadow type="math_number" id="O*@58sDc=!cOO}*b2vf9">
            <field name="NUM">5</field>
          </shadow>
          <block type="variables_get" id="fSL;85sd*J(~72f+,p*2">
            <field name="VAR" id="_w6Z!ZDm$W8UX8wv4+]/" variabletype="">ticks</field>
          </block>
        </value>
        <value name="AMOUNT">
          <shadow type="math_number" id="ml)25~7^q}3I9}vjf:%K">
            <field name="NUM">1</field>
          </shadow>
          <block type="variables_get" id="uN5,U-QkaPEmn}BDx?dW">
            <field name="VAR" id=",v`b(KjR9Bzv7hP03x[I" variabletype="">stake</field>
          </block>
        </value>
      </block>
    </statement>
  </block>
  <block type="before_purchase" id="i-CIx.(Onm4?ihxzA}Y]" collapsed="true" x="0" y="676">
    <statement name="BEFOREPURCHASE_STACK">
      <block type="controls_if" id="s=R+yn#7hbDksn8R/fg1" collapsed="true">
        <value name="IF0">
          <block type="logic_compare" id="EWi2ptF]aGmv|k)=KZjd" inline="false" collapsed="true">
            <field name="OP">EQ</field>
            <value name="A">
              <block type="variables_get" id="|b}8{$e:0!~507x!@?#~" collapsed="true">
                <field name="VAR" id="8)`M1B-n]Fjx|utsb-6+" variabletype="">BRANNY KE</field>
              </block>
            </value>
            <value name="B">
              <block type="logic_boolean" id="r/VCg}EaBF?Ey-:+WM4#" collapsed="true">
                <field name="BOOL">TRUE</field>
              </block>
            </value>
          </block>
        </value>
        <statement name="DO0">
          <block type="controls_if" id="g5dKr-KH^,D}g0_94P.M" collapsed="true">
            <mutation elseif="1"></mutation>
            <value name="IF0">
              <block type="logic_operation" id="$hT{ZoCtdY=`v-qMMG^w" inline="false" collapsed="true">
                <field name="OP">AND</field>
                <value name="A">
                  <block type="math_number_property" id=":z))jXiO[L}eWjN1u2R|" collapsed="true">
                    <mutation divisor_input="false"></mutation>
                    <field name="PROPERTY">EVEN</field>
                    <value name="NUMBER_TO_CHECK">
                      <shadow type="math_number" id="!VAMduRUl+1df9Y%=#3_">
                        <field name="NUM">0</field>
                      </shadow>
                      <block type="variables_get" id="yUlN|9Ow!cjz=?{+[]m7" collapsed="true">
                        <field name="VAR" id="t^~L#g{=[+g@JOk_I_oM" variabletype="">MaxStatsIdx</field>
                      </block>
                    </value>
                  </block>
                </value>
                <value name="B">
                  <block type="math_number_property" id="fZT0+iGm$X4b+ravUm~V" inline="false" collapsed="true">
                    <mutation divisor_input="false"></mutation>
                    <field name="PROPERTY">EVEN</field>
                    <value name="NUMBER_TO_CHECK">
                      <shadow type="math_number" id="!VAMduRUl+1df9Y%=#3_">
                        <field name="NUM">0</field>
                      </shadow>
                      <block type="variables_get" id="3C)lD^%rbK,YTBfa#7^`" collapsed="true">
                        <field name="VAR" id="W5O+hPKIq4x)v|_)$`8A" variabletype="">MinStatsIdx</field>
                      </block>
                    </value>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO0">
              <block type="purchase" id="71ng`,UR8+y2HxSQEyV@">
                <field name="PURCHASE_LIST">DIGITEVEN</field>
              </block>
            </statement>
            <value name="IF1">
              <block type="logic_operation" id="y(~S0y!JA+K956TY;a!g" inline="false" collapsed="true">
                <field name="OP">AND</field>
                <value name="A">
                  <block type="math_number_property" id="?W0)LggsMvo%I5,,9O14" collapsed="true">
                    <mutation divisor_input="false"></mutation>
                    <field name="PROPERTY">ODD</field>
                    <value name="NUMBER_TO_CHECK">
                      <shadow type="math_number" id="!VAMduRUl+1df9Y%=#3_">
                        <field name="NUM">0</field>
                      </shadow>
                      <block type="variables_get" id="/URG]S_sY?i2jCyid0x4" collapsed="true">
                        <field name="VAR" id="t^~L#g{=[+g@JOk_I_oM" variabletype="">MaxStatsIdx</field>
                      </block>
                    </value>
                  </block>
                </value>
                <value name="B">
                  <block type="math_number_property" id="^?gl/b%AL]b}=j#Q*8]o" inline="false" collapsed="true">
                    <mutation divisor_input="false"></mutation>
                    <field name="PROPERTY">ODD</field>
                    <value name="NUMBER_TO_CHECK">
                      <shadow type="math_number" id="!VAMduRUl+1df9Y%=#3_">
                        <field name="NUM">0</field>
                      </shadow>
                      <block type="variables_get" id="G6rIJN#e0USldAzus];|" collapsed="true">
                        <field name="VAR" id="W5O+hPKIq4x)v|_)$`8A" variabletype="">MinStatsIdx</field>
                      </block>
                    </value>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO1">
              <block type="purchase" id="*@T}[unf!qf/lqVcY)ZL">
                <field name="PURCHASE_LIST">DIGITODD</field>
              </block>
            </statement>
          </block>
        </statement>
      </block>
    </statement>
  </block>
  <block type="after_purchase" id="D^Jz1^n=2vtZku1vBN@;" collapsed="true" x="5" y="806">
    <statement name="AFTERPURCHASE_STACK">
      <block type="controls_if" id="a(w-r6IXZfd)$0mW:+gr" collapsed="true">
        <mutation else="1"></mutation>
        <value name="IF0">
          <block type="contract_check_result" id="}0[P/U/b_#,Wz+s)#@YQ" collapsed="true">
            <field name="CHECK_RESULT">loss</field>
          </block>
        </value>
        <statement name="DO0">
          <block type="variables_set" id="$(oS?b,9VF-z--Cx=.aG" collapsed="true">
            <field name="VAR" id="8)`M1B-n]Fjx|utsb-6+" variabletype="">BRANNY KE</field>
            <value name="VALUE">
              <block type="logic_boolean" id="0k%%cKACH,[cQV|H#`bN" collapsed="true">
                <field name="BOOL">TRUE</field>
              </block>
            </value>
            <next>
              <block type="variables_set" id="s;xED/HYs?<EMAIL>" collapsed="true">
                <field name="VAR" id="C#L6A2g4X]W#7~3$tsTO" variabletype="">SHXMMY</field>
                <value name="VALUE">
                  <block type="logic_boolean" id="4RE`;JB5wPLvT1p*EO;2">
                    <field name="BOOL">FALSE</field>
                  </block>
                </value>
                <next>
                  <block type="controls_if" id="(/UKbSe6)!5ip/V*4ZyQ" collapsed="true">
                    <value name="IF0">
                      <block type="logic_compare" id="[ro^5!;zQWT}B0LFV1=B" inline="false" collapsed="true">
                        <field name="OP">EQ</field>
                        <value name="A">
                          <block type="variables_get" id=":c-K@hPR!I_v4m/t5WZj" collapsed="true">
                            <field name="VAR" id="8)`M1B-n]Fjx|utsb-6+" variabletype="">BRANNY KE</field>
                          </block>
                        </value>
                        <value name="B">
                          <block type="logic_boolean" id="KXz5ghgiKz2~RYq2XtVD" collapsed="true">
                            <field name="BOOL">TRUE</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="controls_if" id="./5*:Di[t;eXR~1-q5^8" collapsed="true">
                        <mutation else="1"></mutation>
                        <value name="IF0">
                          <block type="logic_compare" id=";+2d0hjB/sj/q#92T[,P" inline="false" collapsed="true">
                            <field name="OP">EQ</field>
                            <value name="A">
                              <block type="variables_get" id="k)l[MrEjh9g/6?)JkwGb">
                                <field name="VAR" id="C#L6A2g4X]W#7~3$tsTO" variabletype="">SHXMMY</field>
                              </block>
                            </value>
                            <value name="B">
                              <block type="text" id="[k9b6~vnd,3qL6E?kY{A" collapsed="true">
                                <field name="TEXT">BBA</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <statement name="DO0">
                          <block type="variables_set" id="|pFT4^.lK`Ya;Qg2t;-D" collapsed="true">
                            <field name="VAR" id="C#L6A2g4X]W#7~3$tsTO" variabletype="">SHXMMY</field>
                            <value name="VALUE">
                              <block type="text" id="uF)UE%mh5L93o-rdv-E?" collapsed="true">
                                <field name="TEXT">A</field>
                              </block>
                            </value>
                          </block>
                        </statement>
                        <statement name="ELSE">
                          <block type="variables_set" id="5e;O~MKIQs4Ooi6c#Dh%" collapsed="true">
                            <field name="VAR" id="C#L6A2g4X]W#7~3$tsTO" variabletype="">SHXMMY</field>
                            <value name="VALUE">
                              <block type="text" id="?rV[`40Y^_Ka~;x^40bN" collapsed="true">
                                <field name="TEXT">BBA</field>
                              </block>
                            </value>
                          </block>
                        </statement>
                        <next>
                          <block type="notify" id="zPyiA~PNE(1xnq_U0ttm" collapsed="true">
                            <field name="NOTIFICATION_TYPE">error</field>
                            <field name="NOTIFICATION_SOUND">silent</field>
                            <value name="MESSAGE">
                              <shadow type="text" id="sGc_98E-EZ3z/wpE#[JF">
                                <field name="TEXT">abc</field>
                              </shadow>
                              <block type="text_join" id=")ep+{$NT|wmY-1wU_9ZR" collapsed="true">
                                <mutation items="3"></mutation>
                                <value name="ADD0">
                                  <block type="text" id="+sD(xHe3UGjW4qn%{x?." collapsed="true">
                                    <field name="TEXT">LOSS | </field>
                                  </block>
                                </value>
                                <value name="ADD1">
                                  <block type="read_details" id="}{k~~#w0EJfE!*0{`iut" collapsed="true">
                                    <field name="DETAIL_INDEX">4</field>
                                  </block>
                                </value>
                                <value name="ADD2">
                                  <block type="text" id="9=*NglPi!s]QNBgrjl3E" collapsed="true">
                                    <field name="TEXT">  ($)</field>
                                  </block>
                                </value>
                              </block>
                            </value>
                            <next>
                              <block type="variables_set" id="E0AKP*JS%lw))CH]ZW*I" collapsed="true">
                                <field name="VAR" id=",v`b(KjR9Bzv7hP03x[I" variabletype="">stake</field>
                                <value name="VALUE">
                                  <block type="math_arithmetic" id="rCs2iQAwXl04]CWHx?3@" collapsed="true">
                                    <field name="OP">MULTIPLY</field>
                                    <value name="A">
                                      <shadow type="math_number" id="R^[#Y/~Ql8u,lQy5Z#.3">
                                        <field name="NUM">1</field>
                                      </shadow>
                                      <block type="variables_get" id="NrOL,jrq0~Y)f;}5Hi(m">
                                        <field name="VAR" id=",v`b(KjR9Bzv7hP03x[I" variabletype="">stake</field>
                                      </block>
                                    </value>
                                    <value name="B">
                                      <shadow type="math_number" id="OJnR_]MPc08}xb~*Sk}=">
                                        <field name="NUM">1</field>
                                      </shadow>
                                      <block type="variables_get" id="hXDU@SXiO=H4,];rsp+o">
                                        <field name="VAR" id="z;t%;E`Xqj#?hJ$4ZX+[" variabletype="">Martingale Factor</field>
                                      </block>
                                    </value>
                                  </block>
                                </value>
                              </block>
                            </next>
                          </block>
                        </next>
                      </block>
                    </statement>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </statement>
        <statement name="ELSE">
          <block type="variables_set" id="wb.bJ$q0iv_{Jixq{kw@" collapsed="true">
            <field name="VAR" id="8)`M1B-n]Fjx|utsb-6+" variabletype="">BRANNY KE</field>
            <value name="VALUE">
              <block type="logic_boolean" id="uit+x[TpK(KlFhqicQLV" collapsed="true">
                <field name="BOOL">TRUE</field>
              </block>
            </value>
            <next>
              <block type="variables_set" id="=9ir+FPvx_.g9uTT`F34" collapsed="true">
                <field name="VAR" id="C#L6A2g4X]W#7~3$tsTO" variabletype="">SHXMMY</field>
                <value name="VALUE">
                  <block type="logic_boolean" id="6+D%o{-HY=B(AR#laIEQ">
                    <field name="BOOL">TRUE</field>
                  </block>
                </value>
                <next>
                  <block type="controls_if" id=",6^66Xm]4Sdrt(A=a/+~" collapsed="true">
                    <value name="IF0">
                      <block type="logic_compare" id="q33Sbsn.Tf#L((o9)M{y" inline="false" collapsed="true">
                        <field name="OP">EQ</field>
                        <value name="A">
                          <block type="variables_get" id="_Z]7EJ#K!q^}T[L(J7,s" collapsed="true">
                            <field name="VAR" id="8)`M1B-n]Fjx|utsb-6+" variabletype="">BRANNY KE</field>
                          </block>
                        </value>
                        <value name="B">
                          <block type="logic_boolean" id="X8*3_2aLYlS|%qY6tQzJ" collapsed="true">
                            <field name="BOOL">TRUE</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="notify" id="+],+[{$8T;ZT4$.%E~OL" collapsed="true">
                        <field name="NOTIFICATION_TYPE">success</field>
                        <field name="NOTIFICATION_SOUND">silent</field>
                        <value name="MESSAGE">
                          <shadow type="text" id="sGc_98E-EZ3z/wpE#[JF">
                            <field name="TEXT">abc</field>
                          </shadow>
                          <block type="text_join" id="q?gK9{,ZmP$|$JF]Iwbu" collapsed="true">
                            <mutation items="3"></mutation>
                            <value name="ADD0">
                              <block type="text" id="O=M](:?t8NFGRa)/pZFy" collapsed="true">
                                <field name="TEXT">WON | </field>
                              </block>
                            </value>
                            <value name="ADD1">
                              <block type="read_details" id="m[V[?#Sefixj0`pX1j-t" collapsed="true">
                                <field name="DETAIL_INDEX">4</field>
                              </block>
                            </value>
                            <value name="ADD2">
                              <block type="text" id="GuGL]D?R;+Ikr=_/Rm|:" collapsed="true">
                                <field name="TEXT">  ($)</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <next>
                          <block type="variables_set" id="V9}Jq0:5TrU5-Y4]@.vE" collapsed="true">
                            <field name="VAR" id=",v`b(KjR9Bzv7hP03x[I" variabletype="">stake</field>
                            <value name="VALUE">
                              <block type="variables_get" id="]pgMWI7[O+%Y3|;dgOZK">
                                <field name="VAR" id="ej?}H%~K`Saq-#Gvy.w`" variabletype="">win stake</field>
                              </block>
                            </value>
                          </block>
                        </next>
                      </block>
                    </statement>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </statement>
        <next>
          <block type="controls_if" id="O:oE+b`uYd^lWMV6oewN" collapsed="true">
            <mutation else="1"></mutation>
            <value name="IF0">
              <block type="logic_compare" id="u7Nh(8l071Jc,o$HuZ~y" inline="false" collapsed="true">
                <field name="OP">LT</field>
                <value name="A">
                  <block type="total_profit" id="F^[b`m)]*slR]rCrd|af"></block>
                </value>
                <value name="B">
                  <block type="variables_get" id="nRb5o`=3^xbo?97fjk_I" collapsed="true">
                    <field name="VAR" id="Lt*{a`|.Tb%wL^5iz?jx" variabletype="">Take Profit</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO0">
              <block type="controls_if" id="}V-gz*5i`j4,X6PQY]%)" collapsed="true">
                <mutation else="1"></mutation>
                <value name="IF0">
                  <block type="logic_operation" id="tbI@2y_TcIF[hYtO`SwP" inline="false" collapsed="true">
                    <field name="OP">AND</field>
                    <value name="A">
                      <block type="math_number_property" id="u)+Prc?-1JM|c4js^R)7" inline="false">
                        <mutation divisor_input="false"></mutation>
                        <field name="PROPERTY">NEGATIVE</field>
                        <value name="NUMBER_TO_CHECK">
                          <shadow type="math_number" id="U%)H@tCw6Yvh8*(iY-Oh">
                            <field name="NUM">0</field>
                          </shadow>
                          <block type="total_profit" id="50gMtKT)mqp~$l}z:kZ8"></block>
                        </value>
                      </block>
                    </value>
                    <value name="B">
                      <block type="logic_compare" id="0ild6(--9DpM=/Pky2Ym" inline="false" collapsed="true">
                        <field name="OP">GT</field>
                        <value name="A">
                          <block type="math_single" id="V*.(iVzJO!PdsfW|W_-R">
                            <field name="OP">ABS</field>
                            <value name="NUM">
                              <shadow type="math_number" id=".}WEO+vWOR5BY(`XwkCP">
                                <field name="NUM">9</field>
                              </shadow>
                              <block type="total_profit" id="^BT43iE7Q/p,8L|Woj9@"></block>
                            </value>
                          </block>
                        </value>
                        <value name="B">
                          <block type="variables_get" id="ss5Zcebv6bN#xsVm!#Y)">
                            <field name="VAR" id="2pYqfcD38xOAD-dR0@Hs" variabletype="">Stop loss</field>
                          </block>
                        </value>
                      </block>
                    </value>
                  </block>
                </value>
                <statement name="DO0">
                  <block type="text_print" id="ifZK4ORl:5TlQy2/Hg3t" collapsed="true">
                    <value name="TEXT">
                      <shadow type="text" id="zkV73Au%~2{?7Rgrv]X/">
                        <field name="TEXT">STOP LOSS</field>
                      </shadow>
                      <block type="text_join" id="3xDR%!!,!r$y*Eh0usLt" collapsed="true">
                        <mutation items="3"></mutation>
                        <value name="ADD0">
                          <block type="text" id="N`QAe,-`4?58?i8@AUP(" collapsed="true">
                            <field name="TEXT">OOpps, Stop loss hit</field>
                          </block>
                        </value>
                        <value name="ADD1">
                          <block type="total_profit" id="gCI,@armG!}tbkxq@TWi"></block>
                        </value>
                        <value name="ADD2">
                          <block type="text" id="7aIZrH-N/fx2:pwNhj[+" collapsed="true">
                            <field name="TEXT">  ($)  Follow me @BRANNY_KE on all platforms +254 706 338 765 [HGA]</field>
                          </block>
                        </value>
                      </block>
                    </value>
                  </block>
                </statement>
                <statement name="ELSE">
                  <block type="trade_again" id="c)S|KjoQdXc8l#J@5z3P"></block>
                </statement>
              </block>
            </statement>
            <statement name="ELSE">
              <block type="text_print" id="(ll/P,rq;oN,%4KVEh9}" collapsed="true">
                <value name="TEXT">
                  <shadow type="text" id="_T9eNMY*Zi};76^FR|F|">
                    <field name="TEXT">MISSION COMPLETE</field>
                  </shadow>
                  <block type="text_join" id="3=GH{Uz$9$3}I/G;VbNX" collapsed="true">
                    <mutation items="3"></mutation>
                    <value name="ADD0">
                      <block type="text" id="5t8.ly:P$6=jF2W5jeg]" collapsed="true">
                        <field name="TEXT">Take Profit </field>
                      </block>
                    </value>
                    <value name="ADD1">
                      <block type="total_profit" id="DFr7-e/vO9O2_Fyr/-eu"></block>
                    </value>
                    <value name="ADD2">
                      <block type="text" id="hKCm$-#=n~j!%lXdDs5k" collapsed="true">
                        <field name="TEXT">  ($)  Follow me @BRANNY_KE on all platforms +254 706 338 765 [HGA]</field>
                      </block>
                    </value>
                  </block>
                </value>
              </block>
            </statement>
          </block>
        </next>
      </block>
    </statement>
  </block>
  <block type="procedures_defreturn" id="jo~=C?Ye~BV1t;MS_RQ+" collapsed="true" x="5" y="1758">
    <mutation>
      <arg name="__inputTickNumber" varid="K4Af%s#5YtdmP~mgXUZ["></arg>
    </mutation>
    <field name="NAME">getLastDigitOfTheTick</field>
    <comment pinned="false" h="80" w="160">Describe this function...</comment>
    <statement name="STACK">
      <block type="variables_set" id="U|:B_1QRX:DsGd4ik5JD">
        <field name="VAR" id="MH1i7DED25hPWh[:OiYQ" variabletype="">__stringValueOfTheTick</field>
        <value name="VALUE">
          <block type="text_join" id="?Z/HNHA|A,jnk3g:*gvw">
            <mutation items="1"></mutation>
            <value name="ADD0">
              <block type="variables_get" id="-Qess*.+1~k8Pb+@;K(q">
                <field name="VAR" id="K4Af%s#5YtdmP~mgXUZ[" variabletype="">__inputTickNumber</field>
              </block>
            </value>
          </block>
        </value>
        <next>
          <block type="variables_set" id="xcx^3,85|]Sd@el+)(Xy">
            <field name="VAR" id="j52H4Nz#zmWBfbncQqXu" variabletype="">__delemiterPosition</field>
            <value name="VALUE">
              <block type="text_indexOf" id="ad,BtZsEGsH{$[+tBgRs">
                <field name="END">LAST</field>
                <value name="VALUE">
                  <block type="variables_get" id="Bd.tkE^!`JD7[eyM:U_k">
                    <field name="VAR" id="MH1i7DED25hPWh[:OiYQ" variabletype="">__stringValueOfTheTick</field>
                  </block>
                </value>
                <value name="FIND">
                  <shadow type="text" id="=tO.XnTe6uZgK5_(0T5x">
                    <field name="TEXT">abc</field>
                  </shadow>
                  <block type="variables_get" id="mu*gX{d+dS9JeP#8@3n(">
                    <field name="VAR" id="y|FpW3?-:Yg%z!CW-2a:" variabletype="">fractionDelemiter</field>
                  </block>
                </value>
              </block>
            </value>
            <next>
              <block type="variables_set" id="C(*mA-E3OHx|sdYxb}54">
                <field name="VAR" id="Y1JG^y5Y@B`XTnwro,vH" variabletype="">__fractionPart</field>
                <value name="VALUE">
                  <block type="text_getSubstring" id="gf92wcF/)d:t!Mo:q6=6">
                    <mutation at1="true" at2="true"></mutation>
                    <field name="WHERE1">FROM_START</field>
                    <field name="WHERE2">FROM_END</field>
                    <value name="STRING">
                      <block type="variables_get" id="Pcj$d}QbghqJugT4jj_K">
                        <field name="VAR" id="MH1i7DED25hPWh[:OiYQ" variabletype="">__stringValueOfTheTick</field>
                      </block>
                    </value>
                    <value name="AT1">
                      <block type="variables_get" id="liJoF_akW2`O0pY/UUb@">
                        <field name="VAR" id="j52H4Nz#zmWBfbncQqXu" variabletype="">__delemiterPosition</field>
                      </block>
                    </value>
                    <value name="AT2">
                      <block type="math_number" id="@iM0l8ando`et#+L$u.O">
                        <field name="NUM">1</field>
                      </block>
                    </value>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="eI{i8p(/8tyv{}Qu]bi7">
                    <field name="VAR" id="O.@0Cs.c},T68x-]w]wt" variabletype="">__stringLastDigit</field>
                    <value name="VALUE">
                      <block type="text_charAt" id="/,+i]rjn.yL]$Y!P4Fa|">
                        <mutation at="true"></mutation>
                        <field name="WHERE">FROM_START</field>
                        <value name="VALUE">
                          <block type="variables_get" id="$87popp~sG51-$mhxrr9">
                            <field name="VAR" id="MH1i7DED25hPWh[:OiYQ" variabletype="">__stringValueOfTheTick</field>
                          </block>
                        </value>
                        <value name="AT">
                          <block type="text_length" id="ioR{CWJKnoyrg8Fe5G:n">
                            <value name="VALUE">
                              <shadow type="text" id="sSqpClJ,CzS5!qA#,]c6">
                                <field name="TEXT">abc</field>
                              </shadow>
                              <block type="variables_get" id="`7UkEkNHt:ElZd+(-(.5">
                                <field name="VAR" id="MH1i7DED25hPWh[:OiYQ" variabletype="">__stringValueOfTheTick</field>
                              </block>
                            </value>
                          </block>
                        </value>
                      </block>
                    </value>
                    <next>
                      <block type="controls_if" id="oKFfKq;,fLRd4D}uCYZx">
                        <value name="IF0">
                          <block type="logic_compare" id="rS;/H8@.K4|x|0_}mwn:">
                            <field name="OP">LT</field>
                            <value name="A">
                              <block type="text_length" id="6U7?D._qxjS5Mgf8mc6N">
                                <value name="VALUE">
                                  <shadow type="text" id="fP;F+$EyWsWL=I+uP,eN">
                                    <field name="TEXT">abc</field>
                                  </shadow>
                                  <block type="variables_get" id="Xm}++wKmz9]()uB[0$Q=">
                                    <field name="VAR" id="Y1JG^y5Y@B`XTnwro,vH" variabletype="">__fractionPart</field>
                                  </block>
                                </value>
                              </block>
                            </value>
                            <value name="B">
                              <block type="variables_get" id="n{Z=sY?o*[YE((+#PT46">
                                <field name="VAR" id="R#L7L=Cf{`Q;]yq5({`]" variabletype="">fractionPartLength</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <statement name="DO0">
                          <block type="variables_set" id="1GtY~#P4{LMZToY=D,qm">
                            <field name="VAR" id="O.@0Cs.c},T68x-]w]wt" variabletype="">__stringLastDigit</field>
                            <value name="VALUE">
                              <block type="math_number" id="|8a%mU7fWt~je.^i-g4j">
                                <field name="NUM">0</field>
                              </block>
                            </value>
                          </block>
                        </statement>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <value name="RETURN">
      <block type="variables_get" id="z|L5oY+Ioe0l!}yUU8k3">
        <field name="VAR" id="O.@0Cs.c},T68x-]w]wt" variabletype="">__stringLastDigit</field>
      </block>
    </value>
  </block>
  <block type="procedures_defnoreturn" id="I=sv57%kM)tF3rb*kMlG" collapsed="true" x="6" y="1793">
    <mutation>
      <arg name="__inputTickList" varid="DXXuZObE_I:d(su=a=4,"></arg>
      <arg name="__inputNeededLength" varid=".*P?OVjsooZJSxF$d`5H"></arg>
    </mutation>
    <field name="NAME">updateLastDigitStats</field>
    <comment pinned="false" h="80" w="160">Describe this function...</comment>
    <statement name="STACK">
      <block type="variables_set" id="%@=VQV._5298FUXJ=m*,">
        <field name="VAR" id="a1Y]8G9rw4qBh^GU1wF," variabletype="">LastDigitStatsList</field>
        <value name="VALUE">
          <block type="lists_repeat" id=",;6C}%N)fJlQb7t;7j50">
            <value name="ITEM">
              <block type="math_number" id="RsKgkf)X85eF]]F`Cmu+">
                <field name="NUM">0</field>
              </block>
            </value>
            <value name="NUM">
              <shadow type="math_number" id="]]s!5rQ.I/Bp}.H/)y#D">
                <field name="NUM">5</field>
              </shadow>
              <block type="math_number" id="*,3zd.]qY_,6XuMEB]=Q">
                <field name="NUM">9</field>
              </block>
            </value>
          </block>
        </value>
        <next>
          <block type="variables_set" id="d:uba]JHdz^tbB(pC68s">
            <field name="VAR" id="@zooM()x78^W5#^!659p" variabletype="">__chunkedList</field>
            <value name="VALUE">
              <block type="lists_getSublist" id="mSuN(9^Rf):S(sK=jPXY">
                <mutation at1="true" at2="true"></mutation>
                <field name="WHERE1">FROM_END</field>
                <field name="WHERE2">FROM_END</field>
                <value name="LIST">
                  <block type="variables_get" id="/yYIPTPE,AuR:vU1F6ix">
                    <field name="VAR" id="DXXuZObE_I:d(su=a=4," variabletype="">__inputTickList</field>
                  </block>
                </value>
                <value name="AT1">
                  <block type="variables_get" id="4kN/PET.Gl?J0bG7X62-">
                    <field name="VAR" id=".*P?OVjsooZJSxF$d`5H" variabletype="">__inputNeededLength</field>
                  </block>
                </value>
                <value name="AT2">
                  <block type="math_number" id="5^pg8QGr#$z)W!(m]3y8">
                    <field name="NUM">1</field>
                  </block>
                </value>
              </block>
            </value>
            <next>
              <block type="controls_forEach" id="]6)c~NaljBxw|~*1rARY">
                <field name="VAR" id="Y7AZ/PW`e~/(j*}?zclu" variabletype="">i</field>
                <value name="LIST">
                  <block type="variables_get" id="/a6UKJIBe6,jGV[zZ5QY">
                    <field name="VAR" id="@zooM()x78^W5#^!659p" variabletype="">__chunkedList</field>
                  </block>
                </value>
                <statement name="DO">
                  <block type="variables_set" id="{_BH@ASe~CffO_o;`Rmn">
                    <field name="VAR" id="%?V?F3cF%S]*:Ui45YJR" variabletype="">__lastDigitOfTheTick</field>
                    <value name="VALUE">
                      <block type="procedures_callreturn" id="y~laiMyAZ.PL8Qvwkf)f">
                        <mutation name="getLastDigitOfTheTick">
                          <arg name="__inputTickNumber"></arg>
                        </mutation>
                        <value name="ARG0">
                          <block type="variables_get" id="p-.ajHdXPFQG~fkUo{{m">
                            <field name="VAR" id="Y7AZ/PW`e~/(j*}?zclu" variabletype="">i</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <next>
                      <block type="variables_set" id="Qm/HC;+.oJ7FY3vN88BW">
                        <field name="VAR" id="k!;8Kf1h2|{!8DrQFE2`" variabletype="">__statsElement</field>
                        <value name="VALUE">
                          <block type="lists_getIndex" id="I?[gy;y24!aoWf3Ku;sa">
                            <mutation statement="false" at="true"></mutation>
                            <field name="MODE">GET</field>
                            <field name="WHERE">FROM_START</field>
                            <value name="VALUE">
                              <block type="variables_get" id="m2aE*{GD1YH}6t4m/=i,">
                                <field name="VAR" id="a1Y]8G9rw4qBh^GU1wF," variabletype="">LastDigitStatsList</field>
                              </block>
                            </value>
                            <value name="AT">
                              <block type="variables_get" id="MgcZ;e9XD^hw7^6em[kD">
                                <field name="VAR" id="%?V?F3cF%S]*:Ui45YJR" variabletype="">__lastDigitOfTheTick</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <next>
                          <block type="math_change" id="22*{BZhQj(#xqo7-d)Mf">
                            <field name="VAR" id="k!;8Kf1h2|{!8DrQFE2`" variabletype="">__statsElement</field>
                            <value name="DELTA">
                              <shadow type="math_number" id="p@`Kq`g_?aDqv]m|BB?Z">
                                <field name="NUM">1</field>
                              </shadow>
                              <block type="math_number" id="Dcx+zF4LzyUB8qDu6ia1">
                                <field name="NUM">1</field>
                              </block>
                            </value>
                            <next>
                              <block type="lists_setIndex" id="iF94!~s_}ukmpPy_0Ci:">
                                <mutation at="true"></mutation>
                                <field name="MODE">SET</field>
                                <field name="WHERE">FROM_START</field>
                                <value name="LIST">
                                  <block type="variables_get" id="wub}^bt|%03Z%l7yGdbd">
                                    <field name="VAR" id="a1Y]8G9rw4qBh^GU1wF," variabletype="">LastDigitStatsList</field>
                                  </block>
                                </value>
                                <value name="AT">
                                  <block type="variables_get" id="vvCmNiucS!DuSVp(chSI">
                                    <field name="VAR" id="%?V?F3cF%S]*:Ui45YJR" variabletype="">__lastDigitOfTheTick</field>
                                  </block>
                                </value>
                                <value name="TO">
                                  <block type="variables_get" id="zy[o,veawWF{5~-r0p}U">
                                    <field name="VAR" id="k!;8Kf1h2|{!8DrQFE2`" variabletype="">__statsElement</field>
                                  </block>
                                </value>
                              </block>
                            </next>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </statement>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
  </block>
  <block type="tick_analysis" id="VM1]d=x+?$KPUVJU84LN" collapsed="true" x="6" y="1828">
    <statement name="TICKANALYSIS_STACK">
      <block type="procedures_callnoreturn" id="`-O;C?/UJy#}k7X`.Gz(" collapsed="true">
        <mutation name="getLastDigitsMaxAndMinValues">
          <arg name="__inputListToDetermineMaxAndMin"></arg>
        </mutation>
        <value name="ARG0">
          <block type="variables_get" id="bcS5Q/Qt(qKh}-?ROv7o">
            <field name="VAR" id="a1Y]8G9rw4qBh^GU1wF," variabletype="">LastDigitStatsList</field>
          </block>
        </value>
        <next>
          <block type="procedures_callnoreturn" id="CzK.+2#+}gT,z}FWneD)" collapsed="true">
            <mutation name="updateLastDigitStats">
              <arg name="__inputTickList"></arg>
              <arg name="__inputNeededLength"></arg>
            </mutation>
            <value name="ARG0">
              <block type="ticks" id="[.ug!cL3E|v4#`kR+Yw/"></block>
            </value>
            <value name="ARG1">
              <block type="variables_get" id="0yW}Faq}4VQ~-.5PlxJ*">
                <field name="VAR" id=";,D?u9:B;4DnuF2|eg8M" variabletype="">number of ticks</field>
              </block>
            </value>
          </block>
        </next>
      </block>
    </statement>
  </block>
  <block type="procedures_defnoreturn" id="#%p[Gr=LCU7Fn}Ann-;]" collapsed="true" x="6" y="1864">
    <field name="NAME">initVars</field>
    <comment pinned="false" h="80" w="160">Describe this function...</comment>
    <statement name="STACK">
      <block type="variables_set" id="cN_X-dMc{I/]/~b*|:P5">
        <field name="VAR" id="a1Y]8G9rw4qBh^GU1wF," variabletype="">LastDigitStatsList</field>
        <value name="VALUE">
          <block type="lists_repeat" id="Q;6Eb4gj+0YV6w52^*5F">
            <value name="ITEM">
              <block type="math_number" id=":4@ofT:7iJT8-]{(vw8i">
                <field name="NUM">0</field>
              </block>
            </value>
            <value name="NUM">
              <shadow type="math_number" id="]]s!5rQ.I/Bp}.H/)y#D">
                <field name="NUM">5</field>
              </shadow>
              <block type="math_number" id="/sOS@2uVrwP($gc)BA}d">
                <field name="NUM">9</field>
              </block>
            </value>
          </block>
        </value>
        <next>
          <block type="variables_set" id="x6EF4XSBC?(w!w2S[e]H">
            <field name="VAR" id="R#L7L=Cf{`Q;]yq5({`]" variabletype="">fractionPartLength</field>
            <value name="VALUE">
              <block type="math_number" id="?{V?8.TBVPvZT@CQA04a">
                <field name="NUM">3</field>
              </block>
            </value>
            <next>
              <block type="variables_set" id="$zQ++S,Zc%@BR%D0Q%:h">
                <field name="VAR" id="y|FpW3?-:Yg%z!CW-2a:" variabletype="">fractionDelemiter</field>
                <value name="VALUE">
                  <block type="text" id="O+InE.lF]J6)#a6QOk{5">
                    <field name="TEXT">.</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="=[OxP}gw~UK@w02T4VWX">
                    <field name="VAR" id="BR__M7#?v;3wMK#tO/Dw" variabletype="">amountOfTicksToCheck</field>
                    <value name="VALUE">
                      <block type="math_number" id="U0K=J?Gf2~80B:atAF(K">
                        <field name="NUM">20</field>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
  </block>
  <block type="procedures_defnoreturn" id=")KTr?]3+jd+.kL[NR/]m" collapsed="true" x="5" y="1902">
    <mutation>
      <arg name="__inputListToDetermineMaxAndMin" varid="VP/k^|S)8?7[So3jO$G6"></arg>
    </mutation>
    <field name="NAME">getLastDigitsMaxAndMinValues</field>
    <comment pinned="false" h="80" w="160">Describe this function...</comment>
    <statement name="STACK">
      <block type="variables_set" id="`h)24-6VGf@[e#sIUKF3">
        <field name="VAR" id="F0f:u:apL@r?C6a`(878" variabletype="">MaxInTheLastDigitStats</field>
        <value name="VALUE">
          <block type="math_number" id="*U`mXOve!5K6DQ8-)gDS">
            <field name="NUM">0</field>
          </block>
        </value>
        <next>
          <block type="variables_set" id="rK24]*RUjfAX_EesF+Xv">
            <field name="VAR" id="S=xTw3TcL_@WcEtyE{Tt" variabletype="">MinInTheLastDigitStats</field>
            <value name="VALUE">
              <block type="math_number" id="eW-R1o5;,?Az5Ov(U,{@">
                <field name="NUM">1000</field>
              </block>
            </value>
            <next>
              <block type="controls_for" id=":Y|qjQB.b~qkuR#$jPS=">
                <field name="VAR" id="V~Gk-8@zjklU:9}9HGlf" variabletype="">j</field>
                <value name="FROM">
                  <block type="math_number" id="l(z$Fu8qGU.6P:Rc4q[2">
                    <field name="NUM">0</field>
                  </block>
                </value>
                <value name="TO">
                  <block type="math_number" id="zqDIiZt2k_n(MF|Tj0h|">
                    <field name="NUM">9</field>
                  </block>
                </value>
                <value name="BY">
                  <block type="math_number" id="0,T~Ew.*T@qUS-]$dEXi">
                    <field name="NUM">1</field>
                  </block>
                </value>
                <statement name="DO">
                  <block type="variables_set" id="mtu@qb31*mnbmL1=ukc3">
                    <field name="VAR" id="k!;8Kf1h2|{!8DrQFE2`" variabletype="">__statsElement</field>
                    <value name="VALUE">
                      <block type="lists_getIndex" id="Ln{uY%6vU5I!Q:*p_mlP">
                        <mutation statement="false" at="true"></mutation>
                        <field name="MODE">GET</field>
                        <field name="WHERE">FROM_START</field>
                        <value name="VALUE">
                          <block type="variables_get" id="Rk-E$[xHiA]Eh-7{/FJp">
                            <field name="VAR" id="VP/k^|S)8?7[So3jO$G6" variabletype="">__inputListToDetermineMaxAndMin</field>
                          </block>
                        </value>
                        <value name="AT">
                          <block type="variables_get" id="E7UOxO|$Q0%!::U^703@">
                            <field name="VAR" id="V~Gk-8@zjklU:9}9HGlf" variabletype="">j</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <next>
                      <block type="controls_if" id="5-EW--~(*fhvE/ejz]1i">
                        <value name="IF0">
                          <block type="logic_compare" id="@gn=da~?}e0?kw0*hAQ8">
                            <field name="OP">LT</field>
                            <value name="A">
                              <block type="variables_get" id="d=|rlW}BNFP6al^7u8fH">
                                <field name="VAR" id="k!;8Kf1h2|{!8DrQFE2`" variabletype="">__statsElement</field>
                              </block>
                            </value>
                            <value name="B">
                              <block type="variables_get" id="nv#|F3zVB8lvUgN,7LpX">
                                <field name="VAR" id="S=xTw3TcL_@WcEtyE{Tt" variabletype="">MinInTheLastDigitStats</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <statement name="DO0">
                          <block type="variables_set" id=":Wi@Qg{A)?u#4RxdDEa}">
                            <field name="VAR" id="S=xTw3TcL_@WcEtyE{Tt" variabletype="">MinInTheLastDigitStats</field>
                            <value name="VALUE">
                              <block type="variables_get" id="g#Z]7OB6+MRU^@G~PK[[">
                                <field name="VAR" id="k!;8Kf1h2|{!8DrQFE2`" variabletype="">__statsElement</field>
                              </block>
                            </value>
                            <next>
                              <block type="variables_set" id=";jY/yuv-6mD`L?kmWGVT">
                                <field name="VAR" id="W5O+hPKIq4x)v|_)$`8A" variabletype="">MinStatsIdx</field>
                                <value name="VALUE">
                                  <block type="variables_get" id="VIL3tKqw|~mt_[XY0h(q">
                                    <field name="VAR" id="V~Gk-8@zjklU:9}9HGlf" variabletype="">j</field>
                                  </block>
                                </value>
                              </block>
                            </next>
                          </block>
                        </statement>
                        <next>
                          <block type="controls_if" id="2DRbU)VSeW:svpu@6_q@">
                            <value name="IF0">
                              <block type="logic_compare" id="hAwvtk}St=;4xX@,gKI`">
                                <field name="OP">GT</field>
                                <value name="A">
                                  <block type="variables_get" id="JoVYBT;S~|DLXcQ2xu{L">
                                    <field name="VAR" id="k!;8Kf1h2|{!8DrQFE2`" variabletype="">__statsElement</field>
                                  </block>
                                </value>
                                <value name="B">
                                  <block type="variables_get" id="3[e@78G:G[+iSw{A:=+Z">
                                    <field name="VAR" id="F0f:u:apL@r?C6a`(878" variabletype="">MaxInTheLastDigitStats</field>
                                  </block>
                                </value>
                              </block>
                            </value>
                            <statement name="DO0">
                              <block type="variables_set" id="L^(Zb^HB}0hy.OU`2ToC">
                                <field name="VAR" id="F0f:u:apL@r?C6a`(878" variabletype="">MaxInTheLastDigitStats</field>
                                <value name="VALUE">
                                  <block type="variables_get" id="+L0[QSvN(7~6/HsT.[GS">
                                    <field name="VAR" id="k!;8Kf1h2|{!8DrQFE2`" variabletype="">__statsElement</field>
                                  </block>
                                </value>
                                <next>
                                  <block type="variables_set" id="dxjV.LOfWR,^0:sdA)r2">
                                    <field name="VAR" id="t^~L#g{=[+g@JOk_I_oM" variabletype="">MaxStatsIdx</field>
                                    <value name="VALUE">
                                      <block type="variables_get" id="+n9BJl3ivcV0nM30Nrk%">
                                        <field name="VAR" id="V~Gk-8@zjklU:9}9HGlf" variabletype="">j</field>
                                      </block>
                                    </value>
                                  </block>
                                </next>
                              </block>
                            </statement>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </statement>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
  </block>
  <block type="tick_analysis" id="mxb|JlNaJcz*2w*$o=Cg" collapsed="true" x="2" y="1945">
    <statement name="TICKANALYSIS_STACK">
      <block type="procedures_callnoreturn" id="fkN?j:w@Ycn-ef.5p4;2" collapsed="true">
        <mutation name="getLastDigitsMaxAndMinValues">
          <arg name="__inputListToDetermineMaxAndMin"></arg>
        </mutation>
        <value name="ARG0">
          <block type="variables_get" id="9?[ENYrQ7=+cY$yesikP">
            <field name="VAR" id="a1Y]8G9rw4qBh^GU1wF," variabletype="">LastDigitStatsList</field>
          </block>
        </value>
        <next>
          <block type="procedures_callnoreturn" id="kEG@4uRLDWwVrTY,W1+m" collapsed="true">
            <mutation name="updateLastDigitStats">
              <arg name="__inputTickList"></arg>
              <arg name="__inputNeededLength"></arg>
            </mutation>
            <value name="ARG0">
              <block type="ticks" id=":H+p0]YF!qnk$zHg}d~V"></block>
            </value>
            <value name="ARG1">
              <block type="variables_get" id=".XR3Mt**Fcc|@6dC%-o8">
                <field name="VAR" id=";,D?u9:B;4DnuF2|eg8M" variabletype="">number of ticks</field>
              </block>
            </value>
            <next>
              <block type="notify" id="fr~}LHd%7FKsNQ|xtjVb" collapsed="true">
                <field name="NOTIFICATION_TYPE">warn</field>
                <field name="NOTIFICATION_SOUND">silent</field>
                <value name="MESSAGE">
                  <shadow type="text" id="0ldCj9IwPWHm;RvgH=43">
                    <field name="TEXT">abc</field>
                  </shadow>
                  <block type="text_join" id="HydD.HTs#7cUK?v)3*EQ" collapsed="true">
                    <mutation items="4"></mutation>
                    <value name="ADD0">
                      <block type="text" id="qQeZ:uTQG-XY`:MUo,jd" collapsed="true">
                        <field name="TEXT">[BRANNY_KE PRO]  : [multi-settings]  &lt;shxmmy&gt;   </field>
                      </block>
                    </value>
                    <value name="ADD1">
                      <block type="text" id="`Kx3j_dbqIK,0`qhPicO" collapsed="true">
                        <field name="TEXT">[ </field>
                      </block>
                    </value>
                    <value name="ADD2">
                      <block type="variables_get" id="XgJ^]kZxU,D8:~rOYfyo" collapsed="true">
                        <field name="VAR" id="W5O+hPKIq4x)v|_)$`8A" variabletype="">MinStatsIdx</field>
                      </block>
                    </value>
                    <value name="ADD3">
                      <block type="text" id="wqhw;ScUYZU}]#zNlzU}" collapsed="true">
                        <field name="TEXT"> ]</field>
                      </block>
                    </value>
                  </block>
                </value>
                <next>
                  <block type="notify" id="E4:[rtWYgu*:x@We.o*T" collapsed="true">
                    <field name="NOTIFICATION_TYPE">info</field>
                    <field name="NOTIFICATION_SOUND">silent</field>
                    <value name="MESSAGE">
                      <shadow type="text" id="0ldCj9IwPWHm;RvgH=43">
                        <field name="TEXT">abc</field>
                      </shadow>
                      <block type="text_join" id="[@i9rtxod;u69nI5;.}H" collapsed="true">
                        <mutation items="4"></mutation>
                        <value name="ADD0">
                          <block type="text" id="Z`*sawB[#drt)Q)kx?`%" collapsed="true">
                            <field name="TEXT">[BRANNY_KE] : Auto analysis  ▶ CHART READING + TICK COUNTING   &lt;shxmmy&gt;  </field>
                          </block>
                        </value>
                        <value name="ADD1">
                          <block type="text" id="FD3=VS@!zvbYcA[#d4lF" collapsed="true">
                            <field name="TEXT">[ </field>
                          </block>
                        </value>
                        <value name="ADD2">
                          <block type="variables_get" id="GU.|Z9Qv-g*uZ*PU:$lx" collapsed="true">
                            <field name="VAR" id="t^~L#g{=[+g@JOk_I_oM" variabletype="">MaxStatsIdx</field>
                          </block>
                        </value>
                        <value name="ADD3">
                          <block type="text" id="}05zl^aE7%z8M#3GKm4)" collapsed="true">
                            <field name="TEXT"> ]</field>
                          </block>
                        </value>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
  </block>
  <block type="tick_analysis" id="{+9G}Pbm_~Vy8p9mMvv/" collapsed="true" x="3" y="1983">
    <statement name="TICKANALYSIS_STACK">
      <block type="controls_if" id="xnslc;HnZ`H*qyubb|=S" collapsed="true">
        <mutation elseif="1"></mutation>
        <value name="IF0">
          <block type="logic_compare" id="rrflBusF_.W+34^mU`+F" inline="false" collapsed="true">
            <field name="OP">EQ</field>
            <value name="A">
              <block type="variables_get" id="iycsp=[5z#wY*dXJoTk*">
                <field name="VAR" id="C#L6A2g4X]W#7~3$tsTO" variabletype="">SHXMMY</field>
              </block>
            </value>
            <value name="B">
              <block type="logic_boolean" id="@(_T!KeVV4j%ZwfWACk@">
                <field name="BOOL">FALSE</field>
              </block>
            </value>
          </block>
        </value>
        <value name="IF1">
          <block type="logic_compare" id="Iz_BYJ;fOKgu%v(]q6]J" inline="false" collapsed="true">
            <field name="OP">EQ</field>
            <value name="A">
              <block type="variables_get" id="5h@T@.ix,4BxjGOw]opE">
                <field name="VAR" id="C#L6A2g4X]W#7~3$tsTO" variabletype="">SHXMMY</field>
              </block>
            </value>
            <value name="B">
              <block type="logic_boolean" id="$NdYcU+GjH-j@z9(X!/_">
                <field name="BOOL">TRUE</field>
              </block>
            </value>
          </block>
        </value>
      </block>
    </statement>
  </block>
</xml>