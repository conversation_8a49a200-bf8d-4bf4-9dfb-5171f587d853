<xml xmlns="https://developers.google.com/blockly/xml" is_dbot="true" collection="false">
  <variables>
    <variable id="r3#,*L{|k_*_oikco?~E">Stake</variable>
    <variable id="iexW!(p-QSL[kDvWK`o-">Take Profit</variable>
    <variable id=".b8,E|dl7Z~oHMV~G;zm">ticks</variable>
    <variable id="!nZ#u54J;h7qX~z1{FF*">Preduction over</variable>
    <variable id="@VN/)JBDaz5d-aHKEiy:">Switch Markets</variable>
    <variable id="AwGIX3K`=6@AOl/-`G)O">Stop Loss</variable>
    <variable id="C_~~s:?,dwOS0*WOz9qL">Duke</variable>
    <variable id="Vd)a}rnFL$Mr`V[FG0ZX">Count</variable>
    <variable id="m7(YQSH:pOaprkmLlkfZ">Entrypoint</variable>
    <variable id="dH{?1[,[C-~b|]d0QtmE">Total Lost</variable>
    <variable id="AT;gTInFQw;fLD1haYlL">Count Loss</variable>
    <variable id="!(67Y!ov*~[U/$pjZ,0.">Stake win</variable>
    <variable id="TU|r7.|?P{Oa(p5E!/Ft">Loss</variable>
  </variables>
  <block type="trade_definition" id="Dn+|l@F{1c%e:[S_b*y+" deletable="false" x="0" y="60">
    <statement name="TRADE_OPTIONS">
      <block type="trade_definition_market" id="?%-k%1.^XwP?^NTZ,Yt%" deletable="false" movable="false">
        <field name="MARKET_LIST">synthetic_index</field>
        <field name="SUBMARKET_LIST">random_index</field>
        <field name="SYMBOL_LIST">1HZ10V</field>
        <next>
          <block type="trade_definition_tradetype" id="Ny,T+]j~q=euwb2^Hkrt" deletable="false" movable="false">
            <field name="TRADETYPECAT_LIST">digits</field>
            <field name="TRADETYPE_LIST">overunder</field>
            <next>
              <block type="trade_definition_contracttype" id="9KwoosLzczeC~F:5~JTl" deletable="false" movable="false">
                <field name="TYPE_LIST">both</field>
                <next>
                  <block type="trade_definition_candleinterval" id="YCRtYsVDuvI2m(|Sn=oN" deletable="false" movable="false">
                    <field name="CANDLEINTERVAL_LIST">60</field>
                    <next>
                      <block type="trade_definition_restartbuysell" id="5Nn9BCfR{SxZMWof=~U6" deletable="false" movable="false">
                        <field name="TIME_MACHINE_ENABLED">FALSE</field>
                        <next>
                          <block type="trade_definition_restartonerror" id="$w}(v#RJo+GTV~6N#rJ(" deletable="false" movable="false">
                            <field name="RESTARTONERROR">TRUE</field>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="INITIALIZATION">
      <block type="variables_set" id="VtinTUlRho|l^-0R$7%A">
        <field name="VAR" id="r3#,*L{|k_*_oikco?~E">Stake</field>
        <value name="VALUE">
          <block type="math_number" id="eE1:{)l/a]lYGgEq(xNf">
            <field name="NUM">4</field>
          </block>
        </value>
        <next>
          <block type="variables_set" id="yuTs63H@^mt2$7WA8MM#">
            <field name="VAR" id="iexW!(p-QSL[kDvWK`o-">Take Profit</field>
            <value name="VALUE">
              <block type="math_number" id="Ze7;s[0;Nz2%O4S_83gA">
                <field name="NUM">4</field>
              </block>
            </value>
            <next>
              <block type="variables_set" id="nGr}CP0n6hP98*@D%p1~">
                <field name="VAR" id="AwGIX3K`=6@AOl/-`G)O">Stop Loss</field>
                <value name="VALUE">
                  <block type="math_number" id="CNwP055C@dZ]pl;V~^@e">
                    <field name="NUM">200</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="{P*qZBi/BZWtyB6N602=">
                    <field name="VAR" id="m7(YQSH:pOaprkmLlkfZ">Entrypoint</field>
                    <value name="VALUE">
                      <block type="math_number" id="yzg8zK;s/kz?qgGN]Tua">
                        <field name="NUM">8</field>
                      </block>
                    </value>
                    <next>
                      <block type="variables_set" id="0m+b$N[r|}?_eoJ9:2kG">
                        <field name="VAR" id="!nZ#u54J;h7qX~z1{FF*">Preduction over</field>
                        <value name="VALUE">
                          <block type="math_number" id="W1xqn(n[yRu|ujdz0W8W">
                            <field name="NUM">9</field>
                          </block>
                        </value>
                        <next>
                          <block type="variables_set" id="d/!:/.sMPSdRo}cUz;:U" collapsed="true">
                            <field name="VAR" id=".b8,E|dl7Z~oHMV~G;zm">ticks</field>
                            <value name="VALUE">
                              <block type="math_number" id="T`ncM;fb1C07#p;:YfTC">
                                <field name="NUM">3</field>
                              </block>
                            </value>
                            <next>
                              <block type="variables_set" id="7fg|^nHlq-V-mu9l0iZ." collapsed="true">
                                <field name="VAR" id="@VN/)JBDaz5d-aHKEiy:">Switch Markets</field>
                                <value name="VALUE">
                                  <block type="logic_boolean" id="32@L^.A:^/tU^a=tJ8km">
                                    <field name="BOOL">TRUE</field>
                                  </block>
                                </value>
                                <next>
                                  <block type="variables_set" id="}!.lk%[4MD3|lu[DL9|?" collapsed="true">
                                    <field name="VAR" id="C_~~s:?,dwOS0*WOz9qL">Duke</field>
                                    <value name="VALUE">
                                      <block type="text" id="T,6!hzYxx^XsGf,h7qTU">
                                        <field name="TEXT">good</field>
                                      </block>
                                    </value>
                                    <next>
                                      <block type="variables_set" id="04?NsjPy@EcVaNBFvS~q" collapsed="true">
                                        <field name="VAR" id="!(67Y!ov*~[U/$pjZ,0.">Stake win</field>
                                        <value name="VALUE">
                                          <block type="variables_get" id="x|YP+!A%~5?j+94P(AuL" collapsed="true">
                                            <field name="VAR" id="r3#,*L{|k_*_oikco?~E">Stake</field>
                                          </block>
                                        </value>
                                        <next>
                                          <block type="variables_set" id=",N%:N5HXk:D9]dYCQJ-N" collapsed="true">
                                            <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                                            <value name="VALUE">
                                              <block type="math_number" id="%dG/.3}`9pxjxDYvt3t$" collapsed="true">
                                                <field name="NUM">0</field>
                                              </block>
                                            </value>
                                            <next>
                                              <block type="variables_set" id="c)urvmACc.@;kFhQ__A-" collapsed="true">
                                                <field name="VAR" id="TU|r7.|?P{Oa(p5E!/Ft">Loss</field>
                                                <value name="VALUE">
                                                  <block type="math_number" id="ev;8cm*Is:oBDQh%81J=" collapsed="true">
                                                    <field name="NUM">0</field>
                                                  </block>
                                                </value>
                                              </block>
                                            </next>
                                          </block>
                                        </next>
                                      </block>
                                    </next>
                                  </block>
                                </next>
                              </block>
                            </next>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="SUBMARKET">
      <block type="trade_definition_tradeoptions" id="WeNX1fen.XO?FdRLm_m^">
        <mutation xmlns="http://www.w3.org/1999/xhtml" has_first_barrier="false" has_second_barrier="false" has_prediction="true"></mutation>
        <field name="DURATIONTYPE_LIST">t</field>
        <value name="DURATION">
          <shadow type="math_number_positive" id="},}3yJ69-J-gHxwa*8N:" collapsed="true">
            <field name="NUM">3</field>
          </shadow>
          <block type="variables_get" id="O`E%-5==dgSddRU@QC0O">
            <field name="VAR" id=".b8,E|dl7Z~oHMV~G;zm">ticks</field>
          </block>
        </value>
        <value name="AMOUNT">
          <shadow type="math_number_positive" id="f;YaY;efEV^HkE4fJgY/">
            <field name="NUM">1</field>
          </shadow>
          <block type="variables_get" id="#=u;*~XqDVf(6j+LY9ZD" collapsed="true">
            <field name="VAR" id="r3#,*L{|k_*_oikco?~E">Stake</field>
          </block>
        </value>
        <value name="PREDICTION">
          <shadow type="math_number_positive" id="#QW//1ZTwS:2:9i0kPkD">
            <field name="NUM">1</field>
          </shadow>
          <block type="variables_get" id="#%jL*}Y#OvP**i)?V[7~" collapsed="true">
            <field name="VAR" id="!nZ#u54J;h7qX~z1{FF*">Preduction over</field>
          </block>
        </value>
      </block>
    </statement>
  </block>
  <block type="after_purchase" id="N|p|#TF(n)T5q(2;:E_L" collapsed="true" x="735" y="50">
    <statement name="AFTERPURCHASE_STACK">
      <block type="controls_if" id="z;c[K$OpqdAjU*wCLl*9">
        <mutation xmlns="http://www.w3.org/1999/xhtml" elseif="1" else="1"></mutation>
        <value name="IF0">
          <block type="logic_compare" id="NHjJbm[Cijv~Ley/ZvIr" collapsed="true">
            <field name="OP">GTE</field>
            <value name="A">
              <block type="total_profit" id="mmY6mRJMyW:XJT7y-BUk" collapsed="true"></block>
            </value>
            <value name="B">
              <block type="variables_get" id="Qu^BJ/f~0@mXd]Vf[#2w" collapsed="true">
                <field name="VAR" id="iexW!(p-QSL[kDvWK`o-">Take Profit</field>
              </block>
            </value>
          </block>
        </value>
        <statement name="DO0">
          <block type="text_print" id="N(zlzQ$brWJvN0,0xxB5">
            <value name="TEXT">
              <shadow type="text" id="@eqR_i-,.3g]~5e/`t~F" collapsed="true">
                <field name="TEXT">Take Profit Hit. </field>
              </shadow>
            </value>
          </block>
        </statement>
        <value name="IF1">
          <block type="logic_compare" id="gU^da0)!38=fPrv[Rz3T" collapsed="true">
            <field name="OP">LTE</field>
            <value name="A">
              <block type="total_profit" id="#Ns63Q/r5HW*!La^07U-" collapsed="true"></block>
            </value>
            <value name="B">
              <block type="math_single" id="y)htA1wy)WnQ,7:f(MkN" collapsed="true">
                <field name="OP">NEG</field>
                <value name="NUM">
                  <shadow type="math_number" id="z8}-_]UDkc8rkuzJ(ckC">
                    <field name="NUM">9</field>
                  </shadow>
                  <block type="variables_get" id="[F8%a@pBeA2Ks[Q=.Vik" collapsed="true">
                    <field name="VAR" id="AwGIX3K`=6@AOl/-`G)O">Stop Loss</field>
                  </block>
                </value>
              </block>
            </value>
          </block>
        </value>
        <statement name="DO1">
          <block type="text_print" id="5|so};ejYw%-A{s=d5;Q">
            <value name="TEXT">
              <shadow type="text" id=")01HACF%x-bW*#tiNBaS" collapsed="true">
                <field name="TEXT">Sorry Stop Loss Hit contact +254702490526</field>
              </shadow>
            </value>
          </block>
        </statement>
        <statement name="ELSE">
          <block type="controls_if" id="(~~!1CBdadAhV8aPYMI0">
            <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
            <value name="IF0">
              <block type="contract_check_result" id="RQXD%_pE6|`{LQdh#sXt" collapsed="true">
                <field name="CHECK_RESULT">loss</field>
              </block>
            </value>
            <statement name="DO0">
              <block type="variables_set" id="Y8#(dD(8gynF]hyWs9ZA">
                <field name="VAR" id="C_~~s:?,dwOS0*WOz9qL">Duke</field>
                <value name="VALUE">
                  <block type="text" id="mkk;Y`WZcY9TP4bozhYj">
                    <field name="TEXT">tt</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="P+8o)}YboJF}`bEm_(#S">
                    <field name="VAR" id=".b8,E|dl7Z~oHMV~G;zm">ticks</field>
                    <value name="VALUE">
                      <block type="math_number" id="J1D[-v:h:0x@p)P^!PIP">
                        <field name="NUM">3</field>
                      </block>
                    </value>
                    <next>
                      <block type="math_change" id="G*BM+@f8B`q7.;#=YV0;">
                        <field name="VAR" id="dH{?1[,[C-~b|]d0QtmE">Total Lost</field>
                        <value name="DELTA">
                          <shadow type="math_number" id="],_|qjdItX+pU6bhdxq@">
                            <field name="NUM">1</field>
                          </shadow>
                          <block type="variables_get" id="]@;mf~:Ox@#!HF{L(sY^">
                            <field name="VAR" id="r3#,*L{|k_*_oikco?~E">Stake</field>
                          </block>
                        </value>
                        <next>
                          <block type="controls_if" id="H0X@Z*pjn5VGGaSl.Yi-">
                            <value name="IF0">
                              <block type="logic_compare" id="ABQKX3[3H[ImmjhSUgz]">
                                <field name="OP">GT</field>
                                <value name="A">
                                  <block type="variables_get" id="J18WxO2B-mP2xYIm)L(d">
                                    <field name="VAR" id="AT;gTInFQw;fLD1haYlL">Count Loss</field>
                                  </block>
                                </value>
                                <value name="B">
                                  <block type="math_number" id="A`6=,.3d-P{mS:;|2k(r">
                                    <field name="NUM">0</field>
                                  </block>
                                </value>
                              </block>
                            </value>
                            <statement name="DO0">
                              <block type="variables_set" id=":hf[Jy8U%:O)m7.[u`qj">
                                <field name="VAR" id="AT;gTInFQw;fLD1haYlL">Count Loss</field>
                                <value name="VALUE">
                                  <block type="math_number" id="hpnt6EVnr8f00;a[.~,4">
                                    <field name="NUM">0</field>
                                  </block>
                                </value>
                              </block>
                            </statement>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </statement>
            <statement name="ELSE">
              <block type="variables_set" id="CecL-b%FAN:{^)K:g(P_">
                <field name="VAR" id="C_~~s:?,dwOS0*WOz9qL">Duke</field>
                <value name="VALUE">
                  <block type="text" id="_CRuoO_F=*2}LNXQ5{tJ">
                    <field name="TEXT">tt</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="kft@Nh$_j?F:_EZ_ZM*l">
                    <field name="VAR" id="!nZ#u54J;h7qX~z1{FF*">Preduction over</field>
                    <value name="VALUE">
                      <block type="math_number" id="bsh[wwl^Sk|$p!*:PvH`">
                        <field name="NUM">6</field>
                      </block>
                    </value>
                    <next>
                      <block type="variables_set" id="1hUp)Gt[/yAlaGmt9P/`">
                        <field name="VAR" id=".b8,E|dl7Z~oHMV~G;zm">ticks</field>
                        <value name="VALUE">
                          <block type="math_number" id=",*/YIL/=mC(W*Z?%MT#H">
                            <field name="NUM">3</field>
                          </block>
                        </value>
                        <next>
                          <block type="math_change" id="ZaVchW]EW~K6PH9!oL?)">
                            <field name="VAR" id="dH{?1[,[C-~b|]d0QtmE">Total Lost</field>
                            <value name="DELTA">
                              <shadow type="math_number" id="@?/*(ebZ%[.3UloLjCsq">
                                <field name="NUM">1</field>
                              </shadow>
                              <block type="math_single" id="Oypjr2d:qk)SDBJHlYY~">
                                <field name="OP">NEG</field>
                                <value name="NUM">
                                  <shadow type="math_number" id="Y=#RhBHdib([jG%*]nv4">
                                    <field name="NUM">9</field>
                                  </shadow>
                                  <block type="read_details" id="1~IY/nRc$-zdNtR}6V=O">
                                    <field name="DETAIL_INDEX">4</field>
                                  </block>
                                </value>
                              </block>
                            </value>
                            <next>
                              <block type="controls_if" id="VdB;}Q8mQwawiV:AB6+}">
                                <value name="IF0">
                                  <block type="logic_compare" id="TU)vF=e$aglt]K@#e8m9">
                                    <field name="OP">LT</field>
                                    <value name="A">
                                      <block type="variables_get" id="ipT%f@y1aiVK[?.P=;7I">
                                        <field name="VAR" id="dH{?1[,[C-~b|]d0QtmE">Total Lost</field>
                                      </block>
                                    </value>
                                    <value name="B">
                                      <block type="math_number" id="-6azGw7~,3`,fkXzXnAj">
                                        <field name="NUM">0</field>
                                      </block>
                                    </value>
                                  </block>
                                </value>
                                <statement name="DO0">
                                  <block type="variables_set" id="dmzWXQ:eNr/.oxUlO6^G">
                                    <field name="VAR" id="dH{?1[,[C-~b|]d0QtmE">Total Lost</field>
                                    <value name="VALUE">
                                      <block type="math_number" id="%dP0Q?b?}A!ecsyp?xLL">
                                        <field name="NUM">0</field>
                                      </block>
                                    </value>
                                  </block>
                                </statement>
                              </block>
                            </next>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </statement>
            <next>
              <block type="controls_if" id="FKrp!J}b$}8)Wfkz~[sS" collapsed="true">
                <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                <value name="IF0">
                  <block type="logic_compare" id="Voro{m{VMxg^S6,%;h*}">
                    <field name="OP">GT</field>
                    <value name="A">
                      <block type="variables_get" id="f_u/kMyODh~Yh7hcCer$">
                        <field name="VAR" id="dH{?1[,[C-~b|]d0QtmE">Total Lost</field>
                      </block>
                    </value>
                    <value name="B">
                      <block type="math_number" id="^Nx*,:k}{@Mt!0*z7y;!">
                        <field name="NUM">0</field>
                      </block>
                    </value>
                  </block>
                </value>
                <statement name="DO0">
                  <block type="variables_set" id="^c6w:!!f_fjt_qlp5j@v">
                    <field name="VAR" id="!nZ#u54J;h7qX~z1{FF*">Preduction over</field>
                    <value name="VALUE">
                      <block type="math_number" id="/i.?@hI^jL6R1wWxENfL">
                        <field name="NUM">6</field>
                      </block>
                    </value>
                    <next>
                      <block type="math_change" id="a-A=-ddmGU86VJ]G74G:">
                        <field name="VAR" id="AT;gTInFQw;fLD1haYlL">Count Loss</field>
                        <value name="DELTA">
                          <shadow type="math_number" id="],_|qjdItX+pU6bhdxq@">
                            <field name="NUM">1</field>
                          </shadow>
                        </value>
                        <next>
                          <block type="controls_if" id="M?4$B#/degTB)^,+MRyf">
                            <value name="IF0">
                              <block type="logic_compare" id="RKN8OJgWPH.[3iw~RH7M">
                                <field name="OP">EQ</field>
                                <value name="A">
                                  <block type="variables_get" id="_HphL;STVb@f2,}dMdDr">
                                    <field name="VAR" id="AT;gTInFQw;fLD1haYlL">Count Loss</field>
                                  </block>
                                </value>
                                <value name="B">
                                  <block type="math_number" id="55h9Af#NVG0ypyE|NSvS">
                                    <field name="NUM">1</field>
                                  </block>
                                </value>
                              </block>
                            </value>
                            <statement name="DO0">
                              <block type="variables_set" id="R_hdqHmK4OlFt@k82so~">
                                <field name="VAR" id="r3#,*L{|k_*_oikco?~E">Stake</field>
                                <value name="VALUE">
                                  <block type="math_arithmetic" id="R@_pEYF)IrA`pCUl~GI[">
                                    <field name="OP">DIVIDE</field>
                                    <value name="A">
                                      <shadow type="math_number" id="MFYJoQ6b]HS67pnQ3|Pl">
                                        <field name="NUM">1</field>
                                      </shadow>
                                      <block type="math_arithmetic" id=":[~yaJZ6|I8^88tlGB(X">
                                        <field name="OP">DIVIDE</field>
                                        <value name="A">
                                          <shadow type="math_number" id="MFYJoQ6b]HS67pnQ3|Pl">
                                            <field name="NUM">1</field>
                                          </shadow>
                                          <block type="math_arithmetic" id="3PNIP$V3I]P3XZb84qDj">
                                            <field name="OP">MULTIPLY</field>
                                            <value name="A">
                                              <shadow type="math_number" id="MFYJoQ6b]HS67pnQ3|Pl">
                                                <field name="NUM">1</field>
                                              </shadow>
                                              <block type="variables_get" id="oiH~|r))?dh_a$xb9Q#F">
                                                <field name="VAR" id="dH{?1[,[C-~b|]d0QtmE">Total Lost</field>
                                              </block>
                                            </value>
                                            <value name="B">
                                              <shadow type="math_number" id="Q~?%`F(4=s:Fyrij-^g0">
                                                <field name="NUM">100</field>
                                              </shadow>
                                            </value>
                                          </block>
                                        </value>
                                        <value name="B">
                                          <shadow type="math_number" id="S}CkfbeA5sB2y!caiBZI">
                                            <field name="NUM">50</field>
                                          </shadow>
                                        </value>
                                      </block>
                                    </value>
                                    <value name="B">
                                      <shadow type="math_number" id="O,Gn|Jf5-9okLaS`dsn*">
                                        <field name="NUM">2</field>
                                      </shadow>
                                    </value>
                                  </block>
                                </value>
                              </block>
                            </statement>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </statement>
                <statement name="ELSE">
                  <block type="variables_set" id="/D=%BtMp%.oBMmmt++?F">
                    <field name="VAR" id=".b8,E|dl7Z~oHMV~G;zm">ticks</field>
                    <value name="VALUE">
                      <block type="math_number" id="(m;f7Ub,]m5CXo=5#:w=">
                        <field name="NUM">3</field>
                      </block>
                    </value>
                    <next>
                      <block type="variables_set" id="5:.6,$wy!c(A26lJ=i_J">
                        <field name="VAR" id="!nZ#u54J;h7qX~z1{FF*">Preduction over</field>
                        <value name="VALUE">
                          <block type="math_number" id="U;G1@d!Qsj4AV5bK@Xf!">
                            <field name="NUM">9</field>
                          </block>
                        </value>
                        <next>
                          <block type="variables_set" id="=eUnAVBw3%IVKG;jBqKK">
                            <field name="VAR" id="AT;gTInFQw;fLD1haYlL">Count Loss</field>
                            <value name="VALUE">
                              <block type="math_number" id="=aFt!v1sa?_Qz5@wc(ya">
                                <field name="NUM">0</field>
                              </block>
                            </value>
                            <next>
                              <block type="variables_set" id=":E7qMnqf-^+mU})i.S~n" collapsed="true">
                                <field name="VAR" id="r3#,*L{|k_*_oikco?~E">Stake</field>
                                <value name="VALUE">
                                  <block type="variables_get" id="t6h)WB6{sCpQMs~;BUx1" collapsed="true">
                                    <field name="VAR" id="!(67Y!ov*~[U/$pjZ,0.">Stake win</field>
                                  </block>
                                </value>
                              </block>
                            </next>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </statement>
                <next>
                  <block type="trade_again" id="aSW5w-)(u[#Px0zU;mxA"></block>
                </next>
              </block>
            </next>
          </block>
        </statement>
      </block>
    </statement>
  </block>
  <block type="procedures_defnoreturn" id="FrhP.v@2:JZ=^x?jnS$%" collapsed="true" x="9" y="1124">
    <field name="NAME">Switch Markets</field>
    <comment pinned="false" h="80" w="160">Describe this function...</comment>
    <statement name="STACK">
      <block type="controls_if" id="VGZPogGY0Set{6_anb^G" collapsed="true">
        <value name="IF0">
          <block type="variables_get" id="*Q:5xM72M0uH1c4n)z`m" collapsed="true">
            <field name="VAR" id="@VN/)JBDaz5d-aHKEiy:">Switch Markets</field>
          </block>
        </value>
        <statement name="DO0">
          <block type="controls_if" id="Ki6kTyGNE=5#A}+Gg,03" collapsed="true">
            <mutation xmlns="http://www.w3.org/1999/xhtml" elseif="9"></mutation>
            <value name="IF0">
              <block type="logic_compare" id=")RN1:VOnXI9Vk6.4roIe" collapsed="true">
                <field name="OP">EQ</field>
                <value name="A">
                  <block type="variables_get" id="T!R:J2x!dxRo]dj}O8(;" collapsed="true">
                    <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                  </block>
                </value>
                <value name="B">
                  <block type="math_number" id="^E=;m#1KAneN`S:G:2CS" collapsed="true">
                    <field name="NUM">0</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO0">
              <block type="active_symbol_changer" id="-ZxS^B8T}|,HpOC%(Q{b" collapsed="true">
                <field name="SYMBOL_ACTIVE_TYPE">disable</field>
                <next>
                  <block type="math_change" id="P}x0(E:6`/|(_Ea|xuO^" collapsed="true">
                    <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                    <value name="DELTA">
                      <shadow type="math_number" id="Kepl7mFD7Q+~%.k~f4{5" collapsed="true">
                        <field name="NUM">1</field>
                      </shadow>
                    </value>
                  </block>
                </next>
              </block>
            </statement>
            <value name="IF1">
              <block type="logic_compare" id="m5vX2MyIr4aZ2Z_SfSZZ" collapsed="true">
                <field name="OP">EQ</field>
                <value name="A">
                  <block type="variables_get" id="~D0j-xolT?r3DAkm@[yF" collapsed="true">
                    <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                  </block>
                </value>
                <value name="B">
                  <block type="math_number" id="mfil0y+(%k2~t|-?3EaY" collapsed="true">
                    <field name="NUM">1</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO1">
              <block type="math_change" id="JKDEDg;Ys7Y^UlZgyr`L" collapsed="true">
                <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                <value name="DELTA">
                  <shadow type="math_number" id="h=g9/nQ(=[M:tSxL{|Ee" collapsed="true">
                    <field name="NUM">1</field>
                  </shadow>
                </value>
                <next>
                  <block type="active_symbol_changer" id="NqArOhvF`dZYzINFUsKx" collapsed="true">
                    <field name="SYMBOL_ACTIVE_TYPE">R_10</field>
                  </block>
                </next>
              </block>
            </statement>
            <value name="IF2">
              <block type="logic_compare" id="5gT$!er~9CHL:IAap[Bl" collapsed="true">
                <field name="OP">EQ</field>
                <value name="A">
                  <block type="variables_get" id="^BT`2{m!}J^tc~Q,CJb9" collapsed="true">
                    <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                  </block>
                </value>
                <value name="B">
                  <block type="math_number" id="?VFC]H}/sT/Ox}+1AzNn" collapsed="true">
                    <field name="NUM">2</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO2">
              <block type="math_change" id="?hsN1hTjE#p#5U@@dARD" collapsed="true">
                <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                <value name="DELTA">
                  <shadow type="math_number" id="t+/l~tS0c56_.v+{6ve:" collapsed="true">
                    <field name="NUM">1</field>
                  </shadow>
                </value>
                <next>
                  <block type="active_symbol_changer" id="T%SI$7#V|!--$bT@Mv9D" collapsed="true">
                    <field name="SYMBOL_ACTIVE_TYPE">1HZ25V</field>
                  </block>
                </next>
              </block>
            </statement>
            <value name="IF3">
              <block type="logic_compare" id=":/zVQoL2!_bjTeDx[LR6" collapsed="true">
                <field name="OP">EQ</field>
                <value name="A">
                  <block type="variables_get" id="$+4bF,x2VOu(Z~h$9wHx" collapsed="true">
                    <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                  </block>
                </value>
                <value name="B">
                  <block type="math_number" id=":T.32sK|]}^Q1?f#;5?W" collapsed="true">
                    <field name="NUM">3</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO3">
              <block type="math_change" id="*s9r4kZgmsshX/`~7il." collapsed="true">
                <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                <value name="DELTA">
                  <shadow type="math_number" id="tt4#*_)2#g8Fe!i#FJq^" collapsed="true">
                    <field name="NUM">1</field>
                  </shadow>
                </value>
                <next>
                  <block type="active_symbol_changer" id="~BuR4g0t@I)aUy?+F?6f" collapsed="true">
                    <field name="SYMBOL_ACTIVE_TYPE">R_25</field>
                  </block>
                </next>
              </block>
            </statement>
            <value name="IF4">
              <block type="logic_compare" id="_c[I7*nq^TaqO|=F1sS|" collapsed="true">
                <field name="OP">EQ</field>
                <value name="A">
                  <block type="variables_get" id="BsT9ufUY=6Z`()~JYK_E" collapsed="true">
                    <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                  </block>
                </value>
                <value name="B">
                  <block type="math_number" id="ktQ-AOll^a3oeOzKl[n-" collapsed="true">
                    <field name="NUM">4</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO4">
              <block type="math_change" id="S5T^9^i[t$ui78zpcXhX" collapsed="true">
                <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                <value name="DELTA">
                  <shadow type="math_number" id="L~y-nLlv]J0%#!@cu9lG" collapsed="true">
                    <field name="NUM">1</field>
                  </shadow>
                </value>
                <next>
                  <block type="active_symbol_changer" id="d;UjX}NZt|ZI?o`dj(2M" collapsed="true">
                    <field name="SYMBOL_ACTIVE_TYPE">1HZ50V</field>
                  </block>
                </next>
              </block>
            </statement>
            <value name="IF5">
              <block type="logic_compare" id="(}%3CM*e%tc~Ws0)6S7X" collapsed="true">
                <field name="OP">EQ</field>
                <value name="A">
                  <block type="variables_get" id="[`P4=vqTD90T2;{b:3QX" collapsed="true">
                    <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                  </block>
                </value>
                <value name="B">
                  <block type="math_number" id="tN^-(Ge,0B[/%5ul,3ml" collapsed="true">
                    <field name="NUM">5</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO5">
              <block type="math_change" id="EgKJ*IuoZ+^%XeHuBS%s" collapsed="true">
                <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                <value name="DELTA">
                  <shadow type="math_number" id="DaE|Q#[}}Dliwpg;gcBa" collapsed="true">
                    <field name="NUM">1</field>
                  </shadow>
                </value>
                <next>
                  <block type="active_symbol_changer" id="0]U;{fm=H3gkmj[I4UBt" collapsed="true">
                    <field name="SYMBOL_ACTIVE_TYPE">R_50</field>
                  </block>
                </next>
              </block>
            </statement>
            <value name="IF6">
              <block type="logic_compare" id="ft{li;Mhn|D[PhJ!s~?5" collapsed="true">
                <field name="OP">EQ</field>
                <value name="A">
                  <block type="variables_get" id="5m*T?)fR*MmV$zp)VVM8" collapsed="true">
                    <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                  </block>
                </value>
                <value name="B">
                  <block type="math_number" id="OTg-f}eGcCi7LC3TY7if" collapsed="true">
                    <field name="NUM">6</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO6">
              <block type="math_change" id="e)P?$);8NQ08|,zw?L$2" collapsed="true">
                <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                <value name="DELTA">
                  <shadow type="math_number" id="DK8;=cbwYTU!`:j8@oCx" collapsed="true">
                    <field name="NUM">1</field>
                  </shadow>
                </value>
                <next>
                  <block type="active_symbol_changer" id=":o|g$E2[oc*-SnoE6j5v" collapsed="true">
                    <field name="SYMBOL_ACTIVE_TYPE">1HZ75V</field>
                  </block>
                </next>
              </block>
            </statement>
            <value name="IF7">
              <block type="logic_compare" id="|*cb5uT?#D/4qX9rwfOU" collapsed="true">
                <field name="OP">EQ</field>
                <value name="A">
                  <block type="variables_get" id="YQq|Mo=WtahBh%Z5;LQ^" collapsed="true">
                    <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                  </block>
                </value>
                <value name="B">
                  <block type="math_number" id="%BMHyxCJHIqKc:Qav1SD" collapsed="true">
                    <field name="NUM">7</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO7">
              <block type="math_change" id="(xi?s#+N:[Jnbp8ne~-q" collapsed="true">
                <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                <value name="DELTA">
                  <shadow type="math_number" id="M%l{m?_]xV1l*4x}F5}H" collapsed="true">
                    <field name="NUM">1</field>
                  </shadow>
                </value>
                <next>
                  <block type="active_symbol_changer" id="uUE8bKj!o!^IEOUpsq]." collapsed="true">
                    <field name="SYMBOL_ACTIVE_TYPE">R_75</field>
                  </block>
                </next>
              </block>
            </statement>
            <value name="IF8">
              <block type="logic_compare" id="^560qRq.HiF9s~qY-0J|" collapsed="true">
                <field name="OP">EQ</field>
                <value name="A">
                  <block type="variables_get" id="}RGyHH-OOT#Aj!g|6Jv1" collapsed="true">
                    <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                  </block>
                </value>
                <value name="B">
                  <block type="math_number" id="+rWG01YI]k`6a+*y*Rq[" collapsed="true">
                    <field name="NUM">8</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO8">
              <block type="math_change" id="?VTZTO?K~$j~=DoY*)h9" collapsed="true">
                <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                <value name="DELTA">
                  <shadow type="math_number" id="%oBAmNX:4b,g}jqhZQlR" collapsed="true">
                    <field name="NUM">1</field>
                  </shadow>
                </value>
                <next>
                  <block type="active_symbol_changer" id="JDlwY!|41d%Y%@iV|GWx" collapsed="true">
                    <field name="SYMBOL_ACTIVE_TYPE">1HZ100V</field>
                  </block>
                </next>
              </block>
            </statement>
            <value name="IF9">
              <block type="logic_compare" id="jr#1JwW4oZQy7gj.07jU" collapsed="true">
                <field name="OP">EQ</field>
                <value name="A">
                  <block type="variables_get" id="L1mLq.=%V369lY^EOgdz" collapsed="true">
                    <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                  </block>
                </value>
                <value name="B">
                  <block type="math_number" id="bOfynK:1K#@4R(r*2PkT" collapsed="true">
                    <field name="NUM">9</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO9">
              <block type="active_symbol_changer" id="GE6Cz/eu?nKq(;!dgtvt" collapsed="true">
                <field name="SYMBOL_ACTIVE_TYPE">R_100</field>
                <next>
                  <block type="variables_set" id="6|am|{Qan=}eJ4%WGqBc" collapsed="true">
                    <field name="VAR" id="Vd)a}rnFL$Mr`V[FG0ZX">Count</field>
                    <value name="VALUE">
                      <block type="math_number" id="e+T*@qF]C)8m^d[%!=4)" collapsed="true">
                        <field name="NUM">0</field>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </statement>
          </block>
        </statement>
      </block>
    </statement>
  </block>
  <block type="before_purchase" id="V/jf;#b@;*@3ATM]D)=;" collapsed="true" deletable="false" x="-6" y="1209">
    <statement name="BEFOREPURCHASE_STACK">
      <block type="btnotify" id="[YvAvgMZVg.g6q.0u*)b">
        <field name="NOTIFICATION_TYPE">success</field>
        <field name="NOTIFICATION_SOUND">silent</field>
        <value name="MESSAGE">
          <shadow type="text" id="{,xhxSc[k@0vZrXKn;1F">
            <field name="TEXT">Binarytool</field>
          </shadow>
          <block type="last_digit" id="psh`N-BD!]j-$Y,fuyU)"></block>
        </value>
        <next>
          <block type="procedures_callnoreturn" id="W]/99p4AY6,$8SPFGlj@" collapsed="true">
            <mutation xmlns="http://www.w3.org/1999/xhtml" name="Switch Markets"></mutation>
            <data>FrhP.v@2:JZ=^x?jnS$%</data>
            <next>
              <block type="controls_if" id="mYyd[CgSyC^,DQmrW4Ky">
                <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                <value name="IF0">
                  <block type="logic_compare" id="^q=9;xII*!2;6KbmAlUo">
                    <field name="OP">EQ</field>
                    <value name="A">
                      <block type="variables_get" id="Ti{gUwY=R:}{y1Hxd-Ql">
                        <field name="VAR" id="C_~~s:?,dwOS0*WOz9qL">Duke</field>
                      </block>
                    </value>
                    <value name="B">
                      <block type="text" id="lZgk](RAYzL8T%*%gb,K">
                        <field name="TEXT">good</field>
                      </block>
                    </value>
                  </block>
                </value>
                <statement name="DO0">
                  <block type="controls_if" id="8?FDv}V%LgoCD+dupd7.">
                    <value name="IF0">
                      <block type="logic_compare" id="W(l7hv294Etp@l*2ajzh">
                        <field name="OP">EQ</field>
                        <value name="A">
                          <block type="last_digit" id="_f8lL.IhfqATMF!]za|P"></block>
                        </value>
                        <value name="B">
                          <block type="variables_get" id="Lbk)q}6A6?O^?TkhC:*?">
                            <field name="VAR" id="m7(YQSH:pOaprkmLlkfZ">Entrypoint</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="apollo_purchase" id="9]u-%(i!DTj}(RSKn;~0">
                        <field name="PURCHASE_LIST">DIGITUNDER</field>
                      </block>
                    </statement>
                  </block>
                </statement>
                <statement name="ELSE">
                  <block type="apollo_purchase" id="#ZbP/K/c#SukVa,JPShf">
                    <field name="PURCHASE_LIST">DIGITUNDER</field>
                  </block>
                </statement>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
  </block>
</xml>