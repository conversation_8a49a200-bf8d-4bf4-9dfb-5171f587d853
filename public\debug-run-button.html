<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Debug Run Button - PromoClub</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                padding: 20px;
                background: #f5f5f5;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            }
            .debug-section {
                margin: 20px 0;
                padding: 15px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            .debug-section h3 {
                margin-top: 0;
                color: #333;
            }
            .status {
                padding: 10px;
                margin: 10px 0;
                border-radius: 5px;
            }
            .status.success {
                background: #d4edda;
                color: #155724;
            }
            .status.error {
                background: #f8d7da;
                color: #721c24;
            }
            .status.warning {
                background: #fff3cd;
                color: #856404;
            }
            .status.info {
                background: #d1ecf1;
                color: #0c5460;
            }
            button {
                background: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                margin: 5px;
            }
            button:hover {
                background: #0056b3;
            }
            button:disabled {
                background: #6c757d;
                cursor: not-allowed;
            }
            .code {
                background: #f8f9fa;
                padding: 10px;
                border-radius: 3px;
                font-family: monospace;
                white-space: pre-wrap;
                margin: 10px 0;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🔧 Run Button Debug Tool</h1>
            <p>This tool helps diagnose why the run button might not be working in your bot.</p>

            <div class="debug-section">
                <h3>🔍 Quick Diagnostics</h3>
                <button onclick="runDiagnostics()">Run All Diagnostics</button>
                <button onclick="checkWorkspace()">Check Workspace</button>
                <button onclick="checkValidation()">Check Validation</button>
                <button onclick="checkAuth()">Check Authentication</button>
                <div id="diagnostics-results"></div>
            </div>

            <div class="debug-section">
                <h3>📋 Manual Checks</h3>
                <div id="manual-checks">
                    <div class="status info">
                        <strong>Manual Checklist:</strong><br />
                        1. Are you logged into a Deriv account?<br />
                        2. Do you have a complete bot strategy loaded?<br />
                        3. Are all required blocks present (Trade Definition, Purchase Conditions, Apollo Purchase)?<br />
                        4. Are there any red error highlights on blocks?<br />
                        5. Is the run button visible and not grayed out?
                    </div>
                </div>
            </div>

            <div class="debug-section">
                <h3>🚨 Common Issues & Solutions</h3>
                <div class="status warning">
                    <strong>Issue 1: "Purchase block is mandatory" error</strong><br />
                    ✅ <em>Fixed:</em> Apollo Purchase is now supported as a valid purchase condition.
                </div>
                <div class="status info">
                    <strong>Issue 2: Authentication required</strong><br />
                    🔧 <em>Solution:</em> Make sure you're logged into your Deriv account.
                </div>
                <div class="status info">
                    <strong>Issue 3: Missing required blocks</strong><br />
                    🔧 <em>Solution:</em> Ensure your bot has Trade Definition, Purchase Conditions, and a Purchase
                    block.
                </div>
                <div class="status info">
                    <strong>Issue 4: Self-exclusion restrictions</strong><br />
                    🔧 <em>Solution:</em> Check if you have self-exclusion limits that prevent bot running.
                </div>
            </div>

            <div class="debug-section">
                <h3>🛠️ Advanced Debug</h3>
                <button onclick="inspectRunButton()">Inspect Run Button</button>
                <button onclick="checkConsoleErrors()">Check Console Errors</button>
                <button onclick="testBotValidation()">Test Bot Validation</button>
                <div id="advanced-results"></div>
            </div>
        </div>

        <script>
            function addResult(containerId, message, type = 'info') {
                const container = document.getElementById(containerId);
                const div = document.createElement('div');
                div.className = `status ${type}`;
                div.innerHTML = message;
                container.appendChild(div);
            }

            function clearResults(containerId) {
                document.getElementById(containerId).innerHTML = '';
            }

            function runDiagnostics() {
                clearResults('diagnostics-results');

                // Check if we're in the main app
                if (window.location.pathname === '/debug-run-button.html') {
                    addResult(
                        'diagnostics-results',
                        '⚠️ This debug tool should be run from the main app. Go to <a href="/">localhost:3000</a> and open browser console, then run: <code>window.debugRunButton()</code>',
                        'warning'
                    );
                    return;
                }

                checkAuth();
                checkWorkspace();
                checkValidation();
            }

            function checkAuth() {
                try {
                    // This will only work if we're in the main app context
                    if (typeof window !== 'undefined' && window.parent !== window) {
                        addResult('diagnostics-results', '🔍 Checking authentication...', 'info');
                        // We're in an iframe or different context
                        addResult(
                            'diagnostics-results',
                            '⚠️ Cannot check auth from this context. Open browser console on main app.',
                            'warning'
                        );
                    } else {
                        addResult('diagnostics-results', '🔍 Authentication check requires main app context', 'info');
                    }
                } catch (error) {
                    addResult('diagnostics-results', `❌ Auth check error: ${error.message}`, 'error');
                }
            }

            function checkWorkspace() {
                addResult('diagnostics-results', '🔍 Checking workspace...', 'info');

                try {
                    // This diagnostic script provides instructions for manual checking
                    addResult(
                        'diagnostics-results',
                        '📋 Workspace Check Instructions:<br>' +
                            '1. Go to the main app (localhost:3000)<br>' +
                            '2. Open browser console (F12)<br>' +
                            '3. Run: <code>window.Blockly?.derivWorkspace?.getAllBlocks()?.length</code><br>' +
                            '4. Should return a number > 0 if blocks are present',
                        'info'
                    );
                } catch (error) {
                    addResult('diagnostics-results', `❌ Workspace check error: ${error.message}`, 'error');
                }
            }

            function checkValidation() {
                addResult('diagnostics-results', '🔍 Checking validation...', 'info');

                addResult(
                    'diagnostics-results',
                    '📋 Validation Check Instructions:<br>' +
                        '1. In browser console, run: <code>window.dbot?.shouldRunBot?.()</code><br>' +
                        '2. Should return <code>true</code> if validation passes<br>' +
                        '3. If false, check for error messages in console',
                    'info'
                );
            }

            function inspectRunButton() {
                clearResults('advanced-results');

                addResult(
                    'advanced-results',
                    '🔍 Run Button Inspection:<br>' +
                        '1. In browser console, run: <code>document.querySelector("[data-testid*=run], .run-button, #run-button")</code><br>' +
                        '2. Check if button exists and is not disabled<br>' +
                        '3. Look for <code>disabled</code> attribute or <code>is_disabled</code> class',
                    'info'
                );
            }

            function checkConsoleErrors() {
                clearResults('advanced-results');

                addResult(
                    'advanced-results',
                    '🔍 Console Error Check:<br>' +
                        '1. Open browser console (F12)<br>' +
                        '2. Look for red error messages<br>' +
                        '3. Try clicking run button and watch for new errors<br>' +
                        '4. Common errors: "Purchase block is mandatory", "Authentication required", "Validation failed"',
                    'info'
                );
            }

            function testBotValidation() {
                clearResults('advanced-results');

                addResult(
                    'advanced-results',
                    '🔍 Bot Validation Test:<br>' +
                        'Run these commands in browser console:<br>' +
                        '<div class="code">' +
                        '// Check if bot skeleton is loaded\n' +
                        'console.log("Bot skeleton:", !!window.dbot);\n\n' +
                        '// Check workspace\n' +
                        'console.log("Workspace blocks:", window.Blockly?.derivWorkspace?.getAllBlocks()?.length);\n\n' +
                        '// Check validation\n' +
                        'console.log("Should run bot:", window.dbot?.shouldRunBot?.());\n\n' +
                        '// Check for required blocks\n' +
                        'const blocks = window.Blockly?.derivWorkspace?.getAllBlocks() || [];\n' +
                        'const blockTypes = blocks.map(b => b.type);\n' +
                        'console.log("Block types:", blockTypes);\n' +
                        'console.log("Has trade_definition:", blockTypes.includes("trade_definition"));\n' +
                        'console.log("Has before_purchase:", blockTypes.includes("before_purchase"));\n' +
                        'console.log("Has purchase or apollo_purchase:", \n' +
                        '  blockTypes.includes("purchase") || blockTypes.includes("apollo_purchase"));' +
                        '</div>',
                    'info'
                );
            }

            // Add global function for easy access from main app
            if (window.parent === window) {
                window.debugRunButton = runDiagnostics;
            }
        </script>
    </body>
</html>
