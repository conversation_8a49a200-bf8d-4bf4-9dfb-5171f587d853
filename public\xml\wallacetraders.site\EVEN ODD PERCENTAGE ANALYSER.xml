<xml xmlns="https://developers.google.com/blockly/xml" is_dbot="true" collection="false">
  <variables>
    <variable id="H++/hOJGICM!wzXVJwcD">TRADE EVEN:</variable>
    <variable id="p+RtTxpq59Q7E0Dl2}8M">TRADE ODD:</variable>
    <variable id="BQw@koGmjNEXM~N6=y)/">TRADE BOTH:</variable>
    <variable id="*S5@jR!:6fNu_jGh29XP">TARGET (PROFIT):</variable>
    <variable id="#zfdvs@F;$O(rYcB/,RJ">STOPLOSS:</variable>
    <variable id="8|3.Ys5C1.MEvri9FB64">TICKS:</variable>
    <variable id="l/g[COS_p$y={!V:[u*:">STAKE:</variable>
    <variable id=";T?)GYqQJ4wmU77mB_zh">USE STAKE LIST:</variable>
    <variable id="yPTHbp8J22cAev_KIKGk">STAKE LIST:</variable>
    <variable id="}PYb?[K00*b[IHmPgA7k">MARTINGALE:</variable>
    <variable id="X#UMfnUF|OeyRcxIN~Y4">MARTINGALE LEVEL:</variable>
    <variable id="yTo/m%iI-,efK,F98EZ$">NO. OF TICKS (DATA):</variable>
    <variable id="-sYH8a;P)v}g?!iatjL,">DO MARTINGALE AFTER (how many losses):</variable>
    <variable id="n?PC1yzq?YzC866oYh}f">Last Digit Lists</variable>
    <variable id="6QHr,opAh-;a$tGllUDJ">Stats Period (Ticks)</variable>
    <variable id="BPsrb}:o?nCP7t]1@Rfq">[Win Result]</variable>
    <variable id="dK=o~;7~9dr8wCZ%hyh,">Detail Profit</variable>
    <variable id="ddH0?/^#_8u1VjM/!`t|">Profit</variable>
    <variable id=";9K6*v|L#N4Va]fNqsaA">list</variable>
    <variable id="D-MHjLG(H:mwS[]`iA1^">Odd %</variable>
    <variable id="W,xwUTX9UvK.FPQ}k|S=">Even %</variable>
    <variable id="q533fsFRuhI2e%]C~96S">Ticks</variable>
    <variable id="XHCeH[LX7g55MJft#Q1/">STAKKLIST</variable>
    <variable id=":wo}_B]a6yW}vOnvOMKR">text4</variable>
    <variable id="RMbK)by1LEnp^9@HKC(*">Stake</variable>
    <variable id="+lq9`1Xb*zF^4~8]$@P[">list1</variable>
    <variable id="#S5]i9_E6vE_6t^_dYeJ">Continue</variable>
    <variable id="cDRqqU|$6T?`i0eVxf.k">SHXMMY</variable>
    <variable id="3,!j[HXimP@D1Vd:L+q8">LD List</variable>
    <variable id="!ovMYR)h8Erl`U2vcw!q">Trade Both</variable>
    <variable id="[~%IWPApAWAU4`63rDfr">MartiLossLevel</variable>
    <variable id="PXfESF:-T-83fn#h{O$X">PRICE ACTION</variable>
    <variable id="uO0Q.n*[}p6bW;C.h=|7">text1</variable>
    <variable id="r*7kR;1FtFFmko}y;6d:">Trade Even</variable>
    <variable id="!c%d*XBHYNv0h]4+]*nj">TargetProfit</variable>
    <variable id="F,f.PSpD@![!-ACOxasI">Even Do</variable>
    <variable id="1-?(UrcF,vkDI_~?$B^h">text</variable>
    <variable id="27yqn)@jLR#EPGhj1i~=">text3</variable>
    <variable id="^UotDwO-dLL5EPt6H-zL">STAKE LIST</variable>
    <variable id="n5(f~qa.-7fAJ0/kZ%tM">SECONDS DELAY AFTER TRADE</variable>
    <variable id="`:R7~|*x9_{.1.rTwqyS">Odd Do</variable>
    <variable id="v;(Tkg]E`,I@2ETMy[Iz">text2</variable>
    <variable id="4pu$olWHHv+#[Q}D24NA">Trade Odd</variable>
    <variable id="4,{gz4@piZ_i|(Hl,HH7">Seconds To Delay After Trade:</variable>
    <variable id="{@I1+WS#gB.i#;(p)NeF">LOSS LEVEL TO DELAY</variable>
    <variable id="Dxw=G5bQ$^NSTX_4oY]~">LD Count</variable>
    <variable id="RRH]M7%0M)Zi;ridyo7}">Win Count</variable>
    <variable id="^(Gy/YRFAMn6r6Ekap/J">Loss Count</variable>
    <variable id="p[1(Y1~sO^sSEcTby:I}">Loss Level To Delay:</variable>
    <variable id="m(@~SI$PI9_=9gVd*zl9">Stoploss</variable>
    <variable id="Nxx}Lsm5Mjf|D~2!Dj%V">MartiStart</variable>
    <variable id="k:EcRUy)u-26FkWUoszO">FirstStake</variable>
    <variable id="/VJ7O.)sAJmm47`,|W=v">Loss Level</variable>
    <variable id="ezm;s,8YY7`S_pxNx+{s">MartiFactor</variable>
  </variables>
  <block type="trade_definition" id="w+j5HM7viBquDj=AG9-x" deletable="false" x="0" y="110">
    <statement name="TRADE_OPTIONS">
      <block type="trade_definition_market" id="T=vqC0yXkYHS!4,ZCF.W" deletable="false" movable="false">
        <field name="MARKET_LIST">synthetic_index</field>
        <field name="SUBMARKET_LIST">random_index</field>
        <field name="SYMBOL_LIST">1HZ25V</field>
        <next>
          <block type="trade_definition_tradetype" id="H4-gV7(%z|OZ-NAIAw`a" deletable="false" movable="false">
            <field name="TRADETYPECAT_LIST">digits</field>
            <field name="TRADETYPE_LIST">evenodd</field>
            <next>
              <block type="trade_definition_contracttype" id="X4HyZayU1[3VJ/W11Sbi" deletable="false" movable="false">
                <field name="TYPE_LIST">both</field>
                <next>
                  <block type="trade_definition_candleinterval" id="N80Z5?h$zf,k~3s-,Jb}" deletable="false" movable="false">
                    <field name="CANDLEINTERVAL_LIST">60</field>
                    <next>
                      <block type="trade_definition_restartbuysell" id="t?*KT$IO1Ctqdi5?hoy3" deletable="false" movable="false">
                        <field name="TIME_MACHINE_ENABLED">FALSE</field>
                        <next>
                          <block type="trade_definition_restartonerror" id="viCbaMnC5PA5(5W8~?W#" deletable="false" movable="false">
                            <field name="RESTARTONERROR">TRUE</field>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="INITIALIZATION">
      <block type="lists_create_with" id="+ai8qm1on1KvBz:j[W~@">
        <field name="VARIABLE" id=";9K6*v|L#N4Va]fNqsaA">list</field>
        <statement name="STACK">
          <block type="lists_statement" id="MZY`$ek9xHBjx47D@d]8" movable="false">
            <value name="VALUE">
              <block type="math_number" id="`[4FLv8qWwGHoA$`AmUy">
                <field name="NUM">0.35</field>
              </block>
            </value>
            <next>
              <block type="lists_statement" id="65e`FP-6T/U**s!,s*e5" movable="false">
                <value name="VALUE">
                  <block type="math_number" id="~`11IA{n9NtH0iXgbKU5">
                    <field name="NUM">0.43</field>
                  </block>
                </value>
                <next>
                  <block type="lists_statement" id="W!*#DIF.2uy1WpmM`V3?" movable="false">
                    <value name="VALUE">
                      <block type="math_number" id="?qy+[i2(br~Rv4aw6}.6">
                        <field name="NUM">0.85</field>
                      </block>
                    </value>
                    <next>
                      <block type="lists_statement" id="vB)y7NhS7q,1{_uIo._E" movable="false">
                        <value name="VALUE">
                          <block type="math_number" id="S-07N|W#M{=E-Ddalkhx">
                            <field name="NUM">1.74</field>
                          </block>
                        </value>
                        <next>
                          <block type="lists_statement" id="h^TKo@W1USMa;W$q!m4c" movable="false">
                            <value name="VALUE">
                              <block type="math_number" id="i0/K?uVi8qr8+?B-IC!`">
                                <field name="NUM">3.55</field>
                              </block>
                            </value>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </statement>
        <next>
          <block type="procedures_callnoreturn" id="6kqc,H}]~Oh=KYPA%S3j">
            <mutation xmlns="http://www.w3.org/1999/xhtml" name="INVESTORBRAYO 0104270937 EVEN ODD ANALYSER">
              <arg name="TRADE EVEN:"></arg>
              <arg name="TRADE ODD:"></arg>
              <arg name="TRADE BOTH:"></arg>
              <arg name="TARGET (PROFIT):"></arg>
              <arg name="STOPLOSS:"></arg>
              <arg name="TICKS:"></arg>
              <arg name="STAKE:"></arg>
              <arg name="USE STAKE LIST:"></arg>
              <arg name="STAKE LIST:"></arg>
              <arg name="MARTINGALE:"></arg>
              <arg name="MARTINGALE LEVEL:"></arg>
              <arg name="NO. OF TICKS (DATA):"></arg>
              <arg name="DO MARTINGALE AFTER (how many losses):"></arg>
            </mutation>
            <value name="ARG0">
              <block type="logic_boolean" id="Nbz.|k02#*TPC|-4+BbH">
                <field name="BOOL">FALSE</field>
              </block>
            </value>
            <value name="ARG1">
              <block type="logic_boolean" id="/RYHfo%gi,-CJ-P6`UOS">
                <field name="BOOL">FALSE</field>
              </block>
            </value>
            <value name="ARG2">
              <block type="logic_boolean" id="/XX^hpzP7@CtDvs@f6.2">
                <field name="BOOL">TRUE</field>
              </block>
            </value>
            <value name="ARG3">
              <block type="math_number" id="FAzKjpiHK3.-W}@zgx9~">
                <field name="NUM">10</field>
              </block>
            </value>
            <value name="ARG4">
              <block type="math_number" id="~!9X6eAjv+Q,qPy/wig4">
                <field name="NUM">40</field>
              </block>
            </value>
            <value name="ARG5">
              <block type="math_number" id="ANP.QK4|7+dq@W1=L$zi">
                <field name="NUM">1</field>
              </block>
            </value>
            <value name="ARG6">
              <block type="math_number" id="aUmXK#rEc41v(SPh$:=t">
                <field name="NUM">0.35</field>
              </block>
            </value>
            <value name="ARG7">
              <block type="logic_boolean" id="NYL:?MMCrv(f]T|)E32e">
                <field name="BOOL">FALSE</field>
              </block>
            </value>
            <value name="ARG8">
              <block type="variables_get" id="MRt*EuF[+(k.FPUw]+up">
                <field name="VAR" id=";9K6*v|L#N4Va]fNqsaA">list</field>
              </block>
            </value>
            <value name="ARG9">
              <block type="math_number" id="-xcPRLl_*`/l-]#%:%^:">
                <field name="NUM">2.1</field>
              </block>
            </value>
            <value name="ARG10">
              <block type="math_number" id="4v^vEgT[.K5}INcGQ=(B">
                <field name="NUM">10</field>
              </block>
            </value>
            <value name="ARG11">
              <block type="math_number" id="(J!Xk^BhA|~M7YJ7!eHj">
                <field name="NUM">5</field>
              </block>
            </value>
            <value name="ARG12">
              <block type="math_number" id="P!gNql[z}zU^Z=VI4OTS">
                <field name="NUM">1</field>
              </block>
            </value>
            <next>
              <block type="variables_set" id="d?2$R^AtVA~p3R:o_zv;" collapsed="true">
                <field name="VAR" id="cDRqqU|$6T?`i0eVxf.k">SHXMMY</field>
                <value name="VALUE">
                  <block type="logic_boolean" id="]`$COx9zCKkT58u+wH*z" collapsed="true">
                    <field name="BOOL">FALSE</field>
                  </block>
                </value>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="SUBMARKET">
      <block type="controls_if" id="8Rua~#%ikk}06sV(m*bv" collapsed="true">
        <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
        <value name="IF0">
          <block type="logic_compare" id="cLIw8=mLO4E+UI4y$U?3" collapsed="true">
            <field name="OP">EQ</field>
            <value name="A">
              <block type="variables_get" id="8$:Fu3G8d@vBSeE#a4??" collapsed="true">
                <field name="VAR" id="XHCeH[LX7g55MJft#Q1/">STAKKLIST</field>
              </block>
            </value>
            <value name="B">
              <block type="logic_boolean" id="jhz,PmT3@D:7~~~XzO2N" collapsed="true">
                <field name="BOOL">TRUE</field>
              </block>
            </value>
          </block>
        </value>
        <statement name="DO0">
          <block type="variables_set" id=":*|4er%(p,y7ynK%WC5N" collapsed="true">
            <field name="VAR" id="cDRqqU|$6T?`i0eVxf.k">SHXMMY</field>
            <value name="VALUE">
              <block type="logic_boolean" id="glRH-^Ab^$wj%eg5sz=S" collapsed="true">
                <field name="BOOL">TRUE</field>
              </block>
            </value>
            <next>
              <block type="trade_definition_tradeoptions" id="S$F!0!K=v}}ZMMcgJZb!">
                <mutation xmlns="http://www.w3.org/1999/xhtml" has_first_barrier="false" has_second_barrier="false" has_prediction="false"></mutation>
                <field name="DURATIONTYPE_LIST">t</field>
                <value name="DURATION">
                  <shadow type="math_number" id="Ys|Q=EO|o.+0GRm|bPxC">
                    <field name="NUM">1</field>
                  </shadow>
                  <block type="procedures_callreturn" id="#BR]GP9l-pqo{oig.%C=">
                    <mutation xmlns="http://www.w3.org/1999/xhtml" name="[InvestorBrayo EVEN ODD ANALYSER] TICKS"></mutation>
                  </block>
                </value>
                <value name="AMOUNT">
                  <shadow type="math_number" id="i~%asrUSf=Gs=4*@]]tf">
                    <field name="NUM">0.35</field>
                  </shadow>
                  <block type="lists_getIndex" id="/RTZNTCl=Mt3[B_*lGru" collapsed="true">
                    <mutation xmlns="http://www.w3.org/1999/xhtml" statement="false" at="true"></mutation>
                    <field name="MODE">GET</field>
                    <field name="WHERE">FROM_START</field>
                    <value name="VALUE">
                      <block type="variables_get" id="X8~Mb.AZt^w%[x2g`eVY" collapsed="true">
                        <field name="VAR" id="^UotDwO-dLL5EPt6H-zL">STAKE LIST</field>
                      </block>
                    </value>
                    <value name="AT">
                      <block type="variables_get" id="SDgz]9*@P-aBDp7dJu(R" collapsed="true">
                        <field name="VAR" id="PXfESF:-T-83fn#h{O$X">PRICE ACTION</field>
                      </block>
                    </value>
                  </block>
                </value>
              </block>
            </next>
          </block>
        </statement>
        <statement name="ELSE">
          <block type="variables_set" id="6cRP/#k`w.vg}.V(1st6" collapsed="true">
            <field name="VAR" id="cDRqqU|$6T?`i0eVxf.k">SHXMMY</field>
            <value name="VALUE">
              <block type="logic_boolean" id="`stH*_W.!{o!wnxMZfiN" collapsed="true">
                <field name="BOOL">TRUE</field>
              </block>
            </value>
            <next>
              <block type="trade_definition_tradeoptions" id="{,HK4)6@UnHrYA|K-4qa">
                <mutation xmlns="http://www.w3.org/1999/xhtml" has_first_barrier="false" has_second_barrier="false" has_prediction="false"></mutation>
                <field name="DURATIONTYPE_LIST">t</field>
                <value name="DURATION">
                  <shadow type="math_number" id="h5IiPJ7gjvyH3T@mOHt6">
                    <field name="NUM">1</field>
                  </shadow>
                  <block type="procedures_callreturn" id="j7OyaQcE4b2X{BJH./3h">
                    <mutation xmlns="http://www.w3.org/1999/xhtml" name="[InvestorBrayo EVEN ODD ANALYSER] TICKS"></mutation>
                  </block>
                </value>
                <value name="AMOUNT">
                  <shadow type="math_number" id="aj`MpHbU99PpogS1S9LY">
                    <field name="NUM">0.35</field>
                  </shadow>
                  <block type="procedures_callreturn" id="j|Yj!(tdFvX:U2=+2^01">
                    <mutation xmlns="http://www.w3.org/1999/xhtml" name="[INVESTORBRAYO EOA] STAKE"></mutation>
                  </block>
                </value>
              </block>
            </next>
          </block>
        </statement>
      </block>
    </statement>
  </block>
  <block type="after_purchase" id="dg!X#$S#Tn`5dVWOd*rl" x="2785" y="144">
    <statement name="AFTERPURCHASE_STACK">
      <block type="math_change" id="tKSaA.P5q*),xVQs}?-L">
        <field name="VAR" id="ddH0?/^#_8u1VjM/!`t|">Profit</field>
        <value name="DELTA">
          <shadow type="math_number" id="[.TXxN=TEP;;Y{c0!U*a">
            <field name="NUM">1</field>
          </shadow>
          <block type="variables_get" id="0rxb!Fj1JlylJ~H-8na+" collapsed="true">
            <field name="VAR" id="dK=o~;7~9dr8wCZ%hyh,">Detail Profit</field>
          </block>
        </value>
        <next>
          <block type="variables_set" id="5kxr:7P7F]1f|%oc?Rr@">
            <field name="VAR" id="ddH0?/^#_8u1VjM/!`t|">Profit</field>
            <value name="VALUE">
              <block type="math_arithmetic" id="/?J?Za9n0WW4aN5sQJjJ" collapsed="true">
                <field name="OP">DIVIDE</field>
                <value name="A">
                  <shadow type="math_number" id="l6)mv+_Yw^9DOzkM)V=$">
                    <field name="NUM">1</field>
                  </shadow>
                  <block type="math_round" id="dXD?U6QPz-#gA1KD_iaF" collapsed="true">
                    <field name="OP">ROUND</field>
                    <value name="NUM">
                      <shadow type="math_number" id="O+1a-%9.GO.0Q^=V3#p}">
                        <field name="NUM">3.1</field>
                      </shadow>
                      <block type="math_arithmetic" id="kdTbB[;O-1%S7.AZ5]uX" collapsed="true">
                        <field name="OP">MULTIPLY</field>
                        <value name="A">
                          <shadow type="math_number" id="?/sXzwiBbWtD3l*w?p]G">
                            <field name="NUM">1</field>
                          </shadow>
                          <block type="variables_get" id="@@wJYp=0S{Q2G:tv{2Zj">
                            <field name="VAR" id="ddH0?/^#_8u1VjM/!`t|">Profit</field>
                          </block>
                        </value>
                        <value name="B">
                          <shadow type="math_number" id="zDPu}+IbW}r,jE$G:k);">
                            <field name="NUM">100</field>
                          </shadow>
                        </value>
                      </block>
                    </value>
                  </block>
                </value>
                <value name="B">
                  <shadow type="math_number" id="{a=4`i$m0_)8IX?)d)gR">
                    <field name="NUM">100</field>
                  </shadow>
                </value>
              </block>
            </value>
            <next>
              <block type="controls_if" id="zm69^@HsBMcVO94N_*RV" collapsed="true">
                <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                <value name="IF0">
                  <block type="contract_check_result" id="@o?)o;WvSR_!wP-~8eNc" collapsed="true">
                    <field name="CHECK_RESULT">win</field>
                  </block>
                </value>
                <statement name="DO0">
                  <block type="variables_set" id="8N;Y9?7*^#i|ZtmXaCN{" collapsed="true">
                    <field name="VAR" id="PXfESF:-T-83fn#h{O$X">PRICE ACTION</field>
                    <value name="VALUE">
                      <block type="math_number" id="v~pzXK{UKrBTHnokg]U9">
                        <field name="NUM">1</field>
                      </block>
                    </value>
                    <next>
                      <block type="text_join" id="=L4O}@I@#opGctNU!|6O">
                        <field name="VARIABLE" id="1-?(UrcF,vkDI_~?$B^h">text</field>
                        <statement name="STACK">
                          <block type="text_statement" id="~I_3ittA?Fg2avQYu5oe">
                            <value name="TEXT">
                              <shadow type="text" id="5ncpyXLQZj+|*{}9Q)Qy">
                                <field name="TEXT"></field>
                              </shadow>
                              <block type="text" id="fZ~b;*.OJ,k6=DHtFq$H" collapsed="true">
                                <field name="TEXT">WON || </field>
                              </block>
                            </value>
                            <next>
                              <block type="text_statement" id="3F~1aohD~1%Hg^c@iZYt">
                                <value name="TEXT">
                                  <shadow type="text" id="ZORb7vq#`*4@j*2{]p.=">
                                    <field name="TEXT"></field>
                                  </shadow>
                                  <block type="read_details" id="FS7ff!hC`_5[CqALTHx=" collapsed="true">
                                    <field name="DETAIL_INDEX">4</field>
                                  </block>
                                </value>
                                <next>
                                  <block type="text_statement" id="HOL9#QI[L{y+^#Og9Wu#">
                                    <value name="TEXT">
                                      <shadow type="text" id="~I~+1rlsx+eD7RfPO0Ta">
                                        <field name="TEXT"></field>
                                      </shadow>
                                      <block type="text" id="/4T5`NrXg@--bGL[fSQh" collapsed="true">
                                        <field name="TEXT"> [$]</field>
                                      </block>
                                    </value>
                                  </block>
                                </next>
                              </block>
                            </next>
                          </block>
                        </statement>
                        <next>
                          <block type="notify" id="~g$XN~U)YjqiN.Q?Jbl;" collapsed="true">
                            <field name="NOTIFICATION_TYPE">success</field>
                            <field name="NOTIFICATION_SOUND">silent</field>
                            <value name="MESSAGE">
                              <shadow type="text" id="1h2*`9M/Q+18aCrK%0Bf">
                                <field name="TEXT">abc</field>
                              </shadow>
                              <block type="variables_get" id="6|iH/nGHRA}#uu2_L*92" collapsed="true">
                                <field name="VAR" id="1-?(UrcF,vkDI_~?$B^h">text</field>
                              </block>
                            </value>
                            <next>
                              <block type="math_change" id="g@32k20a:}n7-G;e6z)K" collapsed="true">
                                <field name="VAR" id="RRH]M7%0M)Zi;ridyo7}">Win Count</field>
                                <value name="DELTA">
                                  <shadow type="math_number" id="Y)!b0EQNUA`zux@{QdW+">
                                    <field name="NUM">1</field>
                                  </shadow>
                                </value>
                                <next>
                                  <block type="variables_set" id="+8+87%EC-p8[1U,df=`n" collapsed="true">
                                    <field name="VAR" id="^(Gy/YRFAMn6r6Ekap/J">Loss Count</field>
                                    <value name="VALUE">
                                      <block type="math_number" id="6*cDZnn+}lOPj_Bs+Xzi">
                                        <field name="NUM">0</field>
                                      </block>
                                    </value>
                                    <next>
                                      <block type="variables_set" id="Q#7Q3sPKi,(3?97]d03g" collapsed="true">
                                        <field name="VAR" id="RMbK)by1LEnp^9@HKC(*">Stake</field>
                                        <value name="VALUE">
                                          <block type="variables_get" id="U(z8i|eq??pY9S(Z6|CV">
                                            <field name="VAR" id="k:EcRUy)u-26FkWUoszO">FirstStake</field>
                                          </block>
                                        </value>
                                      </block>
                                    </next>
                                  </block>
                                </next>
                              </block>
                            </next>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </statement>
                <statement name="ELSE">
                  <block type="text_join" id="RJix=,)KGBbb6Iy%4%D1" collapsed="true">
                    <field name="VARIABLE" id="uO0Q.n*[}p6bW;C.h=|7">text1</field>
                    <statement name="STACK">
                      <block type="text_statement" id="hUQ-%IH4PQ5^Ux.fiU]{">
                        <value name="TEXT">
                          <shadow type="text" id="YPd%-,Ir+/G5^K!E=mQ^">
                            <field name="TEXT"></field>
                          </shadow>
                          <block type="text" id="Qg4b;{OWPTd}aO!|Bcjk" collapsed="true">
                            <field name="TEXT">LOSS || </field>
                          </block>
                        </value>
                        <next>
                          <block type="text_statement" id="fX_VnrO*;oJQ#tzk3^_Y">
                            <value name="TEXT">
                              <shadow type="text" id="pw^z45I8$|9sA$zdA^Ay">
                                <field name="TEXT"></field>
                              </shadow>
                              <block type="read_details" id="?;*!B*YSFlcp2_;QK9#u" collapsed="true">
                                <field name="DETAIL_INDEX">4</field>
                              </block>
                            </value>
                            <next>
                              <block type="text_statement" id="R8UHdM;8:o;3j+nS2/1W">
                                <value name="TEXT">
                                  <shadow type="text" id="AE`w[?D/4)`weqaI1)?G">
                                    <field name="TEXT"></field>
                                  </shadow>
                                  <block type="text" id="g=jGsE9|wkh+|b-tQ8ZZ" collapsed="true">
                                    <field name="TEXT"> [$]</field>
                                  </block>
                                </value>
                              </block>
                            </next>
                          </block>
                        </next>
                      </block>
                    </statement>
                    <next>
                      <block type="notify" id="Md/I/]ZlY:H[THTmcx5]" collapsed="true">
                        <field name="NOTIFICATION_TYPE">error</field>
                        <field name="NOTIFICATION_SOUND">silent</field>
                        <value name="MESSAGE">
                          <shadow type="text" id="mFQMORcZ4}(iu^M8{hgL">
                            <field name="TEXT">abc</field>
                          </shadow>
                          <block type="variables_get" id="_,RuJu)%pu@$S{o?og|6" collapsed="true">
                            <field name="VAR" id="uO0Q.n*[}p6bW;C.h=|7">text1</field>
                          </block>
                        </value>
                        <next>
                          <block type="math_change" id="n[fMQ!Zs(*5:f|F}GL8L" collapsed="true">
                            <field name="VAR" id="PXfESF:-T-83fn#h{O$X">PRICE ACTION</field>
                            <value name="DELTA">
                              <shadow type="math_number" id="=D%EpmO(PJy)]GBaWaF9">
                                <field name="NUM">1</field>
                              </shadow>
                            </value>
                            <next>
                              <block type="math_change" id="ej:!H=o@OyePAkF1ERb1" collapsed="true">
                                <field name="VAR" id="^(Gy/YRFAMn6r6Ekap/J">Loss Count</field>
                                <value name="DELTA">
                                  <shadow type="math_number" id="?tq9|pnkP=1|3n2xkxZz">
                                    <field name="NUM">1</field>
                                  </shadow>
                                </value>
                                <next>
                                  <block type="variables_set" id="]VjIrZ|xWA@#c9N#?qZO" collapsed="true">
                                    <field name="VAR" id="RRH]M7%0M)Zi;ridyo7}">Win Count</field>
                                    <value name="VALUE">
                                      <block type="math_number" id="t!XxYEyI-@AAl3ZZ:#y0">
                                        <field name="NUM">0</field>
                                      </block>
                                    </value>
                                    <next>
                                      <block type="controls_if" id="PX}[`qCoxrN47r|90x-2" collapsed="true">
                                        <value name="IF0">
                                          <block type="logic_compare" id=")yUT4KB:Jn(E;3z!rzl%" collapsed="true">
                                            <field name="OP">GT</field>
                                            <value name="A">
                                              <block type="variables_get" id="8Ux|L8O^DM;e9LBIN:Hg" collapsed="true">
                                                <field name="VAR" id="^(Gy/YRFAMn6r6Ekap/J">Loss Count</field>
                                              </block>
                                            </value>
                                            <value name="B">
                                              <block type="variables_get" id="ln#(RsG4/{Gs@T,9}stJ" collapsed="true">
                                                <field name="VAR" id="/VJ7O.)sAJmm47`,|W=v">Loss Level</field>
                                              </block>
                                            </value>
                                          </block>
                                        </value>
                                        <statement name="DO0">
                                          <block type="variables_set" id="3#^X=46m]40nd#PY/TV=" collapsed="true">
                                            <field name="VAR" id="/VJ7O.)sAJmm47`,|W=v">Loss Level</field>
                                            <value name="VALUE">
                                              <block type="variables_get" id="I,J`ND:jrUw0whw0Yja@" collapsed="true">
                                                <field name="VAR" id="^(Gy/YRFAMn6r6Ekap/J">Loss Count</field>
                                              </block>
                                            </value>
                                          </block>
                                        </statement>
                                        <next>
                                          <block type="controls_if" id="Hj}U`:1V1,z;)Q^/HEJd" collapsed="true">
                                            <value name="IF0">
                                              <block type="logic_compare" id="df%4ZMzsN`R3*2*dJGC$" collapsed="true">
                                                <field name="OP">GTE</field>
                                                <value name="A">
                                                  <block type="variables_get" id="=ZQ@qGaDD_u/lE$S?)(Z" collapsed="true">
                                                    <field name="VAR" id="^(Gy/YRFAMn6r6Ekap/J">Loss Count</field>
                                                  </block>
                                                </value>
                                                <value name="B">
                                                  <block type="variables_get" id="HcOu9BNpNJ?U}h*)}%L," collapsed="true">
                                                    <field name="VAR" id="{@I1+WS#gB.i#;(p)NeF">LOSS LEVEL TO DELAY</field>
                                                  </block>
                                                </value>
                                              </block>
                                            </value>
                                            <statement name="DO0">
                                              <block type="variables_set" id="o1OiAftUI~d[H9FY+sQU" collapsed="true">
                                                <field name="VAR" id="RMbK)by1LEnp^9@HKC(*">Stake</field>
                                                <value name="VALUE">
                                                  <block type="math_arithmetic" id="_L(;?sF1]z}B*CY!IE27" collapsed="true">
                                                    <field name="OP">MULTIPLY</field>
                                                    <value name="A">
                                                      <shadow type="math_number" id="(WtZ1uys6(}:sS^`bEyb">
                                                        <field name="NUM">1</field>
                                                      </shadow>
                                                      <block type="variables_get" id="/g1/x~(`a!Z/(k^sdSeU" collapsed="true">
                                                        <field name="VAR" id="RMbK)by1LEnp^9@HKC(*">Stake</field>
                                                      </block>
                                                    </value>
                                                    <value name="B">
                                                      <shadow type="math_number" id="I9u)p}e=#s_U@GC?*JTe">
                                                        <field name="NUM">1</field>
                                                      </shadow>
                                                      <block type="variables_get" id="j1Dn~ccc?{.;kGT]6$Aj" collapsed="true">
                                                        <field name="VAR" id="ezm;s,8YY7`S_pxNx+{s">MartiFactor</field>
                                                      </block>
                                                    </value>
                                                  </block>
                                                </value>
                                                <next>
                                                  <block type="timeout" id="ygM%567AEJpKjWZYhzml" collapsed="true">
                                                    <statement name="TIMEOUTSTACK">
                                                      <block type="trade_again" id="~V/5*x#lRQPi*hH-O18Y" collapsed="true"></block>
                                                    </statement>
                                                    <value name="SECONDS">
                                                      <shadow type="math_number" id="n.z`D1r[Lw,MT~5kA^wd">
                                                        <field name="NUM">1</field>
                                                      </shadow>
                                                      <block type="variables_get" id="T+L`Izkov;Dm~Fjy9{V+" collapsed="true">
                                                        <field name="VAR" id="n5(f~qa.-7fAJ0/kZ%tM">SECONDS DELAY AFTER TRADE</field>
                                                      </block>
                                                    </value>
                                                  </block>
                                                </next>
                                              </block>
                                            </statement>
                                            <next>
                                              <block type="controls_if" id="9dJ/^pom[#6PRy2YxE]L" collapsed="true">
                                                <value name="IF0">
                                                  <block type="logic_compare" id="fDtDgM[ZuIj|j5Dx%bld" collapsed="true">
                                                    <field name="OP">GT</field>
                                                    <value name="A">
                                                      <block type="variables_get" id="r]#s8QmyK,N./6NikiAi">
                                                        <field name="VAR" id="[~%IWPApAWAU4`63rDfr">MartiLossLevel</field>
                                                      </block>
                                                    </value>
                                                    <value name="B">
                                                      <block type="math_number" id="M=QnQJaA#Jk?G)ba/{:[">
                                                        <field name="NUM">0</field>
                                                      </block>
                                                    </value>
                                                  </block>
                                                </value>
                                                <statement name="DO0">
                                                  <block type="controls_if" id=";Gh[L-CQvgPFwFMK5wrY" collapsed="true">
                                                    <value name="IF0">
                                                      <block type="logic_operation" id="A(;s(p=Z@JkNVVUQmo}x" collapsed="true">
                                                        <field name="OP">AND</field>
                                                        <value name="A">
                                                          <block type="logic_compare" id="5ZyoXu{uIQW$kTA3[~Rj" collapsed="true">
                                                            <field name="OP">GT</field>
                                                            <value name="A">
                                                              <block type="variables_get" id="xB:eHIb.DVNNQwZ|Ne9J">
                                                                <field name="VAR" id="Nxx}Lsm5Mjf|D~2!Dj%V">MartiStart</field>
                                                              </block>
                                                            </value>
                                                            <value name="B">
                                                              <block type="math_number" id="6wy;Tw/fGH~Bp84*+Dc+">
                                                                <field name="NUM">0</field>
                                                              </block>
                                                            </value>
                                                          </block>
                                                        </value>
                                                        <value name="B">
                                                          <block type="logic_compare" id="B[fX[z9j}|m{D$!J%=Qf" collapsed="true">
                                                            <field name="OP">GTE</field>
                                                            <value name="A">
                                                              <block type="variables_get" id="1XKD#y@y{_pQsecPwHgp">
                                                                <field name="VAR" id="^(Gy/YRFAMn6r6Ekap/J">Loss Count</field>
                                                              </block>
                                                            </value>
                                                            <value name="B">
                                                              <block type="variables_get" id="4nXc3}`r*{Mprr3b1]%L">
                                                                <field name="VAR" id="Nxx}Lsm5Mjf|D~2!Dj%V">MartiStart</field>
                                                              </block>
                                                            </value>
                                                          </block>
                                                        </value>
                                                      </block>
                                                    </value>
                                                    <statement name="DO0">
                                                      <block type="controls_if" id="u%Dtv5`y90`.XI5Z-MT]" collapsed="true">
                                                        <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                                                        <value name="IF0">
                                                          <block type="logic_compare" id="@aqwvoL;p#b_@8?-*RTl" collapsed="true">
                                                            <field name="OP">LTE</field>
                                                            <value name="A">
                                                              <block type="variables_get" id="vsir4C-0urm*lA3i%MW|">
                                                                <field name="VAR" id="^(Gy/YRFAMn6r6Ekap/J">Loss Count</field>
                                                              </block>
                                                            </value>
                                                            <value name="B">
                                                              <block type="variables_get" id="}_NdGBK`_/lLon,Boq{z">
                                                                <field name="VAR" id="[~%IWPApAWAU4`63rDfr">MartiLossLevel</field>
                                                              </block>
                                                            </value>
                                                          </block>
                                                        </value>
                                                        <statement name="DO0">
                                                          <block type="variables_set" id=",CAhfghBQA-PS@|f=({$" collapsed="true">
                                                            <field name="VAR" id="RMbK)by1LEnp^9@HKC(*">Stake</field>
                                                            <value name="VALUE">
                                                              <block type="math_arithmetic" id="=lOqB{^[e%_w7T?d|o3e" collapsed="true">
                                                                <field name="OP">MULTIPLY</field>
                                                                <value name="A">
                                                                  <shadow type="math_number" id="OH,3w3OE2=u84Qh(k*a?">
                                                                    <field name="NUM">1</field>
                                                                  </shadow>
                                                                  <block type="variables_get" id="B`gq.;g97BaluC3km2Wv" collapsed="true">
                                                                    <field name="VAR" id="RMbK)by1LEnp^9@HKC(*">Stake</field>
                                                                  </block>
                                                                </value>
                                                                <value name="B">
                                                                  <shadow type="math_number" id="#U,nCizvK]C_%hb#Swp!">
                                                                    <field name="NUM">1</field>
                                                                  </shadow>
                                                                  <block type="variables_get" id="`j~3RKiV={(+K^Z)nm@t" collapsed="true">
                                                                    <field name="VAR" id="ezm;s,8YY7`S_pxNx+{s">MartiFactor</field>
                                                                  </block>
                                                                </value>
                                                              </block>
                                                            </value>
                                                          </block>
                                                        </statement>
                                                        <statement name="ELSE">
                                                          <block type="notify" id="zWG%yFK^3e@z3dP%/i%}" collapsed="true">
                                                            <field name="NOTIFICATION_TYPE">info</field>
                                                            <field name="NOTIFICATION_SOUND">silent</field>
                                                            <value name="MESSAGE">
                                                              <block type="text" id="(OdgnXPzaa[6W?t5n9lD">
                                                                <field name="TEXT">Martingale Reset</field>
                                                              </block>
                                                            </value>
                                                            <next>
                                                              <block type="variables_set" id="i=,7SZX/,D]hBa4+f0P?" collapsed="true">
                                                                <field name="VAR" id="RMbK)by1LEnp^9@HKC(*">Stake</field>
                                                                <value name="VALUE">
                                                                  <block type="variables_get" id="hhog-@6YboASxS1+|f*H">
                                                                    <field name="VAR" id="k:EcRUy)u-26FkWUoszO">FirstStake</field>
                                                                  </block>
                                                                </value>
                                                              </block>
                                                            </next>
                                                          </block>
                                                        </statement>
                                                      </block>
                                                    </statement>
                                                  </block>
                                                </statement>
                                              </block>
                                            </next>
                                          </block>
                                        </next>
                                      </block>
                                    </next>
                                  </block>
                                </next>
                              </block>
                            </next>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </statement>
                <next>
                  <block type="controls_if" id="utT~55wbt3cc}6`}}`]G">
                    <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                    <value name="IF0">
                      <block type="logic_compare" id="2i8D$nhqzeC#rnsY-q%6">
                        <field name="OP">LT</field>
                        <value name="A">
                          <block type="total_profit" id="F!zv*|Xz(Xlm6%Ae3v3~"></block>
                        </value>
                        <value name="B">
                          <block type="variables_get" id="d+p{{o}|eHB%Y2kt+$~V">
                            <field name="VAR" id="!c%d*XBHYNv0h]4+]*nj">TargetProfit</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="controls_if" id="]MiO5pM]|,)`2s9edhRM" collapsed="true">
                        <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                        <value name="IF0">
                          <block type="logic_operation" id="TLpVLaOzPbm1|ro{5Mj?">
                            <field name="OP">AND</field>
                            <value name="A">
                              <block type="math_number_property" id="Rd10i0UX|4R=R0NLMvKC">
                                <mutation xmlns="http://www.w3.org/1999/xhtml" divisor_input="false"></mutation>
                                <field name="PROPERTY">NEGATIVE</field>
                                <value name="NUMBER_TO_CHECK">
                                  <shadow type="math_number" id=",Z5c.0K@vO=SylQD[_bs">
                                    <field name="NUM">0</field>
                                  </shadow>
                                  <block type="total_profit" id="xa=g/|0l{bmr15Yz@XY`"></block>
                                </value>
                              </block>
                            </value>
                            <value name="B">
                              <block type="logic_compare" id="Q$Z%?hmc!Zg3d+CR{-Iq">
                                <field name="OP">GT</field>
                                <value name="A">
                                  <block type="math_single" id="%,@vvVXxSsOUapugK~!`">
                                    <field name="OP">ABS</field>
                                    <value name="NUM">
                                      <shadow type="math_number" id="/gJU{UXdQ}R*ltASFN:-">
                                        <field name="NUM">9</field>
                                      </shadow>
                                      <block type="total_profit" id=":ya-d9wlBl3t_d=]w_@c"></block>
                                    </value>
                                  </block>
                                </value>
                                <value name="B">
                                  <block type="variables_get" id="+^2gnGNtPRq]xZBYkp[w">
                                    <field name="VAR" id="m(@~SI$PI9_=9gVd*zl9">Stoploss</field>
                                  </block>
                                </value>
                              </block>
                            </value>
                          </block>
                        </value>
                        <statement name="DO0">
                          <block type="text_join" id="~O[cf}ie`yPl_rKFqWv0">
                            <field name="VARIABLE" id="v;(Tkg]E`,I@2ETMy[Iz">text2</field>
                            <statement name="STACK">
                              <block type="text_statement" id="[tq$ysJ1I[Urcs|oCewt">
                                <value name="TEXT">
                                  <shadow type="text" id="X=K27S/LkzgW:f;sa:Qn">
                                    <field name="TEXT"></field>
                                  </shadow>
                                  <block type="text" id="#*M%pcijYQ|=AE,]FQz,">
                                    <field name="TEXT">🛑! Stop Loss Hit😓</field>
                                  </block>
                                </value>
                                <next>
                                  <block type="text_statement" id="x~V];nGo!{S~1_LPeq9[">
                                    <value name="TEXT">
                                      <shadow type="text" id="9aRM+4poGQx/2}R)1*+_">
                                        <field name="TEXT"></field>
                                      </shadow>
                                      <block type="total_profit" id="}QJ(]AvS/{il,{J?{L6u"></block>
                                    </value>
                                    <next>
                                      <block type="text_statement" id="93eu^UwKf4d/kd6Kz@G+">
                                        <value name="TEXT">
                                          <shadow type="text" id="!r}Eaz+m^#JRWnIypEV^">
                                            <field name="TEXT"></field>
                                          </shadow>
                                          <block type="text" id="Iu2H-LGE1}Eq1KSu-3+I">
                                            <field name="TEXT">Avoid Emotions😉RE-ENTER MARKET.</field>
                                          </block>
                                        </value>
                                      </block>
                                    </next>
                                  </block>
                                </next>
                              </block>
                            </statement>
                            <next>
                              <block type="text_print" id="0t2;?sdUr4[[zC[KtEq@">
                                <value name="TEXT">
                                  <shadow type="text" id="J+k(QA,zK/etU}u$4b=j">
                                    <field name="TEXT">abc</field>
                                  </shadow>
                                  <block type="variables_get" id="(V[)}/c)*chY*ymLHmS6">
                                    <field name="VAR" id="v;(Tkg]E`,I@2ETMy[Iz">text2</field>
                                  </block>
                                </value>
                              </block>
                            </next>
                          </block>
                        </statement>
                        <statement name="ELSE">
                          <block type="trade_again" id="c6eUafsvfgRteM/HGajg"></block>
                        </statement>
                      </block>
                    </statement>
                    <statement name="ELSE">
                      <block type="text_join" id="*TfvsD_xk$%m]{hUM8P5" collapsed="true">
                        <field name="VARIABLE" id="27yqn)@jLR#EPGhj1i~=">text3</field>
                        <statement name="STACK">
                          <block type="text_statement" id="+HG+_VFLJoOX;oG7%QO3">
                            <value name="TEXT">
                              <shadow type="text" id="t$QhWIwMha7jp,QoBQSu">
                                <field name="TEXT"></field>
                              </shadow>
                              <block type="text" id="%FhN*A[4}@4OB0Rl.kvS">
                                <field name="TEXT">💲Congratulations! TP Hitted💰</field>
                              </block>
                            </value>
                            <next>
                              <block type="text_statement" id="Mdv3*3*%W.(5^mIu1CpR">
                                <value name="TEXT">
                                  <shadow type="text" id="R|d7*ADccQUyQbe*6E:Z">
                                    <field name="TEXT"></field>
                                  </shadow>
                                  <block type="total_profit" id="C5iWW%9NY`Zy%RZCRkU$"></block>
                                </value>
                                <next>
                                  <block type="text_statement" id="I21L,tr$%R,#:K~AA8`)">
                                    <value name="TEXT">
                                      <shadow type="text" id="T,=L/g}t]s})w`[(z)I_">
                                        <field name="TEXT"></field>
                                      </shadow>
                                      <block type="text" id="HR]`Kxhy3#(1+Ut:]d^Q">
                                        <field name="TEXT">INVESTORBRAYO EVEN ODD ANALYSER 👌</field>
                                      </block>
                                    </value>
                                  </block>
                                </next>
                              </block>
                            </next>
                          </block>
                        </statement>
                        <next>
                          <block type="text_print" id="T]a{-P3wP$E%C0e$%+D8" collapsed="true">
                            <value name="TEXT">
                              <shadow type="text" id="5e[vaop9!Z]EU]U+Be#a">
                                <field name="TEXT">abc</field>
                              </shadow>
                              <block type="variables_get" id="v0~qxyQ7IH.k_ll9I+Lv">
                                <field name="VAR" id="27yqn)@jLR#EPGhj1i~=">text3</field>
                              </block>
                            </value>
                          </block>
                        </next>
                      </block>
                    </statement>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
  </block>
  <block type="before_purchase" id="DL|6fBl-Y98(8$YjG/Qu" deletable="false" x="0" y="1136">
    <statement name="BEFOREPURCHASE_STACK">
      <block type="controls_if" id="Nx]9WtIp=L%B4oLygFzB">
        <value name="IF0">
          <block type="logic_compare" id="1WS_eL50_-gU,q#+6p0Z" collapsed="true">
            <field name="OP">EQ</field>
            <value name="A">
              <block type="variables_get" id="!)jpgFcu59-GgK:n*y?r" collapsed="true">
                <field name="VAR" id="!ovMYR)h8Erl`U2vcw!q">Trade Both</field>
              </block>
            </value>
            <value name="B">
              <block type="logic_boolean" id="~Iz2kdSAx])Wu-u}na|d" collapsed="true">
                <field name="BOOL">TRUE</field>
              </block>
            </value>
          </block>
        </value>
        <statement name="DO0">
          <block type="controls_if" id="HxdQcYf*5@D:XKob{.IH">
            <mutation xmlns="http://www.w3.org/1999/xhtml" elseif="1"></mutation>
            <value name="IF0">
              <block type="logic_compare" id="I!ZzWY=r-JT[O{52^%_=" collapsed="true">
                <field name="OP">GT</field>
                <value name="A">
                  <block type="variables_get" id="lULqz`aoj7##ZT-#u;O}" collapsed="true">
                    <field name="VAR" id="W,xwUTX9UvK.FPQ}k|S=">Even %</field>
                  </block>
                </value>
                <value name="B">
                  <block type="variables_get" id="t%HD.:T96aIq_?qK]Uu3" collapsed="true">
                    <field name="VAR" id="D-MHjLG(H:mwS[]`iA1^">Odd %</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO0">
              <block type="purchase" id="jJwrk`_)s.fWCMJC.Rns">
                <field name="PURCHASE_LIST">DIGITEVEN</field>
              </block>
            </statement>
            <value name="IF1">
              <block type="logic_compare" id="7(rLVtl3puthP1NcwD3Q" collapsed="true">
                <field name="OP">LT</field>
                <value name="A">
                  <block type="variables_get" id="[nD`+{0uqZcPY1/w~$fE" collapsed="true">
                    <field name="VAR" id="W,xwUTX9UvK.FPQ}k|S=">Even %</field>
                  </block>
                </value>
                <value name="B">
                  <block type="variables_get" id="T}MuM#KK1HQRbLWrTaQw" collapsed="true">
                    <field name="VAR" id="D-MHjLG(H:mwS[]`iA1^">Odd %</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO1">
              <block type="purchase" id="#GgnRq5Zo@TS-1)WR2~m">
                <field name="PURCHASE_LIST">DIGITODD</field>
              </block>
            </statement>
          </block>
        </statement>
        <next>
          <block type="controls_if" id="S|pD[Bl!?)soS15?3{$/">
            <value name="IF0">
              <block type="logic_compare" id="Fjz2my[D[(gSgJNLMARq" collapsed="true">
                <field name="OP">EQ</field>
                <value name="A">
                  <block type="variables_get" id="-~zya(x_tT7lmN24g)bj" collapsed="true">
                    <field name="VAR" id="r*7kR;1FtFFmko}y;6d:">Trade Even</field>
                  </block>
                </value>
                <value name="B">
                  <block type="logic_boolean" id="EHI4E1q||HZ.=}_ua3^R" collapsed="true">
                    <field name="BOOL">TRUE</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO0">
              <block type="controls_if" id="uQUM%2qs7m;*5W0slsO4">
                <value name="IF0">
                  <block type="logic_compare" id="#SYa5g|#b!Bz|/(E-p$?" collapsed="true">
                    <field name="OP">GT</field>
                    <value name="A">
                      <block type="variables_get" id="90[r$U|RRB=Aqobo`C55" collapsed="true">
                        <field name="VAR" id="W,xwUTX9UvK.FPQ}k|S=">Even %</field>
                      </block>
                    </value>
                    <value name="B">
                      <block type="variables_get" id="3jK=nk=2eTf}3|IFrnrT" collapsed="true">
                        <field name="VAR" id="D-MHjLG(H:mwS[]`iA1^">Odd %</field>
                      </block>
                    </value>
                  </block>
                </value>
                <statement name="DO0">
                  <block type="controls_if" id="6]@ymF:rPY68ac,MQ]jQ">
                    <value name="IF0">
                      <block type="logic_compare" id="q5A(45i6bIQjV|EB`O:*" collapsed="true">
                        <field name="OP">EQ</field>
                        <value name="A">
                          <block type="variables_get" id="T7-pO/{:o=ZRZ8!1@Oew" collapsed="true">
                            <field name="VAR" id="4pu$olWHHv+#[Q}D24NA">Trade Odd</field>
                          </block>
                        </value>
                        <value name="B">
                          <block type="logic_boolean" id="oJ0ZrS45c_ne-0j1.ovj" collapsed="true">
                            <field name="BOOL">TRUE</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="controls_if" id="kFexv;[rUu$V_DKyWq9F">
                        <value name="IF0">
                          <block type="logic_compare" id="~)X]DRymt~1kovGL/KH8" collapsed="true">
                            <field name="OP">LT</field>
                            <value name="A">
                              <block type="variables_get" id=")pYie5F~Q!r`@cij!7Tt" collapsed="true">
                                <field name="VAR" id="W,xwUTX9UvK.FPQ}k|S=">Even %</field>
                              </block>
                            </value>
                            <value name="B">
                              <block type="variables_get" id="rWUY#V1NaG]$(ir%d^8." collapsed="true">
                                <field name="VAR" id="D-MHjLG(H:mwS[]`iA1^">Odd %</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <statement name="DO0">
                          <block type="purchase" id="Qr=X]`FuOmBT1Zex0qf.">
                            <field name="PURCHASE_LIST">DIGITODD</field>
                          </block>
                        </statement>
                      </block>
                    </statement>
                    <next>
                      <block type="purchase" id="62q`mo^?a4$m^~*~Q~=s">
                        <field name="PURCHASE_LIST">DIGITEVEN</field>
                      </block>
                    </next>
                  </block>
                </statement>
              </block>
            </statement>
          </block>
        </next>
      </block>
    </statement>
  </block>
  <block type="tick_analysis" id="^R%d8)m}K-9S};$34~-Z" x="0" y="2014">
    <statement name="TICKANALYSIS_STACK">
      <block type="procedures_callnoreturn" id="5z,sq^7jDJBp_aW2peas">
        <mutation xmlns="http://www.w3.org/1999/xhtml" name="Reading InvestorBrayo Digit Analysis System">
          <arg name="Last Digit Lists"></arg>
          <arg name="Stats Period (Ticks)"></arg>
        </mutation>
        <value name="ARG0">
          <block type="lastDigitList" id="kz{.AIX/HUNi5Y{$1N=N"></block>
        </value>
        <value name="ARG1">
          <block type="variables_get" id="~N4G.R,xpZVu8LFj7MRs" collapsed="true">
            <field name="VAR" id="6QHr,opAh-;a$tGllUDJ">Stats Period (Ticks)</field>
          </block>
        </value>
      </block>
    </statement>
  </block>
  <block type="procedures_defreturn" id="5twL~!p%N)rR77jh)mN@" collapsed="true" x="0" y="2190">
    <field name="NAME">Odd (%)</field>
    <comment pinned="false" h="80" w="160">Describe this function...</comment>
    <value name="RETURN">
      <block type="variables_get" id="E;Uk!#zH`XG[l0N?L;8D" collapsed="true">
        <field name="VAR" id="D-MHjLG(H:mwS[]`iA1^">Odd %</field>
      </block>
    </value>
  </block>
  <block type="procedures_defreturn" id="O{RJ1qrP;L0Qta:~I[sw" collapsed="true" x="0" y="2286">
    <field name="NAME">Even (%)</field>
    <comment pinned="false" h="80" w="160">Describe this function...</comment>
    <value name="RETURN">
      <block type="variables_get" id="/}pvV9F6LkF.n*[=)J=G" collapsed="true">
        <field name="VAR" id="W,xwUTX9UvK.FPQ}k|S=">Even %</field>
      </block>
    </value>
  </block>
  <block type="procedures_defreturn" id="#:C@futpbQm1h2S1R;]j" collapsed="true" x="0" y="2382">
    <field name="NAME">[InvestorBrayo EVEN ODD ANALYSER] TICKS</field>
    <comment pinned="false" h="80" w="160">Describe this function...</comment>
    <value name="RETURN">
      <block type="variables_get" id="JkXN^_(G,yMv3D|doM}%" collapsed="true">
        <field name="VAR" id="q533fsFRuhI2e%]C~96S">Ticks</field>
      </block>
    </value>
  </block>
  <block type="procedures_defnoreturn" id="{9Ow!C_{EP[)(Q;9Qa)v" collapsed="true" x="-8" y="2440">
    <mutation xmlns="http://www.w3.org/1999/xhtml">
      <arg name="TRADE EVEN:" varid="H++/hOJGICM!wzXVJwcD"></arg>
      <arg name="TRADE ODD:" varid="p+RtTxpq59Q7E0Dl2}8M"></arg>
      <arg name="TRADE BOTH:" varid="BQw@koGmjNEXM~N6=y)/"></arg>
      <arg name="TARGET (PROFIT):" varid="*S5@jR!:6fNu_jGh29XP"></arg>
      <arg name="STOPLOSS:" varid="#zfdvs@F;$O(rYcB/,RJ"></arg>
      <arg name="TICKS:" varid="8|3.Ys5C1.MEvri9FB64"></arg>
      <arg name="STAKE:" varid="l/g[COS_p$y={!V:[u*:"></arg>
      <arg name="USE STAKE LIST:" varid=";T?)GYqQJ4wmU77mB_zh"></arg>
      <arg name="STAKE LIST:" varid="yPTHbp8J22cAev_KIKGk"></arg>
      <arg name="MARTINGALE:" varid="}PYb?[K00*b[IHmPgA7k"></arg>
      <arg name="MARTINGALE LEVEL:" varid="X#UMfnUF|OeyRcxIN~Y4"></arg>
      <arg name="NO. OF TICKS (DATA):" varid="yTo/m%iI-,efK,F98EZ$"></arg>
      <arg name="DO MARTINGALE AFTER (how many losses):" varid="-sYH8a;P)v}g?!iatjL,"></arg>
    </mutation>
    <field name="NAME">INVESTORBRAYO 0104270937 EVEN ODD ANALYSER</field>
    <comment pinned="false" h="80" w="160">Describe this function...</comment>
    <statement name="STACK">
      <block type="variables_set" id="D{ztWJ{}ZptV-c;nU=G/" collapsed="true">
        <field name="VAR" id="XHCeH[LX7g55MJft#Q1/">STAKKLIST</field>
        <value name="VALUE">
          <block type="variables_get" id="X3d(e}I2!b!0GZbI{4w$" collapsed="true">
            <field name="VAR" id=";T?)GYqQJ4wmU77mB_zh">USE STAKE LIST:</field>
          </block>
        </value>
        <next>
          <block type="variables_set" id="n4?sQJQvl2PxHh:iW/Lx" collapsed="true">
            <field name="VAR" id="6QHr,opAh-;a$tGllUDJ">Stats Period (Ticks)</field>
            <value name="VALUE">
              <block type="variables_get" id="CqEX%`X?)31}6l=oaLr6" collapsed="true">
                <field name="VAR" id="yTo/m%iI-,efK,F98EZ$">NO. OF TICKS (DATA):</field>
              </block>
            </value>
            <next>
              <block type="variables_set" id="SNUrE^h*{VnscRk7Gj^d" collapsed="true">
                <field name="VAR" id="[~%IWPApAWAU4`63rDfr">MartiLossLevel</field>
                <value name="VALUE">
                  <block type="variables_get" id="~jsC`-~?h?v]YdsBCoQQ" collapsed="true">
                    <field name="VAR" id="X#UMfnUF|OeyRcxIN~Y4">MARTINGALE LEVEL:</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="icx1d8aSG%A_Bg,peIBe" collapsed="true">
                    <field name="VAR" id="!c%d*XBHYNv0h]4+]*nj">TargetProfit</field>
                    <value name="VALUE">
                      <block type="variables_get" id="W-)Y---)Bz/YqD4XKqlW" collapsed="true">
                        <field name="VAR" id="*S5@jR!:6fNu_jGh29XP">TARGET (PROFIT):</field>
                      </block>
                    </value>
                    <next>
                      <block type="variables_set" id="uKy{YQmo7{AC2Okg2Gl=" collapsed="true">
                        <field name="VAR" id="n5(f~qa.-7fAJ0/kZ%tM">SECONDS DELAY AFTER TRADE</field>
                        <value name="VALUE">
                          <block type="variables_get" id="foJMuooE%+v{I~GCD?G5" collapsed="true">
                            <field name="VAR" id="4,{gz4@piZ_i|(Hl,HH7">Seconds To Delay After Trade:</field>
                          </block>
                        </value>
                        <next>
                          <block type="variables_set" id="/DTD~P/k@*EIIY/AFs9." collapsed="true">
                            <field name="VAR" id="{@I1+WS#gB.i#;(p)NeF">LOSS LEVEL TO DELAY</field>
                            <value name="VALUE">
                              <block type="variables_get" id="*tH0CTp{l24%j[.,#oNR" collapsed="true">
                                <field name="VAR" id="p[1(Y1~sO^sSEcTby:I}">Loss Level To Delay:</field>
                              </block>
                            </value>
                            <next>
                              <block type="variables_set" id="lAr^4V20G~`Oqn%GWip|" collapsed="true">
                                <field name="VAR" id="ddH0?/^#_8u1VjM/!`t|">Profit</field>
                                <value name="VALUE">
                                  <block type="math_number" id="z7],@g?78n@hx7)|]zE*" collapsed="true">
                                    <field name="NUM">0</field>
                                  </block>
                                </value>
                                <next>
                                  <block type="variables_set" id="T3lYgSbW8|c5t.$VOGE|" collapsed="true">
                                    <field name="VAR" id="Nxx}Lsm5Mjf|D~2!Dj%V">MartiStart</field>
                                    <value name="VALUE">
                                      <block type="variables_get" id="G3onk*633RxnvdTmm+g:" collapsed="true">
                                        <field name="VAR" id="-sYH8a;P)v}g?!iatjL,">DO MARTINGALE AFTER (how many losses):</field>
                                      </block>
                                    </value>
                                    <next>
                                      <block type="variables_set" id="V6j]N.}}f79Wkszl]G1e" collapsed="true">
                                        <field name="VAR" id="^(Gy/YRFAMn6r6Ekap/J">Loss Count</field>
                                        <value name="VALUE">
                                          <block type="math_number" id="Y:r5ZvH*[^hvdYVNL{KG" collapsed="true">
                                            <field name="NUM">0</field>
                                          </block>
                                        </value>
                                        <next>
                                          <block type="variables_set" id="R6fLi)8H_VepID|#pmA~" collapsed="true">
                                            <field name="VAR" id="ezm;s,8YY7`S_pxNx+{s">MartiFactor</field>
                                            <value name="VALUE">
                                              <block type="variables_get" id="Q]D*7GDnrTRA9CyO^G^Q" collapsed="true">
                                                <field name="VAR" id="}PYb?[K00*b[IHmPgA7k">MARTINGALE:</field>
                                              </block>
                                            </value>
                                            <next>
                                              <block type="variables_set" id="wWF^[Psx[[M[z?d{tSBr" collapsed="true">
                                                <field name="VAR" id="r*7kR;1FtFFmko}y;6d:">Trade Even</field>
                                                <value name="VALUE">
                                                  <block type="variables_get" id="j={CluZpcDhJ|pnB+qOI" collapsed="true">
                                                    <field name="VAR" id="H++/hOJGICM!wzXVJwcD">TRADE EVEN:</field>
                                                  </block>
                                                </value>
                                                <next>
                                                  <block type="variables_set" id="/?2Xav9EO3t%soj.6p56" collapsed="true">
                                                    <field name="VAR" id="PXfESF:-T-83fn#h{O$X">PRICE ACTION</field>
                                                    <value name="VALUE">
                                                      <block type="math_number" id="0VBGJ2$R(3JxNv%Fd%T^" collapsed="true">
                                                        <field name="NUM">1</field>
                                                      </block>
                                                    </value>
                                                    <next>
                                                      <block type="variables_set" id="NtF}%7~79^T?[G8MXby;" collapsed="true">
                                                        <field name="VAR" id="m(@~SI$PI9_=9gVd*zl9">Stoploss</field>
                                                        <value name="VALUE">
                                                          <block type="variables_get" id="u@n6w]KiDUzQUcG[AWrB" collapsed="true">
                                                            <field name="VAR" id="#zfdvs@F;$O(rYcB/,RJ">STOPLOSS:</field>
                                                          </block>
                                                        </value>
                                                        <next>
                                                          <block type="variables_set" id="MDC1VPgjgAO9S+h+y8#B" collapsed="true">
                                                            <field name="VAR" id="^UotDwO-dLL5EPt6H-zL">STAKE LIST</field>
                                                            <value name="VALUE">
                                                              <block type="variables_get" id="G~Tzd6ljZ2kI=tq.KldY" collapsed="true">
                                                                <field name="VAR" id="yPTHbp8J22cAev_KIKGk">STAKE LIST:</field>
                                                              </block>
                                                            </value>
                                                            <next>
                                                              <block type="variables_set" id="7$$vAjP9qR`)0}Jd@Q4)" collapsed="true">
                                                                <field name="VAR" id="/VJ7O.)sAJmm47`,|W=v">Loss Level</field>
                                                                <value name="VALUE">
                                                                  <block type="math_number" id="Bl*{XiV}b7d.rt-x/HS*" collapsed="true">
                                                                    <field name="NUM">0</field>
                                                                  </block>
                                                                </value>
                                                                <next>
                                                                  <block type="notify" id="T}OxCMgg@KVtm[th2{UX" collapsed="true">
                                                                    <field name="NOTIFICATION_TYPE">success</field>
                                                                    <field name="NOTIFICATION_SOUND">silent</field>
                                                                    <value name="MESSAGE">
                                                                      <block type="text" id="H-vv!=[|=EocjMfp/B+i" collapsed="true">
                                                                        <field name="TEXT">Welcome To The BinaryBotsAfrica Experience</field>
                                                                      </block>
                                                                    </value>
                                                                    <next>
                                                                      <block type="variables_set" id="Ll+@taG[QE8/IQNY27/:" collapsed="true">
                                                                        <field name="VAR" id="k:EcRUy)u-26FkWUoszO">FirstStake</field>
                                                                        <value name="VALUE">
                                                                          <block type="variables_get" id="Pnv@F-}[]wMoPJ,#[i`5" collapsed="true">
                                                                            <field name="VAR" id="l/g[COS_p$y={!V:[u*:">STAKE:</field>
                                                                          </block>
                                                                        </value>
                                                                        <next>
                                                                          <block type="variables_set" id="E#q=%4RNQs(QhP`SFxD7" collapsed="true">
                                                                            <field name="VAR" id="RRH]M7%0M)Zi;ridyo7}">Win Count</field>
                                                                            <value name="VALUE">
                                                                              <block type="math_number" id="lA6k(5LKv:{_FG[E24tN" collapsed="true">
                                                                                <field name="NUM">0</field>
                                                                              </block>
                                                                            </value>
                                                                            <next>
                                                                              <block type="variables_set" id="6p{CiOF!hce9}M}-S9_*" collapsed="true">
                                                                                <field name="VAR" id="q533fsFRuhI2e%]C~96S">Ticks</field>
                                                                                <value name="VALUE">
                                                                                  <block type="variables_get" id="^!F`5$!dp@jU@B671H+t" collapsed="true">
                                                                                    <field name="VAR" id="8|3.Ys5C1.MEvri9FB64">TICKS:</field>
                                                                                  </block>
                                                                                </value>
                                                                                <next>
                                                                                  <block type="variables_set" id="pX;o!:-/zRGE6.KpVauA" collapsed="true">
                                                                                    <field name="VAR" id="RMbK)by1LEnp^9@HKC(*">Stake</field>
                                                                                    <value name="VALUE">
                                                                                      <block type="variables_get" id="+q4V;pY?h(-JIkycRl`h" collapsed="true">
                                                                                        <field name="VAR" id="l/g[COS_p$y={!V:[u*:">STAKE:</field>
                                                                                      </block>
                                                                                    </value>
                                                                                    <next>
                                                                                      <block type="variables_set" id="j5%Q=ZUQgQ:vMP3xaJpv" collapsed="true">
                                                                                        <field name="VAR" id="!ovMYR)h8Erl`U2vcw!q">Trade Both</field>
                                                                                        <value name="VALUE">
                                                                                          <block type="variables_get" id="EVQzJ~r,}U/Hwvtn;iSd" collapsed="true">
                                                                                            <field name="VAR" id="BQw@koGmjNEXM~N6=y)/">TRADE BOTH:</field>
                                                                                          </block>
                                                                                        </value>
                                                                                        <next>
                                                                                          <block type="variables_set" id="xM3HkcE9wPnkH.N/`c`V" collapsed="true">
                                                                                            <field name="VAR" id="4pu$olWHHv+#[Q}D24NA">Trade Odd</field>
                                                                                            <value name="VALUE">
                                                                                              <block type="variables_get" id="Y*,/aN0Eyjnw-Ut+l[81" collapsed="true">
                                                                                                <field name="VAR" id="p+RtTxpq59Q7E0Dl2}8M">TRADE ODD:</field>
                                                                                              </block>
                                                                                            </value>
                                                                                          </block>
                                                                                        </next>
                                                                                      </block>
                                                                                    </next>
                                                                                  </block>
                                                                                </next>
                                                                              </block>
                                                                            </next>
                                                                          </block>
                                                                        </next>
                                                                      </block>
                                                                    </next>
                                                                  </block>
                                                                </next>
                                                              </block>
                                                            </next>
                                                          </block>
                                                        </next>
                                                      </block>
                                                    </next>
                                                  </block>
                                                </next>
                                              </block>
                                            </next>
                                          </block>
                                        </next>
                                      </block>
                                    </next>
                                  </block>
                                </next>
                              </block>
                            </next>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
  </block>
  <block type="tick_analysis" id="k{5q-@VVjbuXJU$-0=]{" x="-6" y="2511">
    <statement name="TICKANALYSIS_STACK">
      <block type="text_join" id="5bMK6TMp1]w-{LL3;/1/" collapsed="true">
        <field name="VARIABLE" id=":wo}_B]a6yW}vOnvOMKR">text4</field>
        <statement name="STACK">
          <block type="text_statement" id=":3Ioh]p+TSVqtS@b5JOy">
            <value name="TEXT">
              <shadow type="text" id="M-o@uLVT6#Ff6B`Kf|yB">
                <field name="TEXT"></field>
              </shadow>
              <block type="text" id="JhC}SSu.0Pf7s/X{if*)" collapsed="true">
                <field name="TEXT">ODD </field>
              </block>
            </value>
            <next>
              <block type="text_statement" id="VZ^3*_Sa]Ve;TcAOX.{|">
                <value name="TEXT">
                  <shadow type="text" id="9p|Fgj$~#n:#MT0|!!@J">
                    <field name="TEXT"></field>
                  </shadow>
                  <block type="procedures_callreturn" id="X@@HU:L{fJPN-p^i`UoS">
                    <mutation xmlns="http://www.w3.org/1999/xhtml" name="Odd (%)"></mutation>
                  </block>
                </value>
                <next>
                  <block type="text_statement" id="7;UYxtK;*O`2d9ho[p(R">
                    <value name="TEXT">
                      <shadow type="text" id="K5k#MwT~zt+7NIn4?}we">
                        <field name="TEXT"></field>
                      </shadow>
                      <block type="text" id="/)NcEeK!TI{3uLRk1iIE" collapsed="true">
                        <field name="TEXT">%</field>
                      </block>
                    </value>
                    <next>
                      <block type="text_statement" id="]V%n8@Gqo6J|.q=G8G5A">
                        <value name="TEXT">
                          <shadow type="text" id="XKr=T:z2:Me,2jvrW+xB">
                            <field name="TEXT"></field>
                          </shadow>
                          <block type="text" id="z]OEU(^WiK-h`(EMkaC%" collapsed="true">
                            <field name="TEXT">  vs  </field>
                          </block>
                        </value>
                        <next>
                          <block type="text_statement" id="r|t+}?CJn_=SyXD)c{lb">
                            <value name="TEXT">
                              <shadow type="text" id="q;2E1~]FghcVN0GZ!dGN">
                                <field name="TEXT"></field>
                              </shadow>
                              <block type="procedures_callreturn" id="Kg06JZp=Yv8sBP,sJ$F@">
                                <mutation xmlns="http://www.w3.org/1999/xhtml" name="Even (%)"></mutation>
                              </block>
                            </value>
                            <next>
                              <block type="text_statement" id=",7KLH?[*FArW}Cej?:lE">
                                <value name="TEXT">
                                  <shadow type="text" id="?gtgt2DGgZ7h^Dkj7v)U">
                                    <field name="TEXT"></field>
                                  </shadow>
                                  <block type="text" id="@%M~bWkiI8!6JC{kQs$$" collapsed="true">
                                    <field name="TEXT">%</field>
                                  </block>
                                </value>
                                <next>
                                  <block type="text_statement" id="k-Sc$6ACfE}sE2G#j|2]">
                                    <value name="TEXT">
                                      <shadow type="text" id="OC9.QEe47p=9}5y~L%!:">
                                        <field name="TEXT"></field>
                                      </shadow>
                                      <block type="text" id="?bwbWr23mESh-%,D`qYx" collapsed="true">
                                        <field name="TEXT"> EVEN</field>
                                      </block>
                                    </value>
                                  </block>
                                </next>
                              </block>
                            </next>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </statement>
        <next>
          <block type="notify" id="kn_!voB=^}i(WisH)ub," collapsed="true">
            <field name="NOTIFICATION_TYPE">success</field>
            <field name="NOTIFICATION_SOUND">silent</field>
            <value name="MESSAGE">
              <shadow type="text" id="h@ZKYGk+6(1QQJY=t$/a">
                <field name="TEXT">abc</field>
              </shadow>
              <block type="variables_get" id="sa909Z7`];xq@FE$DeE1" collapsed="true">
                <field name="VAR" id=":wo}_B]a6yW}vOnvOMKR">text4</field>
              </block>
            </value>
          </block>
        </next>
      </block>
    </statement>
  </block>
  <block type="procedures_defreturn" id="IH4xTAno/*E,i:Un3k3I" collapsed="true" x="409" y="2505">
    <field name="NAME">[INVESTORBRAYO EOA] STAKE</field>
    <comment pinned="false" h="80" w="160">Describe this function...</comment>
    <value name="RETURN">
      <block type="variables_get" id="RfT4,=`T9Wu]gMtTYK?i" collapsed="true">
        <field name="VAR" id="RMbK)by1LEnp^9@HKC(*">Stake</field>
      </block>
    </value>
  </block>
  <block type="procedures_defreturn" id="s`u5+C]$J3)JfPr]#iA[" collapsed="true" x="797" y="2505">
    <mutation xmlns="http://www.w3.org/1999/xhtml">
      <arg name="[Win Result]" varid="BPsrb}:o?nCP7t]1@Rfq"></arg>
      <arg name="Detail Profit" varid="dK=o~;7~9dr8wCZ%hyh,"></arg>
    </mutation>
    <field name="NAME">[InvestorBrayo] Trade Again</field>
    <comment pinned="false" h="80" w="160">Describe this function...</comment>
    <value name="RETURN">
      <block type="variables_get" id="^6%^[p?~_b$RiW!6ukCG" collapsed="true">
        <field name="VAR" id="#S5]i9_E6vE_6t^_dYeJ">Continue</field>
      </block>
    </value>
  </block>
  <block type="procedures_defnoreturn" id="WCbY*2V_9a{f@{Lqc(Hr" collapsed="true" x="1329" y="2504">
    <mutation xmlns="http://www.w3.org/1999/xhtml">
      <arg name="Last Digit Lists" varid="n?PC1yzq?YzC866oYh}f"></arg>
      <arg name="Stats Period (Ticks)" varid="6QHr,opAh-;a$tGllUDJ"></arg>
    </mutation>
    <field name="NAME">Reading InvestorBrayo Digit Analysis System</field>
    <comment pinned="false" h="80" w="160">Describe this function...</comment>
    <statement name="STACK">
      <block type="lists_create_with" id="}0F)ec~;I4$lx1;rg..A" collapsed="true">
        <field name="VARIABLE" id="+lq9`1Xb*zF^4~8]$@P[">list1</field>
        <next>
          <block type="variables_set" id="#ZNx?N2x_E)Kw0|0jNat" collapsed="true">
            <field name="VAR" id="3,!j[HXimP@D1Vd:L+q8">LD List</field>
            <value name="VALUE">
              <block type="variables_get" id="P%U3v8}OdPz}-VUXwX@l" collapsed="true">
                <field name="VAR" id="+lq9`1Xb*zF^4~8]$@P[">list1</field>
              </block>
            </value>
            <next>
              <block type="variables_set" id="_2#|gV_NK3dbrB%A:1~[" collapsed="true">
                <field name="VAR" id="3,!j[HXimP@D1Vd:L+q8">LD List</field>
                <value name="VALUE">
                  <block type="variables_get" id="4us[MRmbYQ|X?8A|;Dj(" collapsed="true">
                    <field name="VAR" id="n?PC1yzq?YzC866oYh}f">Last Digit Lists</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id=",WNxaADfWv6KaiPC8#I:" collapsed="true">
                    <field name="VAR" id="F,f.PSpD@![!-ACOxasI">Even Do</field>
                    <value name="VALUE">
                      <block type="math_number" id="Z,v{YRa-9RO;XLqRSO/*">
                        <field name="NUM">0</field>
                      </block>
                    </value>
                    <next>
                      <block type="variables_set" id="/u~.|ZSdEo1]$DrY7hpT" collapsed="true">
                        <field name="VAR" id="`:R7~|*x9_{.1.rTwqyS">Odd Do</field>
                        <value name="VALUE">
                          <block type="math_number" id="_I@bl/ZpNrob]2IW$B=x">
                            <field name="NUM">0</field>
                          </block>
                        </value>
                        <next>
                          <block type="controls_for" id="6_,}FrYyA3#^(jkBnknr" collapsed="true">
                            <field name="VAR" id="Dxw=G5bQ$^NSTX_4oY]~">LD Count</field>
                            <value name="FROM">
                              <block type="math_number" id="bTz4aXCYcU)Cy{hdhz`u" collapsed="true">
                                <field name="NUM">1</field>
                              </block>
                            </value>
                            <value name="TO">
                              <block type="variables_get" id="1WZ,-huZ9pQX*#Oc^)uA" collapsed="true">
                                <field name="VAR" id="6QHr,opAh-;a$tGllUDJ">Stats Period (Ticks)</field>
                              </block>
                            </value>
                            <value name="BY">
                              <block type="math_number" id="_fy:U#$-Rp%mmP~C)En+">
                                <field name="NUM">1</field>
                              </block>
                            </value>
                            <statement name="DO">
                              <block type="controls_if" id="4wI,~S5UD}H_#.-},@XF" collapsed="true">
                                <mutation xmlns="http://www.w3.org/1999/xhtml" elseif="1"></mutation>
                                <value name="IF0">
                                  <block type="math_number_property" id=")20qW-QUvr*z{l}mYv_O" collapsed="true">
                                    <mutation xmlns="http://www.w3.org/1999/xhtml" divisor_input="false"></mutation>
                                    <field name="PROPERTY">EVEN</field>
                                    <value name="NUMBER_TO_CHECK">
                                      <shadow type="math_number" id="3s-e|7fXcR9}h^3^Yaj;">
                                        <field name="NUM">0</field>
                                      </shadow>
                                      <block type="lists_getIndex" id="}p$I1$])EO/h}pm$^B7=" collapsed="true">
                                        <mutation xmlns="http://www.w3.org/1999/xhtml" statement="false" at="true"></mutation>
                                        <field name="MODE">GET</field>
                                        <field name="WHERE">FROM_END</field>
                                        <value name="VALUE">
                                          <block type="variables_get" id="6$,y`j:|m5r1]^Pf|`DR" collapsed="true">
                                            <field name="VAR" id="3,!j[HXimP@D1Vd:L+q8">LD List</field>
                                          </block>
                                        </value>
                                        <value name="AT">
                                          <block type="variables_get" id="crZ3o=RxFQ,ON7StSs?z" collapsed="true">
                                            <field name="VAR" id="Dxw=G5bQ$^NSTX_4oY]~">LD Count</field>
                                          </block>
                                        </value>
                                      </block>
                                    </value>
                                  </block>
                                </value>
                                <statement name="DO0">
                                  <block type="math_change" id="E[SS87k2D]Af6A!qTMM;" collapsed="true">
                                    <field name="VAR" id="F,f.PSpD@![!-ACOxasI">Even Do</field>
                                    <value name="DELTA">
                                      <shadow type="math_number" id="*+UWO5hMox-ZS@}f@i0~">
                                        <field name="NUM">1</field>
                                      </shadow>
                                    </value>
                                  </block>
                                </statement>
                                <value name="IF1">
                                  <block type="math_number_property" id="|8v#BH6L0=zR[k!cW@-3" collapsed="true">
                                    <mutation xmlns="http://www.w3.org/1999/xhtml" divisor_input="false"></mutation>
                                    <field name="PROPERTY">ODD</field>
                                    <value name="NUMBER_TO_CHECK">
                                      <shadow type="math_number" id="2+R!9U|rrL6,#|{2.wu?">
                                        <field name="NUM">0</field>
                                      </shadow>
                                      <block type="lists_getIndex" id="cjc:M~QJ%75RC$mt8jlV" collapsed="true">
                                        <mutation xmlns="http://www.w3.org/1999/xhtml" statement="false" at="true"></mutation>
                                        <field name="MODE">GET</field>
                                        <field name="WHERE">FROM_END</field>
                                        <value name="VALUE">
                                          <block type="variables_get" id="rfkM^ssT#IFauT$oBhEc" collapsed="true">
                                            <field name="VAR" id="3,!j[HXimP@D1Vd:L+q8">LD List</field>
                                          </block>
                                        </value>
                                        <value name="AT">
                                          <block type="variables_get" id="G7x~8jP0k/kkID4G$yH," collapsed="true">
                                            <field name="VAR" id="Dxw=G5bQ$^NSTX_4oY]~">LD Count</field>
                                          </block>
                                        </value>
                                      </block>
                                    </value>
                                  </block>
                                </value>
                                <statement name="DO1">
                                  <block type="math_change" id="7.}RV^z||^:2rT:L^tlr" collapsed="true">
                                    <field name="VAR" id="`:R7~|*x9_{.1.rTwqyS">Odd Do</field>
                                    <value name="DELTA">
                                      <shadow type="math_number" id="k;Wggen}?UeYTm9${O]o">
                                        <field name="NUM">1</field>
                                      </shadow>
                                    </value>
                                  </block>
                                </statement>
                              </block>
                            </statement>
                            <next>
                              <block type="variables_set" id="*]d0?lePBlb2npgkEww+" collapsed="true">
                                <field name="VAR" id="W,xwUTX9UvK.FPQ}k|S=">Even %</field>
                                <value name="VALUE">
                                  <block type="math_arithmetic" id="=w_ynu/%vQt:}2mo=6te" collapsed="true">
                                    <field name="OP">DIVIDE</field>
                                    <value name="A">
                                      <shadow type="math_number" id=")Nbfj2YZU4#J-/+m$AwJ">
                                        <field name="NUM">1</field>
                                      </shadow>
                                      <block type="math_arithmetic" id="]k7yFapPU9#wRZ/.P1V~" collapsed="true">
                                        <field name="OP">MULTIPLY</field>
                                        <value name="A">
                                          <shadow type="math_number" id="?Fw80ny.x#-L9r]m)}NY">
                                            <field name="NUM">1</field>
                                          </shadow>
                                          <block type="variables_get" id="7Gg@CYmVUe/6e}A%W8L}" collapsed="true">
                                            <field name="VAR" id="F,f.PSpD@![!-ACOxasI">Even Do</field>
                                          </block>
                                        </value>
                                        <value name="B">
                                          <shadow type="math_number" id="wkcmo|9wjl()ro2~/#D0">
                                            <field name="NUM">100</field>
                                          </shadow>
                                        </value>
                                      </block>
                                    </value>
                                    <value name="B">
                                      <shadow type="math_number" id="vQoVo1Htc*I?wonoyrP.">
                                        <field name="NUM">100</field>
                                      </shadow>
                                      <block type="variables_get" id="z$/T7YaWkO[vQT)EV0xy" collapsed="true">
                                        <field name="VAR" id="6QHr,opAh-;a$tGllUDJ">Stats Period (Ticks)</field>
                                      </block>
                                    </value>
                                  </block>
                                </value>
                                <next>
                                  <block type="variables_set" id="1CR_/D:8J{My~Kj@Kt!X" collapsed="true">
                                    <field name="VAR" id="W,xwUTX9UvK.FPQ}k|S=">Even %</field>
                                    <value name="VALUE">
                                      <block type="math_arithmetic" id="0~tu=P.-^UB~})!xc(,q" collapsed="true">
                                        <field name="OP">DIVIDE</field>
                                        <value name="A">
                                          <shadow type="math_number" id="VXpl(5?7ie(*Kh%dHFEx">
                                            <field name="NUM">1</field>
                                          </shadow>
                                          <block type="math_round" id="X}F?L}yjB;_6%N;p3WQw" collapsed="true">
                                            <field name="OP">ROUND</field>
                                            <value name="NUM">
                                              <shadow type="math_number" id="#lG,mc0Z@1r%o9N7M,nl">
                                                <field name="NUM">3.1</field>
                                              </shadow>
                                              <block type="math_arithmetic" id="*_l++y(5rD)AJHe@A)P=" collapsed="true">
                                                <field name="OP">MULTIPLY</field>
                                                <value name="A">
                                                  <shadow type="math_number" id="Tp^A#o+9n]H*6+nMS@=Z">
                                                    <field name="NUM">1</field>
                                                  </shadow>
                                                  <block type="variables_get" id="`uVQ@qHmw3co)7$~raTq">
                                                    <field name="VAR" id="W,xwUTX9UvK.FPQ}k|S=">Even %</field>
                                                  </block>
                                                </value>
                                                <value name="B">
                                                  <shadow type="math_number" id="nU+}U|m[pYj}KK;!AK~H">
                                                    <field name="NUM">100</field>
                                                  </shadow>
                                                </value>
                                              </block>
                                            </value>
                                          </block>
                                        </value>
                                        <value name="B">
                                          <shadow type="math_number" id="YS/kifXHQ@06_dq$G`RA">
                                            <field name="NUM">100</field>
                                          </shadow>
                                        </value>
                                      </block>
                                    </value>
                                    <next>
                                      <block type="variables_set" id="`|0JyU{v|3/E+OX)EP?a" collapsed="true">
                                        <field name="VAR" id="D-MHjLG(H:mwS[]`iA1^">Odd %</field>
                                        <value name="VALUE">
                                          <block type="math_arithmetic" id="*Hr;+g]h_R0WK|Xn10/@" collapsed="true">
                                            <field name="OP">DIVIDE</field>
                                            <value name="A">
                                              <shadow type="math_number" id="z,~=Y-Oh~~Ej%L]Xo!vS">
                                                <field name="NUM">1</field>
                                              </shadow>
                                              <block type="math_arithmetic" id="t`t5f?i4kGm1=~w{y8I`" collapsed="true">
                                                <field name="OP">MULTIPLY</field>
                                                <value name="A">
                                                  <shadow type="math_number" id="Pf(~a_4r(K7)Xm!ANr5z">
                                                    <field name="NUM">1</field>
                                                  </shadow>
                                                  <block type="variables_get" id="v%Awi.dHqhH??b@vYj.v">
                                                    <field name="VAR" id="`:R7~|*x9_{.1.rTwqyS">Odd Do</field>
                                                  </block>
                                                </value>
                                                <value name="B">
                                                  <shadow type="math_number" id="#^-7WqhE};HHzg#nnE)e">
                                                    <field name="NUM">100</field>
                                                  </shadow>
                                                </value>
                                              </block>
                                            </value>
                                            <value name="B">
                                              <shadow type="math_number" id="s(}pM}FB3B,VLB8zn}Cz">
                                                <field name="NUM">100</field>
                                              </shadow>
                                              <block type="variables_get" id="7kDs,Lmhwc|V^$;WLSkA" collapsed="true">
                                                <field name="VAR" id="6QHr,opAh-;a$tGllUDJ">Stats Period (Ticks)</field>
                                              </block>
                                            </value>
                                          </block>
                                        </value>
                                        <next>
                                          <block type="variables_set" id="|psnec{O8S.7oN]V^)$(" collapsed="true">
                                            <field name="VAR" id="D-MHjLG(H:mwS[]`iA1^">Odd %</field>
                                            <value name="VALUE">
                                              <block type="math_arithmetic" id="P?Om?p.c_H?;ql;F[,;3" collapsed="true">
                                                <field name="OP">DIVIDE</field>
                                                <value name="A">
                                                  <shadow type="math_number" id="PG]IkGU~#]sqv{Yy=hg1">
                                                    <field name="NUM">1</field>
                                                  </shadow>
                                                  <block type="math_round" id="q+-2J_+msw)ej#=@v:]3" collapsed="true">
                                                    <field name="OP">ROUND</field>
                                                    <value name="NUM">
                                                      <shadow type="math_number" id="_s!-9s+C/VEqw{jaiAfI">
                                                        <field name="NUM">3.1</field>
                                                      </shadow>
                                                      <block type="math_arithmetic" id="~As0lhc/_PTB~5xw4J|U" collapsed="true">
                                                        <field name="OP">MULTIPLY</field>
                                                        <value name="A">
                                                          <shadow type="math_number" id="Fe1LwZF8R6%}b?g$dzqh">
                                                            <field name="NUM">1</field>
                                                          </shadow>
                                                          <block type="variables_get" id="gzGC!S1F@k8|;$uT}G%~">
                                                            <field name="VAR" id="D-MHjLG(H:mwS[]`iA1^">Odd %</field>
                                                          </block>
                                                        </value>
                                                        <value name="B">
                                                          <shadow type="math_number" id="PXD/g2EI~fP-oL|ukg7X">
                                                            <field name="NUM">100</field>
                                                          </shadow>
                                                        </value>
                                                      </block>
                                                    </value>
                                                  </block>
                                                </value>
                                                <value name="B">
                                                  <shadow type="math_number" id="Q!hwyOhhsxF[MEfn0Vi+">
                                                    <field name="NUM">100</field>
                                                  </shadow>
                                                </value>
                                              </block>
                                            </value>
                                          </block>
                                        </next>
                                      </block>
                                    </next>
                                  </block>
                                </next>
                              </block>
                            </next>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
  </block>
</xml>