#!/usr/bin/env node

/**
 * Localhost Development Utilities
 * Helper functions for local development environment
 */

const os = require('os');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    green: '\x1b[32m',
    blue: '\x1b[34m',
    yellow: '\x1b[33m',
    red: '\x1b[31m',
    cyan: '\x1b[36m',
    magenta: '\x1b[35m',
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function getNetworkInfo() {
    const interfaces = os.networkInterfaces();
    const networkInfo = [];

    for (const name of Object.keys(interfaces)) {
        for (const interface of interfaces[name]) {
            if (interface.family === 'IPv4' && !interface.internal) {
                networkInfo.push({
                    name,
                    address: interface.address,
                    netmask: interface.netmask,
                });
            }
        }
    }

    return networkInfo;
}

function generateQRCode(url) {
    try {
        // Simple ASCII QR code alternative - just display the URL prominently
        const border = '█'.repeat(url.length + 4);
        log('\n' + border, 'cyan');
        log(`█ ${url} █`, 'cyan');
        log(border, 'cyan');
        log('\n📱 Scan this URL on your mobile device:', 'yellow');
        log(`   ${url}`, 'bright');
    } catch (error) {
        log('📱 Mobile URL: ' + url, 'yellow');
    }
}

function checkSystemRequirements() {
    log('🔍 Checking System Requirements...', 'cyan');

    try {
        // Check Node.js version
        const nodeVersion = process.version;
        log(`✅ Node.js: ${nodeVersion}`, 'green');

        // Check npm version
        const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
        log(`✅ npm: v${npmVersion}`, 'green');

        // Check if port 3000 is available
        try {
            execSync('netstat -an | findstr :3000', { stdio: 'pipe' });
            log(`⚠️  Port 3000 is in use`, 'yellow');
        } catch {
            log(`✅ Port 3000 is available`, 'green');
        }

        // Check available memory
        const totalMem = Math.round(os.totalmem() / 1024 / 1024 / 1024);
        const freeMem = Math.round(os.freemem() / 1024 / 1024 / 1024);
        log(`💾 Memory: ${freeMem}GB free / ${totalMem}GB total`, 'blue');
    } catch (error) {
        log(`❌ Error checking requirements: ${error.message}`, 'red');
    }
}

function displayNetworkInfo() {
    log('\n🌐 Network Information', 'bright');
    log('═══════════════════════════════════', 'cyan');

    const networkInfo = getNetworkInfo();

    if (networkInfo.length === 0) {
        log('❌ No network interfaces found', 'red');
        return;
    }

    networkInfo.forEach((info, index) => {
        log(`${index + 1}. ${info.name}`, 'yellow');
        log(`   IP: ${info.address}`, 'green');
        log(`   Netmask: ${info.netmask}`, 'blue');

        const port = process.env.PORT || 3000;
        const url = `http://${info.address}:${port}`;
        log(`   URL: ${url}`, 'magenta');

        if (index === 0) {
            generateQRCode(url);
        }
    });
}

function displayDomainTestingInfo() {
    log('\n🎯 Domain Testing Features', 'bright');
    log('═══════════════════════════════════', 'cyan');

    const domains = [
        'promoclub.site',
        'tradermaster.site',
        'gletraders.site',
        'legoo.site',
        'wallacetraders.site',
        'kingstraders.site',
        'dbotprinters.site',
        'kenyanhennessy.site',
        'masterhunter.site',
        'swiftpro.site',
    ];

    const port = process.env.PORT || 3000;
    const baseUrl = `http://localhost:${port}`;

    log('🔧 Test domain-specific features using URL parameters:', 'yellow');
    log(`   ${baseUrl}?bots_domain=DOMAIN_NAME`, 'green');
    log('\n📋 Available domains to test:', 'yellow');

    domains.forEach((domain, index) => {
        log(`   ${index + 1}. ${baseUrl}?bots_domain=${domain}`, 'blue');
    });

    log('\n💡 This will load domain-specific bot configurations', 'cyan');
}

function displayDevelopmentTips() {
    log('\n💡 Development Tips', 'bright');
    log('═══════════════════════════════════', 'cyan');

    const tips = [
        'Use browser dev tools to inspect network requests',
        'Check localStorage for config.app_id and config.server_url',
        'Service worker caches bot XMLs - clear cache if needed',
        'Use ?bots_domain parameter to test domain-specific features',
        'Hot reload is enabled - changes will auto-refresh',
        'External connections allowed for mobile testing',
        'Check localhost-setup.md for detailed documentation',
    ];

    tips.forEach((tip, index) => {
        log(`${index + 1}. ${tip}`, 'yellow');
    });
}

// Parse command line arguments
const args = process.argv.slice(2);
const command = args[0];

switch (command) {
    case 'network':
    case 'net':
        displayNetworkInfo();
        break;

    case 'domains':
    case 'domain':
        displayDomainTestingInfo();
        break;

    case 'tips':
        displayDevelopmentTips();
        break;

    case 'check':
    case 'requirements':
        checkSystemRequirements();
        break;

    case 'info':
    case 'all':
    default:
        checkSystemRequirements();
        displayNetworkInfo();
        displayDomainTestingInfo();
        displayDevelopmentTips();
        break;
}

if (args.includes('--help') || args.includes('-h')) {
    log('\n🛠️  Localhost Development Utilities', 'bright');
    log('\nUsage:', 'cyan');
    log('  node scripts/localhost-utils.js [command]', 'green');
    log('\nCommands:', 'cyan');
    log('  info, all      Show all information (default)', 'yellow');
    log('  network, net   Show network information', 'yellow');
    log('  domains        Show domain testing info', 'yellow');
    log('  tips           Show development tips', 'yellow');
    log('  check          Check system requirements', 'yellow');
    log('  --help, -h     Show this help message', 'yellow');
}
