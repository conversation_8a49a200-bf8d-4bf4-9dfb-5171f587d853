<xml xmlns="https://developers.google.com/blockly/xml" is_dbot="true" collection="false">
  <variables>
    <variable id="X[(S;ATE$Dbs3flXN{-m">Initial Amount</variable>
    <variable id="lBWo1Ta1T]7SBMFX8k9R">Tick 1</variable>
    <variable id="4T(l!T7tM|8YeN!7`h[5">Prediction Before loss</variable>
    <variable id="mcPPu45l`UKxnH73P@w=">Prediction</variable>
    <variable id="t~60Rry*.,:.w/VFe!$F">Tick 2</variable>
    <variable id="(.pOfi2ikl^ae0}~D!5f">Prediction after loss</variable>
    <variable id=";wJj_I^Js9-1UR#/~_$1">Next Trade Condition</variable>
    <variable id="KBmmw!H,}psHLCZX=nXr">Tick 3</variable>
    <variable id="ZC9jlKp0p+PVYAU:n7?-">Win Amount</variable>
  </variables>
  <block type="trade_definition" id="U[,.-|2e]3;YO9OfznyD" deletable="false" x="0" y="60">
    <statement name="TRADE_OPTIONS">
      <block type="trade_definition_market" id="^}Rt|UxnL^ajIK$NcJD[" deletable="false" movable="false">
        <field name="MARKET_LIST">synthetic_index</field>
        <field name="SUBMARKET_LIST">random_index</field>
        <field name="SYMBOL_LIST">1HZ10V</field>
        <next>
          <block type="trade_definition_tradetype" id="`52R@RN6Ikm3p4|^hy4#" deletable="false" movable="false">
            <field name="TRADETYPECAT_LIST">digits</field>
            <field name="TRADETYPE_LIST">overunder</field>
            <next>
              <block type="trade_definition_contracttype" id="Klc~i]aDdd39.@f~CaA!" deletable="false" movable="false">
                <field name="TYPE_LIST">both</field>
                <next>
                  <block type="trade_definition_candleinterval" id="EFRI;?~=2vL]$HO,;Vx]" deletable="false" movable="false">
                    <field name="CANDLEINTERVAL_LIST">60</field>
                    <next>
                      <block type="trade_definition_restartbuysell" id=",uL;UGiqfXsnkbNVXaRe" deletable="false" movable="false">
                        <field name="TIME_MACHINE_ENABLED">FALSE</field>
                        <next>
                          <block type="trade_definition_restartonerror" id="2WGP0=.TOJInFocZa_R7" deletable="false" movable="false">
                            <field name="RESTARTONERROR">TRUE</field>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="INITIALIZATION">
      <block type="variables_set" id="jOtC|pw8K=5k*h;5^@{0">
        <field name="VAR" id="X[(S;ATE$Dbs3flXN{-m">Initial Amount</field>
        <value name="VALUE">
          <block type="math_number" id="rErk*C5_@s,TFgcaLOC#">
            <field name="NUM">4</field>
          </block>
        </value>
        <next>
          <block type="variables_set" id="6li3uk*Ziz=.0_jqiLN#">
            <field name="VAR" id="4T(l!T7tM|8YeN!7`h[5">Prediction Before loss</field>
            <value name="VALUE">
              <block type="math_number" id="aKeve+/O+^rf8wk+y.qJ">
                <field name="NUM">1</field>
              </block>
            </value>
            <next>
              <block type="variables_set" id="wVJ1~cpk[nUG+RN+hkb;">
                <field name="VAR" id="(.pOfi2ikl^ae0}~D!5f">Prediction after loss</field>
                <value name="VALUE">
                  <block type="math_number" id="o]`ysFZ?AMWHVbf60pGK">
                    <field name="NUM">4</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="uGO`kgYD*y]Ux(Ii`B_B">
                    <field name="VAR" id=";wJj_I^Js9-1UR#/~_$1">Next Trade Condition</field>
                    <value name="VALUE">
                      <block type="text" id="CWa#tvrsg56m2I~O!laP">
                        <field name="TEXT">Even</field>
                      </block>
                    </value>
                    <next>
                      <block type="variables_set" id="fH)M`Qh3x=Q9Pt/7:];x">
                        <field name="VAR" id="ZC9jlKp0p+PVYAU:n7?-">Win Amount</field>
                        <value name="VALUE">
                          <block type="variables_get" id="g-|`B`6FUsld0;+hB%!)">
                            <field name="VAR" id="X[(S;ATE$Dbs3flXN{-m">Initial Amount</field>
                          </block>
                        </value>
                        <next>
                          <block type="variables_set" id="Bc}OPMd#Nk@]:,7+4wRE">
                            <field name="VAR" id="mcPPu45l`UKxnH73P@w=">Prediction</field>
                            <value name="VALUE">
                              <block type="variables_get" id="Gp8cS?52m*9!tI;uu|,t">
                                <field name="VAR" id="4T(l!T7tM|8YeN!7`h[5">Prediction Before loss</field>
                              </block>
                            </value>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="SUBMARKET">
      <block type="trade_definition_tradeoptions" id="/=hOz{QM{MuOwei{jam[">
        <mutation xmlns="http://www.w3.org/1999/xhtml" has_first_barrier="false" has_second_barrier="false" has_prediction="true"></mutation>
        <field name="DURATIONTYPE_LIST">t</field>
        <value name="DURATION">
          <block type="math_random_int" id="VX`-LCbjh^C.3k]!o8lD">
            <value name="FROM">
              <shadow type="math_number" id="tcZ!w1Ay?0}_o)0z#=A6">
                <field name="NUM">1</field>
              </shadow>
            </value>
            <value name="TO">
              <shadow type="math_number" id="8r_5l!VzOLH*Bf}#3sO1">
                <field name="NUM">1</field>
              </shadow>
            </value>
          </block>
        </value>
        <value name="AMOUNT">
          <block type="variables_get" id="xWa3xA[pJ8AdYdJHK5p)">
            <field name="VAR" id="X[(S;ATE$Dbs3flXN{-m">Initial Amount</field>
          </block>
        </value>
        <value name="PREDICTION">
          <shadow type="math_number_positive" id="xadD3d_dbAqr{X6doB4=" inline="true">
            <field name="NUM">1</field>
          </shadow>
          <block type="variables_get" id="`+9rRj|#}Iq7JK?W9|Bs">
            <field name="VAR" id="mcPPu45l`UKxnH73P@w=">Prediction</field>
          </block>
        </value>
      </block>
    </statement>
  </block>
  <block type="after_purchase" id="ca@2x?E#-u}HLizUkPmQ" x="1037" y="60">
    <statement name="AFTERPURCHASE_STACK">
      <block type="controls_if" id="uS|UQ$j[0z:%1VG8L[;I">
        <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
        <value name="IF0">
          <block type="logic_compare" id="{kvK-`#l7+aI=`2:%!K(">
            <field name="OP">GTE</field>
            <value name="A">
              <block type="total_profit" id="%|rU6Jm])JUg0{f+k{xC"></block>
            </value>
            <value name="B">
              <block type="variables_get" id="u+60J~nJ(#6!W$KNmP-c">
                <field name="VAR" id="X[(S;ATE$Dbs3flXN{-m">Initial Amount</field>
              </block>
            </value>
          </block>
        </value>
        <statement name="DO0">
          <block type="text_print" id="^UuajXA$q2FIs~:m%m!)">
            <value name="TEXT">
              <shadow type="text" id="-,LB4JM5{*)NlvwY`g((">
                <field name="TEXT">Take Profit hit</field>
              </shadow>
            </value>
          </block>
        </statement>
        <statement name="ELSE">
          <block type="controls_if" id="e.)Zd[oh!C=upGLfjb1X">
            <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
            <value name="IF0">
              <block type="contract_check_result" id="UgQAMk@/p8aghx2qQGOR">
                <field name="CHECK_RESULT">loss</field>
              </block>
            </value>
            <statement name="DO0">
              <block type="math_change" id=";(}waEKtrEvh`j$=/4_@">
                <field name="VAR" id="X[(S;ATE$Dbs3flXN{-m">Initial Amount</field>
                <value name="DELTA">
                  <shadow type="math_number" id="@lC(q@zhEj/MK)4{cK);">
                    <field name="NUM">1</field>
                  </shadow>
                  <block type="math_arithmetic" id="q-Xz~EXD1Jex4mUh)n5Q">
                    <field name="OP">MULTIPLY</field>
                    <value name="A">
                      <shadow type="math_number" id="4d!2#QzC}dp+Up/i05ye">
                        <field name="NUM">1</field>
                      </shadow>
                      <block type="math_single" id="epdH`s7NHge3-fevy+V.">
                        <field name="OP">ABS</field>
                        <value name="NUM">
                          <shadow type="math_number" id="4,Tx{=flY|Lx?o-VfX2?">
                            <field name="NUM">9</field>
                          </shadow>
                          <block type="read_details" id=":!Jp5=jcH!.m,uK^ARKk">
                            <field name="DETAIL_INDEX">4</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <value name="B">
                      <shadow type="math_number" id="6iRW.jAja`|a^*QP|VI$">
                        <field name="NUM">1</field>
                      </shadow>
                      <block type="math_number" id="nzPJT@dpiu^)2D%=4K3#">
                        <field name="NUM">1</field>
                      </block>
                    </value>
                  </block>
                </value>
                <next>
                  <block type="controls_if" id="K+!Ur?K/L--1/$=,wqHh">
                    <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                    <value name="IF0">
                      <block type="logic_compare" id="$3?kGIfhbp6uytU0oFPy">
                        <field name="OP">NEQ</field>
                        <value name="A">
                          <block type="variables_get" id="64KE@JIKtkg%uRC|M9M]">
                            <field name="VAR" id=";wJj_I^Js9-1UR#/~_$1">Next Trade Condition</field>
                          </block>
                        </value>
                        <value name="B">
                          <block type="text" id=",@xG.%CoSs$;vo:Zz.zA">
                            <field name="TEXT">Even</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="variables_set" id="ta2gSTLRCgsb/sk,^87U">
                        <field name="VAR" id="mcPPu45l`UKxnH73P@w=">Prediction</field>
                        <value name="VALUE">
                          <block type="variables_get" id="bHptw%Zi7=mibc6^7T1Z">
                            <field name="VAR" id="(.pOfi2ikl^ae0}~D!5f">Prediction after loss</field>
                          </block>
                        </value>
                        <next>
                          <block type="variables_set" id="Uf/Dz4A?)h=j3C7T+IH(">
                            <field name="VAR" id=";wJj_I^Js9-1UR#/~_$1">Next Trade Condition</field>
                            <value name="VALUE">
                              <block type="text" id="C_|k%$h``^TDXTs]oCLF">
                                <field name="TEXT">Even</field>
                              </block>
                            </value>
                          </block>
                        </next>
                      </block>
                    </statement>
                    <statement name="ELSE">
                      <block type="variables_set" id="1;7}!68+(lJdm|qmT.i+">
                        <field name="VAR" id=";wJj_I^Js9-1UR#/~_$1">Next Trade Condition</field>
                        <value name="VALUE">
                          <block type="text" id="y`*,Av-dmcp:PDQr0[z,">
                            <field name="TEXT">Odd</field>
                          </block>
                        </value>
                      </block>
                    </statement>
                  </block>
                </next>
              </block>
            </statement>
            <statement name="ELSE">
              <block type="controls_if" id="7ruT(X*zPp8z+6WX.Fq+">
                <value name="IF0">
                  <block type="contract_check_result" id="@mDjdk]zPeIjR|dlsv?:">
                    <field name="CHECK_RESULT">win</field>
                  </block>
                </value>
                <statement name="DO0">
                  <block type="variables_set" id="He24nq%C%Xwn=C[rap91">
                    <field name="VAR" id="X[(S;ATE$Dbs3flXN{-m">Initial Amount</field>
                    <value name="VALUE">
                      <block type="variables_get" id="Yksi(o6.p7V%h@,ZJ./]">
                        <field name="VAR" id="ZC9jlKp0p+PVYAU:n7?-">Win Amount</field>
                      </block>
                    </value>
                    <next>
                      <block type="controls_if" id="I~v.)JBaeX5_KQI$f;xY">
                        <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                        <value name="IF0">
                          <block type="logic_compare" id="=x5JB-%*5_imJFLjQ*@^">
                            <field name="OP">NEQ</field>
                            <value name="A">
                              <block type="variables_get" id="Z)~:RZ[5e_C?mQ!q:4Ds">
                                <field name="VAR" id=";wJj_I^Js9-1UR#/~_$1">Next Trade Condition</field>
                              </block>
                            </value>
                            <value name="B">
                              <block type="text" id="dldyn.#+=@-~t6gb**]m">
                                <field name="TEXT">ss</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <statement name="DO0">
                          <block type="variables_set" id="4SRbpN4B_We+q-uOe3]Y">
                            <field name="VAR" id="mcPPu45l`UKxnH73P@w=">Prediction</field>
                            <value name="VALUE">
                              <block type="variables_get" id=":`%w?oD)0s*V{$XTHQ-E">
                                <field name="VAR" id="(.pOfi2ikl^ae0}~D!5f">Prediction after loss</field>
                              </block>
                            </value>
                            <next>
                              <block type="variables_set" id="OXiNNHqJjA@M0whh,|2J">
                                <field name="VAR" id=";wJj_I^Js9-1UR#/~_$1">Next Trade Condition</field>
                                <value name="VALUE">
                                  <block type="text" id="ce`=AaF*`p$cva^;50I!">
                                    <field name="TEXT">ss</field>
                                  </block>
                                </value>
                              </block>
                            </next>
                          </block>
                        </statement>
                        <statement name="ELSE">
                          <block type="variables_set" id=";Q5dw;*TriP9zf_p|7uR">
                            <field name="VAR" id="mcPPu45l`UKxnH73P@w=">Prediction</field>
                            <value name="VALUE">
                              <block type="variables_get" id="urnF}*Cf*bp`m;s)AZxw">
                                <field name="VAR" id="4T(l!T7tM|8YeN!7`h[5">Prediction Before loss</field>
                              </block>
                            </value>
                            <next>
                              <block type="variables_set" id="|05+[$6e3Acj=|P~?qTg">
                                <field name="VAR" id=";wJj_I^Js9-1UR#/~_$1">Next Trade Condition</field>
                                <value name="VALUE">
                                  <block type="text" id="5w#w[5By_$HW!c~lkX).">
                                    <field name="TEXT">jj</field>
                                  </block>
                                </value>
                              </block>
                            </next>
                          </block>
                        </statement>
                      </block>
                    </next>
                  </block>
                </statement>
              </block>
            </statement>
            <next>
              <block type="trade_again" id="-^NYgtlbvfH(9%/:;_PO"></block>
            </next>
          </block>
        </statement>
      </block>
    </statement>
  </block>
  <block type="before_purchase" id=";xs]Lgz;V{$3huSy@fNM" collapsed="true" deletable="false" x="0" y="928">
    <statement name="BEFOREPURCHASE_STACK">
      <block type="controls_if" id="1_MW-_1*:nR#sNiJmhI)">
        <mutation xmlns="http://www.w3.org/1999/xhtml" elseif="2" else="1"></mutation>
        <value name="IF0">
          <block type="logic_compare" id="__k=F+B.L,f+*,eI7r-!">
            <field name="OP">EQ</field>
            <value name="A">
              <block type="variables_get" id="B5MLoVrS~N}P7X!W^;QT">
                <field name="VAR" id=";wJj_I^Js9-1UR#/~_$1">Next Trade Condition</field>
              </block>
            </value>
            <value name="B">
              <block type="text" id="K([;37aqT~?|:/5T[z.R">
                <field name="TEXT">Even</field>
              </block>
            </value>
          </block>
        </value>
        <statement name="DO0">
          <block type="controls_if" id="P_4.m#zJM)`$$TNA$*#9">
            <value name="IF0">
              <block type="logic_compare" id="(O=k)j7gy!J!p4%f}*9J">
                <field name="OP">LTE</field>
                <value name="A">
                  <block type="variables_get" id="PxbN0FoQ4A.Zs]Q~A_uH">
                    <field name="VAR" id="lBWo1Ta1T]7SBMFX8k9R">Tick 1</field>
                  </block>
                </value>
                <value name="B">
                  <block type="math_number" id="X!/Zb^8vDz;D]E6s8)Jf">
                    <field name="NUM">4</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO0">
              <block type="controls_if" id="{MSnAZtCq~$bfP(^:-}C">
                <value name="IF0">
                  <block type="logic_compare" id="fE[CqV_=O#CSR#Exs@%$">
                    <field name="OP">LTE</field>
                    <value name="A">
                      <block type="variables_get" id="jLcNdWHi+-%[iIK1f;YF">
                        <field name="VAR" id="t~60Rry*.,:.w/VFe!$F">Tick 2</field>
                      </block>
                    </value>
                    <value name="B">
                      <block type="math_number" id="0a%o+ayWj=1jdtYyVX%P">
                        <field name="NUM">4</field>
                      </block>
                    </value>
                  </block>
                </value>
                <statement name="DO0">
                  <block type="controls_if" id="E.8Bat0Yxcj)Ew@luOW)">
                    <value name="IF0">
                      <block type="logic_compare" id="u(k7NXdxFYA0]*w:Q?|Y">
                        <field name="OP">LTE</field>
                        <value name="A">
                          <block type="variables_get" id="X%nV2Kno![XR7[K;^sX2">
                            <field name="VAR" id="KBmmw!H,}psHLCZX=nXr">Tick 3</field>
                          </block>
                        </value>
                        <value name="B">
                          <block type="math_number" id="_~:W](jVV0FfXMS`!w_B">
                            <field name="NUM">4</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="contract_changer_block" id="pOs-`L8%r:),hmIvC-v7">
                        <field name="CONTRACT_CHANGER">DIGITOVER</field>
                        <next>
                          <block type="apollo_purchase" id="QcDsD14)%qjH}/tc7/]Y">
                            <field name="PURCHASE_LIST">DIGITOVER</field>
                          </block>
                        </next>
                      </block>
                    </statement>
                  </block>
                </statement>
              </block>
            </statement>
          </block>
        </statement>
        <value name="IF1">
          <block type="logic_compare" id="+G.@mu!*/)jIO|KuD]V5">
            <field name="OP">EQ</field>
            <value name="A">
              <block type="variables_get" id="B;phY0VgZXy$~Y{l,Lal">
                <field name="VAR" id=";wJj_I^Js9-1UR#/~_$1">Next Trade Condition</field>
              </block>
            </value>
            <value name="B">
              <block type="text" id="V,,OjkbYLrMp2F1s=qG}">
                <field name="TEXT">ss</field>
              </block>
            </value>
          </block>
        </value>
        <statement name="DO1">
          <block type="controls_if" id="*60[P.Z8m07!`CCKsMw2">
            <value name="IF0">
              <block type="logic_compare" id="FR~]exP7EI8k!ems6qqy">
                <field name="OP">NEQ</field>
                <value name="A">
                  <block type="variables_get" id="Frip,x%CRPoSg:J)pS4v">
                    <field name="VAR" id="lBWo1Ta1T]7SBMFX8k9R">Tick 1</field>
                  </block>
                </value>
                <value name="B">
                  <block type="variables_get" id="l}X1u4Y}+cV|x#zRGRq%">
                    <field name="VAR" id="(.pOfi2ikl^ae0}~D!5f">Prediction after loss</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO0">
              <block type="controls_if" id="iMdn!@2L#z{G?,cO*JFW">
                <value name="IF0">
                  <block type="logic_compare" id=",YyKFDA9:mdss_2kUBpt">
                    <field name="OP">NEQ</field>
                    <value name="A">
                      <block type="variables_get" id="Vm_U7iZYVM0W,oKU|0)m">
                        <field name="VAR" id="t~60Rry*.,:.w/VFe!$F">Tick 2</field>
                      </block>
                    </value>
                    <value name="B">
                      <block type="variables_get" id="IOJpSC22s=|msxDU(~O?">
                        <field name="VAR" id="(.pOfi2ikl^ae0}~D!5f">Prediction after loss</field>
                      </block>
                    </value>
                  </block>
                </value>
                <statement name="DO0">
                  <block type="controls_if" id="wm_q+gg4B@,Cvt*_Yf4M">
                    <value name="IF0">
                      <block type="logic_compare" id="W~ANBOe2Dv@3OgyS7qbo">
                        <field name="OP">NEQ</field>
                        <value name="A">
                          <block type="variables_get" id="CXy=ub?y/6o]4[fgFNe}">
                            <field name="VAR" id="KBmmw!H,}psHLCZX=nXr">Tick 3</field>
                          </block>
                        </value>
                        <value name="B">
                          <block type="variables_get" id="-XVF%ZS~}I;oR,|O!^(;">
                            <field name="VAR" id="(.pOfi2ikl^ae0}~D!5f">Prediction after loss</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="contract_changer_block" id="_h~SZ{y/3mJ|~pt?^8Bf">
                        <field name="CONTRACT_CHANGER">DIGITDIFF</field>
                        <next>
                          <block type="apollo_purchase" id="rR%Fw[!GI_Ru:A%YllrW">
                            <field name="PURCHASE_LIST">DIGITOVER</field>
                          </block>
                        </next>
                      </block>
                    </statement>
                  </block>
                </statement>
              </block>
            </statement>
          </block>
        </statement>
        <value name="IF2">
          <block type="logic_compare" id="VKl}pld;,xsj}~4S?LQC">
            <field name="OP">EQ</field>
            <value name="A">
              <block type="variables_get" id="?Drqa`C;1Q%%E{K{{c,U">
                <field name="VAR" id=";wJj_I^Js9-1UR#/~_$1">Next Trade Condition</field>
              </block>
            </value>
            <value name="B">
              <block type="text" id="/GgsZ.ao-}~lCKF$Oh9~">
                <field name="TEXT">jj</field>
              </block>
            </value>
          </block>
        </value>
        <statement name="DO2">
          <block type="controls_if" id="a7rC,o:28QAGj0Z3Gi-n">
            <value name="IF0">
              <block type="logic_compare" id="~YM!4+c`fG^Z(Vr2TZn_">
                <field name="OP">LTE</field>
                <value name="A">
                  <block type="variables_get" id="4IZ?ru1(Ea%Rfxpz_)Ma">
                    <field name="VAR" id="lBWo1Ta1T]7SBMFX8k9R">Tick 1</field>
                  </block>
                </value>
                <value name="B">
                  <block type="math_number" id="JY(qYhe%f3n{/,o,q^-}">
                    <field name="NUM">4</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO0">
              <block type="controls_if" id="Qw+M#[O.^(Z3TUd*T%+u">
                <value name="IF0">
                  <block type="logic_compare" id="xf[XKY#wU?5j8GDM4rs9">
                    <field name="OP">LTE</field>
                    <value name="A">
                      <block type="variables_get" id="Cn4*B*/7,hCq^+,*M$y_">
                        <field name="VAR" id="t~60Rry*.,:.w/VFe!$F">Tick 2</field>
                      </block>
                    </value>
                    <value name="B">
                      <block type="math_number" id="~rx(Ff3,~:KE{{Ppa}^X">
                        <field name="NUM">4</field>
                      </block>
                    </value>
                  </block>
                </value>
                <statement name="DO0">
                  <block type="controls_if" id=",7-*a!Q8QZ^0l8+}`w]d">
                    <value name="IF0">
                      <block type="logic_compare" id="!$6i:#[zIAE[[~tSD24-">
                        <field name="OP">LTE</field>
                        <value name="A">
                          <block type="variables_get" id="PDC$O2-6{ARpaLwA=K#R">
                            <field name="VAR" id="KBmmw!H,}psHLCZX=nXr">Tick 3</field>
                          </block>
                        </value>
                        <value name="B">
                          <block type="math_number" id="Er.4Hsw6[-c]|jU_o!D4">
                            <field name="NUM">4</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="contract_changer_block" id=",RqRlG8=%(zGq;s:|740">
                        <field name="CONTRACT_CHANGER">DIGITOVER</field>
                        <next>
                          <block type="apollo_purchase" id=")pu*ynx7+L|LS}TmyEA1">
                            <field name="PURCHASE_LIST">DIGITOVER</field>
                          </block>
                        </next>
                      </block>
                    </statement>
                  </block>
                </statement>
              </block>
            </statement>
          </block>
        </statement>
        <statement name="ELSE">
          <block type="controls_if" id="%2Y}k(#,b%0c6Em;OV)z">
            <value name="IF0">
              <block type="math_number_property" id="e-Fix70QuOAh0`?+T,vX">
                <mutation xmlns="http://www.w3.org/1999/xhtml" divisor_input="false"></mutation>
                <field name="PROPERTY">ODD</field>
                <value name="NUMBER_TO_CHECK">
                  <shadow type="math_number" id="X/Q6TE/(uY[?@QmCA6Ni">
                    <field name="NUM">0</field>
                  </shadow>
                  <block type="variables_get" id="Mlhsa0`B=Ss,@.IE]2wk">
                    <field name="VAR" id="lBWo1Ta1T]7SBMFX8k9R">Tick 1</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO0">
              <block type="controls_if" id="jb?m9#a]Co3p[O~*[.F)">
                <value name="IF0">
                  <block type="math_number_property" id="{`NGr8o_OCt6f?DB9lQH">
                    <mutation xmlns="http://www.w3.org/1999/xhtml" divisor_input="false"></mutation>
                    <field name="PROPERTY">ODD</field>
                    <value name="NUMBER_TO_CHECK">
                      <shadow type="math_number" id="X/Q6TE/(uY[?@QmCA6Ni">
                        <field name="NUM">0</field>
                      </shadow>
                      <block type="variables_get" id="MlzaznjpJ7cW@wKa6Pq1">
                        <field name="VAR" id="t~60Rry*.,:.w/VFe!$F">Tick 2</field>
                      </block>
                    </value>
                  </block>
                </value>
                <statement name="DO0">
                  <block type="controls_if" id="rq~U.q05@4}+gViN_X#V">
                    <value name="IF0">
                      <block type="math_number_property" id="N!~n$tPcec+j0.bXd!F8">
                        <mutation xmlns="http://www.w3.org/1999/xhtml" divisor_input="false"></mutation>
                        <field name="PROPERTY">ODD</field>
                        <value name="NUMBER_TO_CHECK">
                          <shadow type="math_number" id="X/Q6TE/(uY[?@QmCA6Ni">
                            <field name="NUM">0</field>
                          </shadow>
                          <block type="variables_get" id="WHZoR}z|K$}a6P*B^vRp">
                            <field name="VAR" id="KBmmw!H,}psHLCZX=nXr">Tick 3</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="contract_changer_block" id=")`hd+/yB_abg1R8DE@ZW">
                        <field name="CONTRACT_CHANGER">DIGITEVEN</field>
                        <next>
                          <block type="apollo_purchase" id="RV_:Dg{`V[4_0/J8y}R#">
                            <field name="PURCHASE_LIST">DIGITOVER</field>
                          </block>
                        </next>
                      </block>
                    </statement>
                  </block>
                </statement>
              </block>
            </statement>
          </block>
        </statement>
      </block>
    </statement>
  </block>
  <block type="tick_analysis" id="%_(x7I|8le[%R36@pmz%" x="-31" y="2737">
    <statement name="TICKANALYSIS_STACK">
      <block type="variables_set" id="Bt,,Kc0D!=P?(jRrlY|5">
        <field name="VAR" id="lBWo1Ta1T]7SBMFX8k9R">Tick 1</field>
        <value name="VALUE">
          <block type="lists_getIndex" id="3yEhc@8qQxp@s~oiA^0S">
            <mutation xmlns="http://www.w3.org/1999/xhtml" statement="false" at="true"></mutation>
            <field name="MODE">GET</field>
            <field name="WHERE">FROM_END</field>
            <value name="VALUE">
              <block type="lastDigitList" id="4HFNnYzh@OLp;Yj$;)xq"></block>
            </value>
            <value name="AT">
              <block type="math_number" id="O/BzU9nLppO1,h]_Wloi">
                <field name="NUM">1</field>
              </block>
            </value>
          </block>
        </value>
        <next>
          <block type="variables_set" id="UF6eCZMBmZMFU/djp-]U">
            <field name="VAR" id="t~60Rry*.,:.w/VFe!$F">Tick 2</field>
            <value name="VALUE">
              <block type="lists_getIndex" id="{,D/1WLvQwYa8hC}sHaZ">
                <mutation xmlns="http://www.w3.org/1999/xhtml" statement="false" at="true"></mutation>
                <field name="MODE">GET</field>
                <field name="WHERE">FROM_END</field>
                <value name="VALUE">
                  <block type="lastDigitList" id="`aHA7;Unks9kvh-5L?ZO"></block>
                </value>
                <value name="AT">
                  <block type="math_number" id="(vwnqTo4epqrq.BgLIN2">
                    <field name="NUM">2</field>
                  </block>
                </value>
              </block>
            </value>
            <next>
              <block type="variables_set" id="v6B2Ysdn-`ATWQ{k|FA`">
                <field name="VAR" id="KBmmw!H,}psHLCZX=nXr">Tick 3</field>
                <value name="VALUE">
                  <block type="lists_getIndex" id="_3BlX85C0$6t0[rV~y0]">
                    <mutation xmlns="http://www.w3.org/1999/xhtml" statement="false" at="true"></mutation>
                    <field name="MODE">GET</field>
                    <field name="WHERE">FROM_END</field>
                    <value name="VALUE">
                      <block type="lastDigitList" id="fTlmWtTsI}cIN_jcGz~C"></block>
                    </value>
                    <value name="AT">
                      <block type="math_number" id="NdOO*|((FL{pU;}]c}7#">
                        <field name="NUM">3</field>
                      </block>
                    </value>
                  </block>
                </value>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
  </block>
</xml>