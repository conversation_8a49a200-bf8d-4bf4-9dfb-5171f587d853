<xml xmlns="https://developers.google.com/blockly/xml" is_dbot="true" collection="false">
  
  <variables>
    
    <variable id="W4$:ZQCmEz#8+=4ysv5Y">Loss</variable>
    
    <variable id="j}8O`Vs+RJljIwPu-_:_">Stake</variable>
    
    <variable id="cGiEb[SpixQhfH7kwj$t">text</variable>
    
    <variable id=".40y8^bkMB:oo!f#OAyE">text1</variable>
    
    <variable id="Q+V=Pm?V[H((+u)PaN%4">text2</variable>
    
    <variable id="mXtFswo{p,|%W1:V-$+r">Target Profit</variable>
    
    <variable id="%L?;380E6Lr^3b.%}t5Q">stake 2</variable>
    
    <variable id="BltpSZ[^gSQJ~#pp,o~u">text3</variable>
  
  </variables>
  
  <block type="trade_definition" id="{Xe?7tAgiRgF`-65Q,M7" deletable="false" x="0" y="110">
    
    <statement name="TRADE_OPTIONS">
      
      <block type="trade_definition_market" id="9vvPR3hh^`olXYF]rVUT" deletable="false" movable="false">
        
        <field name="MARKET_LIST">synthetic_index</field>
        
        <field name="SUBMARKET_LIST">random_index</field>
        
        <field name="SYMBOL_LIST">R_100</field>
        
        <next>
          
          <block type="trade_definition_tradetype" id="Bzqu:Pe|L9pjed.a*w#^" deletable="false" movable="false">
            
            <field name="TRADETYPECAT_LIST">digits</field>
            
            <field name="TRADETYPE_LIST">evenodd</field>
            
            <next>
              
              <block type="trade_definition_contracttype" id=",^%+8Jc`(F5mBBRxQL#L" deletable="false" movable="false">
                
                <field name="TYPE_LIST">DIGITODD</field>
                
                <next>
                  
                  <block type="trade_definition_candleinterval" id="YK^U-yy}-0!gD-R!2x0~" deletable="false" movable="false">
                    
                    <field name="CANDLEINTERVAL_LIST">60</field>
                    
                    <next>
                      
                      <block type="trade_definition_restartbuysell" id="XVy`HA2:WiUHFc8(O_LK" deletable="false" movable="false">
                        
                        <field name="TIME_MACHINE_ENABLED">FALSE</field>
                        
                        <next>
                          
                          <block type="trade_definition_restartonerror" id="bcqgS=;Y@dMd;PX4EpL(" deletable="false" movable="false">
                            
                            <field name="RESTARTONERROR">TRUE</field>
                          
                          </block>
                        
                        </next>
                      
                      </block>
                    
                    </next>
                  
                  </block>
                
                </next>
              
              </block>
            
            </next>
          
          </block>
        
        </next>
      
      </block>
    
    </statement>
    
    <statement name="INITIALIZATION">
      
      <block type="text_print" id="[wO]v.3kT8-?*kFc}@{(" collapsed="true">
        
        <value name="TEXT">
          
          <shadow type="text" id="K/6A5^}}#slZ=2sUnm.W">
            
            <field name="TEXT">About To Print Dollars 💵 All The Best</field>
          
          </shadow>
        
        </value>
        
        <next>
          
          <block type="variables_set" id="dU~!5$|7`D*|w?q/m,yl">
            
            <field name="VAR" id="W4$:ZQCmEz#8+=4ysv5Y">Loss</field>
            
            <value name="VALUE">
              
              <block type="math_number" id="%Z`t-:cO;x0SywIc?NeZ">
                
                <field name="NUM">1000</field>
              
              </block>
            
            </value>
            
            <next>
              
              <block type="variables_set" id="$0%XFpK$MIjs4M3Q3j9z">
                
                <field name="VAR" id="mXtFswo{p,|%W1:V-$+r">Target Profit</field>
                
                <value name="VALUE">
                  
                  <block type="math_number" id="F(/;h%J2D%+g6w:6$8rb">
                    
                    <field name="NUM">500</field>
                  
                  </block>
                
                </value>
                
                <next>
                  
                  <block type="variables_set" id="xC$v:}_JmUb,Epk!i({7">
                    
                    <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">Stake</field>
                    
                    <value name="VALUE">
                      
                      <block type="math_number" id="G%-aD]-o#ES`dn(O7V+n">
                        
                        <field name="NUM">2</field>
                      
                      </block>
                    
                    </value>
                    
                    <next>
                      
                      <block type="variables_set" id="D;ECV-zCRxNA@l$$%tB_">
                        
                        <field name="VAR" id="%L?;380E6Lr^3b.%}t5Q">stake 2</field>
                        
                        <value name="VALUE">
                          
                          <block type="math_number" id="7D^;1]CgqiqXu6GjkU(m">
                            
                            <field name="NUM">2</field>
                          
                          </block>
                        
                        </value>
                      
                      </block>
                    
                    </next>
                  
                  </block>
                
                </next>
              
              </block>
            
            </next>
          
          </block>
        
        </next>
      
      </block>
    
    </statement>
    
    <statement name="SUBMARKET">
      
      <block type="trade_definition_tradeoptions" id="5i;ldL]*/s^/|kPS[f9*">
        
        <mutation xmlns="http://www.w3.org/1999/xhtml" has_first_barrier="false" has_second_barrier="false" has_prediction="false"></mutation>
        
        <field name="DURATIONTYPE_LIST">t</field>
        
        <value name="DURATION">
          
          <block type="math_number" id="=Xsbz?=J5TaE*zRSfNBv">
            
            <field name="NUM">1</field>
          
          </block>
        
        </value>
        
        <value name="AMOUNT">
          
          <block type="variables_get" id="S?#JYb5l{j.)H)I[=%/!">
            
            <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">Stake</field>
          
          </block>
        
        </value>
      
      </block>
    
    </statement>
  
  </block>
  
  <block type="during_purchase" id="chv=J|^VEqsT7HdM8Y-S" collapsed="true" x="714" y="110"></block>
  
  <block type="after_purchase" id="hAks(y2P$WGH)xvOAM`C" x="724" y="181">
    
    <statement name="AFTERPURCHASE_STACK">
      
      <block type="controls_if" id="uQO1M8s(VYhd1xeMn2Q+">
        
        <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
        
        <value name="IF0">
          
          <block type="contract_check_result" id="EdI}|:)P;.Me-g}V6db^">
            
            <field name="CHECK_RESULT">win</field>
          
          </block>
        
        </value>
        
        <statement name="DO0">
          
          <block type="text_join" id="jMEh9hZkezim|Q]qEeko">
            
            <field name="VARIABLE" id="cGiEb[SpixQhfH7kwj$t">text</field>
            
            <statement name="STACK">
              
              <block type="text_statement" id="w?K5jZg;*.sCQ125.kC,">
                
                <value name="TEXT">
                  
                  <shadow type="text" id="B3dJ(6=!bfNrTimqI-(2">
                    
                    <field name="TEXT"></field>
                  
                  </shadow>
                  
                  <block type="text" id="$rGb[9;vr[i_L]FY?/oh">
                    
                    <field name="TEXT">Scanned</field>
                  
                  </block>
                
                </value>
                
                <next>
                  
                  <block type="text_statement" id="d==/zIVZh]oimh9aP(JE">
                    
                    <value name="TEXT">
                      
                      <shadow type="text" id="R?x1WlCYTcT5VrMcyMg]">
                        
                        <field name="TEXT"></field>
                      
                      </shadow>
                      
                      <block type="read_details" id="2ci?bytq=sGFW1QSx|p%">
                        
                        <field name="DETAIL_INDEX">4</field>
                      
                      </block>
                    
                    </value>
                  
                  </block>
                
                </next>
              
              </block>
            
            </statement>
            
            <next>
              
              <block type="notify" id="pR5Wm5+SOxi4H{+M9?g*">
                
                <field name="NOTIFICATION_TYPE">success</field>
                
                <field name="NOTIFICATION_SOUND">earned-money</field>
                
                <value name="MESSAGE">
                  
                  <block type="variables_get" id="WYWWT`;PHu`]P|!6r=kZ">
                    
                    <field name="VAR" id="cGiEb[SpixQhfH7kwj$t">text</field>
                  
                  </block>
                
                </value>
                
                <next>
                  
                  <block type="variables_set" id="NOO,?/$D|?.adJyWvJp[">
                    
                    <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">Stake</field>
                    
                    <value name="VALUE">
                      
                      <block type="variables_get" id="A(ihtvhtz,ujYA[UW`i%">
                        
                        <field name="VAR" id="%L?;380E6Lr^3b.%}t5Q">stake 2</field>
                      
                      </block>
                    
                    </value>
                  
                  </block>
                
                </next>
              
              </block>
            
            </next>
          
          </block>
        
        </statement>
        
        <statement name="ELSE">
          
          <block type="text_join" id="30!eT%(?T#5~i(S?cR/C">
            
            <field name="VARIABLE" id=".40y8^bkMB:oo!f#OAyE">text1</field>
            
            <statement name="STACK">
              
              <block type="text_statement" id="L~6%)|Fa0MG,-l.^Jl[w">
                
                <value name="TEXT">
                  
                  <shadow type="text" id="aL%=rs(c##W^M9P_Y(oa">
                    
                    <field name="TEXT"></field>
                  
                  </shadow>
                  
                  <block type="text" id="@gY!B]<EMAIL>)s7bgw7">
                    
                    <field name="TEXT">Scanned: </field>
                  
                  </block>
                
                </value>
                
                <next>
                  
                  <block type="text_statement" id="0x2t{B2$Ov+#B{?_3ed{">
                    
                    <value name="TEXT">
                      
                      <shadow type="text" id="VIq4T}^nm#pEt~/|0s!r">
                        
                        <field name="TEXT"></field>
                      
                      </shadow>
                      
                      <block type="math_single" id="Ow],xC+/t?W6ZLSk9#{w">
                        
                        <field name="OP">ABS</field>
                        
                        <value name="NUM">
                          
                          <shadow type="math_number" id="8r|A_]#wnvSaE:APKu3(">
                            
                            <field name="NUM">9</field>
                          
                          </shadow>
                          
                          <block type="read_details" id="jZCG-LfDjNKB^-7HKA%u">
                            
                            <field name="DETAIL_INDEX">4</field>
                          
                          </block>
                        
                        </value>
                      
                      </block>
                    
                    </value>
                  
                  </block>
                
                </next>
              
              </block>
            
            </statement>
            
            <next>
              
              <block type="notify" id="q0{{k=wr@/9FatxTF1hf">
                
                <field name="NOTIFICATION_TYPE">warn</field>
                
                <field name="NOTIFICATION_SOUND">error</field>
                
                <value name="MESSAGE">
                  
                  <block type="variables_get" id="]~F6-wgRn0t.Ju^uD[Ug">
                    
                    <field name="VAR" id=".40y8^bkMB:oo!f#OAyE">text1</field>
                  
                  </block>
                
                </value>
                
                <next>
                  
                  <block type="math_change" id="hGT-F41b}t/^-kQQeQ4E">
                    
                    <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">Stake</field>
                    
                    <value name="DELTA">
                      
                      <shadow type="math_number" id="Yw]@Eiiz+jvP/|(N0h3m">
                        
                        <field name="NUM">1</field>
                      
                      </shadow>
                      
                      <block type="math_arithmetic" id="!][bvZY(rd~xJ%t{Pk7g">
                        
                        <field name="OP">MULTIPLY</field>
                        
                        <value name="A">
                          
                          <shadow type="math_number" id="y3P5L@zXOx2ex;+|xeTu">
                            
                            <field name="NUM">1</field>
                          
                          </shadow>
                          
                          <block type="math_single" id="!5t=^@wU]++A$IEqfO%s">
                            
                            <field name="OP">ABS</field>
                            
                            <value name="NUM">
                              
                              <shadow type="math_number" id="RrNg%IP0s]nKIf4T=?ss">
                                
                                <field name="NUM">9</field>
                              
                              </shadow>
                              
                              <block type="read_details" id=",KNfA}[5a%zbwJ[vwCCO">
                                
                                <field name="DETAIL_INDEX">4</field>
                              
                              </block>
                            
                            </value>
                          
                          </block>
                        
                        </value>
                        
                        <value name="B">
                          
                          <shadow type="math_number" id="y}cz%fwe)`|wN2A4?mx5">
                            
                            <field name="NUM">1</field>
                          
                          </shadow>
                          
                          <block type="math_number" id="[#JR?|p$tm/wO,;+|]d}">
                            
                            <field name="NUM">1</field>
                          
                          </block>
                        
                        </value>
                      
                      </block>
                    
                    </value>
                    
                    <next>
                      
                      <block type="controls_if" id="0(xJ,KK6ENBrNneJHCtp">
                        
                        <value name="IF0">
                          
                          <block type="logic_compare" id="cU#:r(+ZaeRj7-r6Kq)[">
                            
                            <field name="OP">GTE</field>
                            
                            <value name="A">
                              
                              <block type="math_single" id="Vn?JZWW*U6Iwq,iT=VK6">
                                
                                <field name="OP">ABS</field>
                                
                                <value name="NUM">
                                  
                                  <shadow type="math_number" id="/`R8.%O-0l-*2q!6(M/:">
                                    
                                    <field name="NUM">9</field>
                                  
                                  </shadow>
                                  
                                  <block type="read_details" id="N@{i@$9U.nv?^f9:V~5,">
                                    
                                    <field name="DETAIL_INDEX">4</field>
                                  
                                  </block>
                                
                                </value>
                              
                              </block>
                            
                            </value>
                            
                            <value name="B">
                              
                              <block type="variables_get" id="hN$Bp,!|4ZCT;Wc=UX)s">
                                
                                <field name="VAR" id="W4$:ZQCmEz#8+=4ysv5Y">Loss</field>
                              
                              </block>
                            
                            </value>
                          
                          </block>
                        
                        </value>
                        
                        <statement name="DO0">
                          
                          <block type="variables_set" id="mo%iXjm);6Q344WHN~K:">
                            
                            <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">Stake</field>
                            
                            <value name="VALUE">
                              
                              <block type="variables_get" id="A6C3s#p=f?y4GNhD;Kb[">
                                
                                <field name="VAR" id="%L?;380E6Lr^3b.%}t5Q">stake 2</field>
                              
                              </block>
                            
                            </value>
                          
                          </block>
                        
                        </statement>
                      
                      </block>
                    
                    </next>
                  
                  </block>
                
                </next>
              
              </block>
            
            </next>
          
          </block>
        
        </statement>
        
        <next>
          
          <block type="text_join" id="rGuX=_;pUC%!1f.~v~or">
            
            <field name="VARIABLE" id="Q+V=Pm?V[H((+u)PaN%4">text2</field>
            
            <statement name="STACK">
              
              <block type="text_statement" id=")T:i(7LZ.#olT~KrsdPW">
                
                <value name="TEXT">
                  
                  <shadow type="text" id="+gHiw}DL*`/dwkUN]Lq7">
                    
                    <field name="TEXT"></field>
                  
                  </shadow>
                  
                  <block type="text" id="ls@A(z#0;yeh{Fpc=|[,">
                    
                    <field name="TEXT">Total Profit: </field>
                  
                  </block>
                
                </value>
                
                <next>
                  
                  <block type="text_statement" id="h$,yy+pkKW.W;4sqAD{,">
                    
                    <value name="TEXT">
                      
                      <shadow type="text" id="Hr,BqC;~{AYl(|Y!*f!}">
                        
                        <field name="TEXT"></field>
                      
                      </shadow>
                      
                      <block type="total_profit" id="cLI;X@!HF`cO;(wRKh.L"></block>
                    
                    </value>
                  
                  </block>
                
                </next>
              
              </block>
            
            </statement>
            
            <next>
              
              <block type="notify" id="zD[z,XI^ecI3wB*MEX^!">
                
                <field name="NOTIFICATION_TYPE">info</field>
                
                <field name="NOTIFICATION_SOUND">silent</field>
                
                <value name="MESSAGE">
                  
                  <block type="variables_get" id="-5-1z_9}n5)GR8$H!Bx9">
                    
                    <field name="VAR" id="Q+V=Pm?V[H((+u)PaN%4">text2</field>
                  
                  </block>
                
                </value>
                
                <next>
                  
                  <block type="controls_if" id="sf;3oIV5#Bd:pT3zXn@5">
                    
                    <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                    
                    <value name="IF0">
                      
                      <block type="logic_compare" id="NRheeDx2pE_*[)d5D*W3">
                        
                        <field name="OP">LT</field>
                        
                        <value name="A">
                          
                          <block type="total_profit" id=";65!S^l2ifOA,8ea5%:S"></block>
                        
                        </value>
                        
                        <value name="B">
                          
                          <block type="variables_get" id="[mf[C*RX?xmDE{cy[19W">
                            
                            <field name="VAR" id="mXtFswo{p,|%W1:V-$+r">Target Profit</field>
                          
                          </block>
                        
                        </value>
                      
                      </block>
                    
                    </value>
                    
                    <statement name="DO0">
                      
                      <block type="trade_again" id="Ol3dWX+VqIuPlGld!N#s"></block>
                    
                    </statement>
                    
                    <statement name="ELSE">
                      
                      <block type="text_join" id="mLgYcRI%IZomqGOJyK~0">
                        
                        <field name="VARIABLE" id="BltpSZ[^gSQJ~#pp,o~u">text3</field>
                        
                        <statement name="STACK">
                          
                          <block type="text_statement" id="PUzScd(JaH:D_9Vz!(-c">
                            
                            <value name="TEXT">
                              
                              <shadow type="text" id="ULF#??.#P7Z$c@McSXIs">
                                
                                <field name="TEXT"></field>
                              
                              </shadow>
                              
                              <block type="text" id="@rP]YL_:%.y45b|)VLWf">
                                
                                <field name="TEXT">Dollars Printed Successfully🤑Take Profit : </field>
                              
                              </block>
                            
                            </value>
                            
                            <next>
                              
                              <block type="text_statement" id="!q(m[=94A%wr_8]D9,cG">
                                
                                <value name="TEXT">
                                  
                                  <shadow type="text" id="|^L0uIhahJV{.jD}]qsn">
                                    
                                    <field name="TEXT"></field>
                                  
                                  </shadow>
                                  
                                  <block type="total_profit" id="T`p;ZGK^.axPv1F*[g;Z"></block>
                                
                                </value>
                              
                              </block>
                            
                            </next>
                          
                          </block>
                        
                        </statement>
                        
                        <next>
                          
                          <block type="text_print" id="lLZnPz74[=ER4vp3S~bc">
                            
                            <value name="TEXT">
                              
                              <shadow type="text" id="Nwc.D76Y7,exS_~e-?BY">
                                
                                <field name="TEXT">abc</field>
                              
                              </shadow>
                              
                              <block type="variables_get" id="erFPW*k7UIe8T=~BL6rQ">
                                
                                <field name="VAR" id="BltpSZ[^gSQJ~#pp,o~u">text3</field>
                              
                              </block>
                            
                            </value>
                          
                          </block>
                        
                        </next>
                      
                      </block>
                    
                    </statement>
                  
                  </block>
                
                </next>
              
              </block>
            
            </next>
          
          </block>
        
        </next>
      
      </block>
    
    </statement>
  
  </block>
  
  <block type="before_purchase" id="Nf~)SWT$evs8jnw(vdQl" deletable="false" x="0" y="968">
    
    <statement name="BEFOREPURCHASE_STACK">
      
      <block type="purchase" id="qA[^vwB~B{:0]3Healfb">
        
        <field name="PURCHASE_LIST">DIGITODD</field>
      
      </block>
    
    </statement>
  
  </block>
  
  <block type="math_number" id="D5T8yROmlXkVVCc}.PEG" disabled="true" x="0" y="1856">
    
    <field name="NUM">5</field>
  
  </block>
  
  <block type="text" id="dFIk{E?NH:t(HIBvkOHg" collapsed="true" disabled="true" x="0" y="1944">
    
    <field name="TEXT">Expert  Speed Bot</field>
  
  </block>

</xml>