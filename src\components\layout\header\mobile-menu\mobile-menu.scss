.mobile-menu {
    height: 100%;

    &__toggle {
        padding-inline-start: 1rem;
        padding-inline-end: 1rem;
        border-inline-end: 1px solid var(--general-section-1);
        display: flex;
        height: 100%;
        cursor: pointer;
    }

    &__footer {
        justify-content: center;
        height: 4rem;
    }

    &__header {
        display: flex;
        padding-inline-end: 1.6rem;
        padding-inline-start: 0.4rem;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        &__language {
            display: flex;
            align-items: center;
            gap: 0.4rem;
        }
    }

    &__content {
        display: flex;
        flex-direction: column;
        height: 100%;

        &__platform {
            display: flex;
            justify-content: center;
            align-items: center;
            border-bottom: 1px solid var(--general-section-1);
            min-height: 7rem;
            cursor: pointer;
        }

        .deriv-context-menu {
            box-shadow: none !important;
        }

        &__items {
            position: relative;
            height: 100%;
            padding-top: 1rem;

            &--padding {
                padding-inline-start: 4.8rem;
                padding-inline-end: 1.6rem;
            }

            &--bottom-border {
                border-bottom: 1px solid var(--general-section-1);
            }

            &__item {
                height: 5.6rem;
                cursor: pointer;
                width: 100%;

                .deriv-toggle-switch {
                    margin-left: auto;
                    margin-right: 1.6rem;
                }

                &--active {
                    .deriv-text {
                        color: var(--text-prominent);
                        font-weight: bold;
                    }
                }
            }

            &--right-margin {
                margin-inline-end: 1.6rem;
            }

            &--chevron {
                position: absolute;
                right: 1.5rem;
            }

            &__icons {
                g > path,
                path {
                    fill: var(--text-prominent);
                }
            }
        }
    }

    &__language-drawer {
        padding-inline-start: 0.8rem;
        padding-inline-end: 0.8rem;

        button {
            height: auto;
        }

        .languages-modal__body-button {
            height: 88px;
            width: 133px;
            display: flex;
            align-items: center;
            flex-direction: column;
            padding: 8px;

            .deriv-text {
                color: var(--text-prominent) !important;
            }

            &-selected {
                border: 1px solid var(--brand-blue, var(--brand-secondary));
                border-radius: 4px;
            }
        }
    }

    &__back-btn {
        display: flex;
        align-items: center;
        padding: 3.2rem;
        padding-top: 2rem;
        cursor: pointer;

        span {
            margin-inline-start: 1.6rem;
        }
    }

    .deriv-drawer__container {
        background: var(--general-main-1);
    }

    &__toggle,
    &__back-btn,
    .deriv-drawer__header__close-btn {
        button > svg > path,
        svg > path {
            fill: var(--text-prominent);
        }
    }
}
