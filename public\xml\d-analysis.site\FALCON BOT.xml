<xml xmlns="https://developers.google.com/blockly/xml" is_dbot="true" collection="false">
  <variables>
    <variable id="9dQ4tsj$@`vWpu;:2{K=">Stake</variable>
    <variable id="!A+WNsCL7LW[X/-IL!Uk">loop</variable>
    <variable id="`05B+e.2ANRJ9_i|v_CA">First_trade </variable>
    <variable id="!P]:?:q)?v?}qINF%J42">Win Stake</variable>
    <variable id="!T,V6]5_UQwL4;[wVu,s">tradesCounter</variable>
    <variable id="h~E{sLp%[!g*biOm|KFp">Entry_point</variable>
    <variable id=")vsQdUxN%]c3qIu%/?H9">Loop Stop </variable>
    <variable id="`=|V?TV%1c6]^Pvh=CK/">Loss</variable>
    <variable id=":Fbza.{0*q*jalJ+tc#.">Expected Profit</variable>
    <variable id="DNhF.v}O9v+rRicP?%xS">text</variable>
    <variable id="T{(x6lh:.m:xcqT0`!+3">maxLosses</variable>
    <variable id="G5b%GTn.jQKlh,1)ap7t">text1</variable>
    <variable id="BTQ{$u318X:bRnhP(mQ9">Stop Loss</variable>
    <variable id="2;2wvV6eBpaZEjwJ#y(1">tradingOption</variable>
    <variable id="7JojWW:8}EgD*|lq;S_$">list</variable>
    <variable id="7/Cs|{m_XjDwo::I6g5A">Random</variable>
    <variable id="#nKyoAm=Zh-Afx.zUa%f">Trend List</variable>
    <variable id="*fDA|@cvrn4wRkcZ,PWX">tradesNo</variable>
  </variables>
  <block type="trade_definition" id="a776q.sLW}$8Uj*t9i*V" deletable="false" x="0" y="60">
    <statement name="TRADE_OPTIONS">
      <block type="trade_definition_market" id="@~-wkYABRgD3ZJp3j^3!" deletable="false" movable="false">
        <field name="MARKET_LIST">synthetic_index</field>
        <field name="SUBMARKET_LIST">random_index</field>
        <field name="SYMBOL_LIST">1HZ10V</field>
        <next>
          <block type="trade_definition_tradetype" id="bEk7]/H576^0#sZkcav2" deletable="false" movable="false">
            <field name="TRADETYPECAT_LIST">digits</field>
            <field name="TRADETYPE_LIST">overunder</field>
            <next>
              <block type="trade_definition_contracttype" id="HA`feb8a^4**sTd.,HTL" deletable="false" movable="false">
                <field name="TYPE_LIST">both</field>
                <next>
                  <block type="trade_definition_candleinterval" id="ZKBDHZQoGi$gLl6.vojv" deletable="false" movable="false">
                    <field name="CANDLEINTERVAL_LIST">60</field>
                    <next>
                      <block type="trade_definition_restartbuysell" id="4sDxeIe#[L|IEYSL-oLF" deletable="false" movable="false">
                        <field name="TIME_MACHINE_ENABLED">FALSE</field>
                        <next>
                          <block type="trade_definition_restartonerror" id="3Dzz,)BOI1PKG*-HGU){" deletable="false" movable="false">
                            <field name="RESTARTONERROR">TRUE</field>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="INITIALIZATION">
      <block type="variables_set" id="P?L#KWk#eS$4tkth!;(%">
        <field name="VAR" id="9dQ4tsj$@`vWpu;:2{K=">Stake</field>
        <value name="VALUE">
          <block type="math_number" id="{Pa;X%SJB6A~oow#)USO">
            <field name="NUM">1</field>
          </block>
        </value>
        <next>
          <block type="variables_set" id="Bq_=gwO^QC0-Wgik=RwJ">
            <field name="VAR" id="!P]:?:q)?v?}qINF%J42">Win Stake</field>
            <value name="VALUE">
              <block type="math_number" id="35.AuWApTlWC]CMgzw@X">
                <field name="NUM">2.5</field>
              </block>
            </value>
            <next>
              <block type="variables_set" id="]aHRV[2VMwbYGTpt`,Hx">
                <field name="VAR" id="h~E{sLp%[!g*biOm|KFp">Entry_point</field>
                <value name="VALUE">
                  <block type="math_number" id="{_D`WNC4zPY|H,-q!(Q|">
                    <field name="NUM">9</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="m{O3:UuR`DY*Tr[I!12/">
                    <field name="VAR" id=":Fbza.{0*q*jalJ+tc#.">Expected Profit</field>
                    <value name="VALUE">
                      <block type="math_number" id="/Mb|JfyTPGc;$K4^0?e2">
                        <field name="NUM">0.4</field>
                      </block>
                    </value>
                    <next>
                      <block type="variables_set" id="v9|h+;tiC45m1X.r9lRr">
                        <field name="VAR" id="BTQ{$u318X:bRnhP(mQ9">Stop Loss</field>
                        <value name="VALUE">
                          <block type="math_number" id="W%uA!SZ6Gvsi`$eToN8B">
                            <field name="NUM">50</field>
                          </block>
                        </value>
                        <next>
                          <block type="lists_create_with" id="~!OkZ?ylWMwd;^68c]`-" collapsed="true">
                            <field name="VARIABLE" id="7JojWW:8}EgD*|lq;S_$">list</field>
                            <next>
                              <block type="variables_set" id=",KT*(nxrs2dhq^sw!^zY" collapsed="true">
                                <field name="VAR" id="#nKyoAm=Zh-Afx.zUa%f">Trend List</field>
                                <value name="VALUE">
                                  <block type="variables_get" id="T4:~TtHaoiD{U14k/zi?">
                                    <field name="VAR" id="7JojWW:8}EgD*|lq;S_$">list</field>
                                  </block>
                                </value>
                                <next>
                                  <block type="text_join" id="5({+h(QW)]3NU?n8@_Qz" collapsed="true">
                                    <field name="VARIABLE" id="DNhF.v}O9v+rRicP?%xS">text</field>
                                    <statement name="STACK">
                                      <block type="text_statement" id="/)V|g#}{wjwu9m?4Nzkx">
                                        <value name="TEXT">
                                          <shadow type="text" id=";h/[-HZLM}P@BADkV2g+">
                                            <field name="TEXT"></field>
                                          </shadow>
                                          <block type="text" id="+Ya(AI#O@Y9GP-]i4(1=">
                                            <field name="TEXT">Falcon Bot</field>
                                          </block>
                                        </value>
                                        <next>
                                          <block type="text_statement" id="/)dV)CK8v9w7bX#KdFG,">
                                            <value name="TEXT">
                                              <shadow type="text" id="fHyJ2ItG14b4w~uWK7D)">
                                                <field name="TEXT"></field>
                                              </shadow>
                                              <block type="variables_get" id="R6a!=laBMK/Mn*yYrNW-">
                                                <field name="VAR" id=":Fbza.{0*q*jalJ+tc#.">Expected Profit</field>
                                              </block>
                                            </value>
                                            <next>
                                              <block type="text_statement" id="uBDrCTFw{E9[(Xj!]Prm" collapsed="true">
                                                <value name="TEXT">
                                                  <shadow type="text" id="koZasf/}DmbXme?|MaWd">
                                                    <field name="TEXT"></field>
                                                  </shadow>
                                                  <block type="text" id="2zf|JIB%NAk+-n1|/Ie~">
                                                    <field name="TEXT">  |  Stop Loss $</field>
                                                  </block>
                                                </value>
                                                <next>
                                                  <block type="text_statement" id="2vU6Lbz;^?2((x$k)QBd">
                                                    <value name="TEXT">
                                                      <shadow type="text" id="vV.G_hUG1dDMB8u%0xAP">
                                                        <field name="TEXT"></field>
                                                      </shadow>
                                                      <block type="variables_get" id="_c6fLVq6ftVMkAOp2giY">
                                                        <field name="VAR" id="BTQ{$u318X:bRnhP(mQ9">Stop Loss</field>
                                                      </block>
                                                    </value>
                                                  </block>
                                                </next>
                                              </block>
                                            </next>
                                          </block>
                                        </next>
                                      </block>
                                    </statement>
                                    <next>
                                      <block type="notify" id="|vGru6B($^:OP|m,fzTS" collapsed="true">
                                        <field name="NOTIFICATION_TYPE">info</field>
                                        <field name="NOTIFICATION_SOUND">earned-money</field>
                                        <value name="MESSAGE">
                                          <shadow type="text" id="p%{(HQwId_!$2ny^AbH9">
                                            <field name="TEXT">abc</field>
                                          </shadow>
                                          <block type="variables_get" id="/crT@;nvB~`){6{z.Db:">
                                            <field name="VAR" id="DNhF.v}O9v+rRicP?%xS">text</field>
                                          </block>
                                        </value>
                                        <next>
                                          <block type="variables_set" id="Ay|Uij:{ST`+=@Fvf.tF" collapsed="true">
                                            <field name="VAR" id="`=|V?TV%1c6]^Pvh=CK/">Loss</field>
                                            <value name="VALUE">
                                              <block type="math_number" id="$}gVGQx`{gVV;bdwxg!1">
                                                <field name="NUM">0</field>
                                              </block>
                                            </value>
                                            <next>
                                              <block type="variables_set" id="R~pk{B9$WsDOKjcUg@DK" collapsed="true">
                                                <field name="VAR" id="2;2wvV6eBpaZEjwJ#y(1">tradingOption</field>
                                                <value name="VALUE">
                                                  <block type="math_number" id="{=gSnf[OT7C_SA|[E[HF">
                                                    <field name="NUM">0</field>
                                                  </block>
                                                </value>
                                                <next>
                                                  <block type="variables_set" id="=Dz`9WRk@D=kpyG12]G#" collapsed="true">
                                                    <field name="VAR" id="T{(x6lh:.m:xcqT0`!+3">maxLosses</field>
                                                    <value name="VALUE">
                                                      <block type="math_number" id="}ejjkAO{_+j6ZCq$@dfz">
                                                        <field name="NUM">8</field>
                                                      </block>
                                                    </value>
                                                    <next>
                                                      <block type="variables_set" id="c84MaIYWs*!}N]jC%]Q?" collapsed="true">
                                                        <field name="VAR" id="*fDA|@cvrn4wRkcZ,PWX">tradesNo</field>
                                                        <value name="VALUE">
                                                          <block type="math_number" id="3.!A}VVUQVOg}{Ca=yBk">
                                                            <field name="NUM">100</field>
                                                          </block>
                                                        </value>
                                                        <next>
                                                          <block type="variables_set" id="C}hA,iZ]M=R0)6Sq=#%s" collapsed="true">
                                                            <field name="VAR" id="!T,V6]5_UQwL4;[wVu,s">tradesCounter</field>
                                                            <value name="VALUE">
                                                              <block type="math_number" id="TysbqJ:JG?.kR|+YueB%">
                                                                <field name="NUM">0</field>
                                                              </block>
                                                            </value>
                                                            <next>
                                                              <block type="variables_set" id="l@%lM*ez~KOSF@~f,eXl" collapsed="true">
                                                                <field name="VAR" id=")vsQdUxN%]c3qIu%/?H9">Loop Stop </field>
                                                                <value name="VALUE">
                                                                  <block type="logic_boolean" id="p)5Y|`Oi^#d6:~aK-t#9">
                                                                    <field name="BOOL">FALSE</field>
                                                                  </block>
                                                                </value>
                                                              </block>
                                                            </next>
                                                          </block>
                                                        </next>
                                                      </block>
                                                    </next>
                                                  </block>
                                                </next>
                                              </block>
                                            </next>
                                          </block>
                                        </next>
                                      </block>
                                    </next>
                                  </block>
                                </next>
                              </block>
                            </next>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="SUBMARKET">
      <block type="controls_whileUntil" id="dCI91+Gl3B`|iLxoxgi?" collapsed="true">
        <field name="MODE">UNTIL</field>
        <value name="BOOL">
          <block type="logic_compare" id="9}rxWHia[FRQ~K9~IQfH">
            <field name="OP">EQ</field>
            <value name="A">
              <block type="variables_get" id="jTfYji~xpCiEtq;|RUv+">
                <field name="VAR" id=")vsQdUxN%]c3qIu%/?H9">Loop Stop </field>
              </block>
            </value>
            <value name="B">
              <block type="logic_boolean" id="U^}c8jF{hZ:,avP#P97h">
                <field name="BOOL">TRUE</field>
              </block>
            </value>
          </block>
        </value>
        <statement name="DO">
          <block type="timeout" id="OVM:[6IEAysNy@/vI;Z~">
            <statement name="TIMEOUTSTACK">
              <block type="controls_if" id="u8x#!JOo72{x8c=]eRW:">
                <value name="IF0">
                  <block type="logic_compare" id="FwMK6`RM}p*{ud6WDN31">
                    <field name="OP">EQ</field>
                    <value name="A">
                      <block type="last_digit" id="[b|$H},;HXjNUgi1kxH{"></block>
                    </value>
                    <value name="B">
                      <block type="variables_get" id="OX;3OmL)d[?K-UQGk1*J">
                        <field name="VAR" id="h~E{sLp%[!g*biOm|KFp">Entry_point</field>
                      </block>
                    </value>
                  </block>
                </value>
                <statement name="DO0">
                  <block type="variables_set" id="IU:?cm^?xV?o;Ev)w?t;">
                    <field name="VAR" id=")vsQdUxN%]c3qIu%/?H9">Loop Stop </field>
                    <value name="VALUE">
                      <block type="logic_boolean" id="yMc0~JU:[7f`?K2],6-|">
                        <field name="BOOL">TRUE</field>
                      </block>
                    </value>
                  </block>
                </statement>
                <next>
                  <block type="text_join" id="Ru_Q#qC6?M6y*Xavq5,m">
                    <field name="VARIABLE" id="DNhF.v}O9v+rRicP?%xS">text</field>
                    <statement name="STACK">
                      <block type="text_statement" id="9I*m:_qnq1)8u*t)i_kg">
                        <value name="TEXT">
                          <shadow type="text" id="iIt;)PBK4)[n+$xGCBdC">
                            <field name="TEXT">Last Digit:</field>
                          </shadow>
                        </value>
                        <next>
                          <block type="text_statement" id="pz7tdQjF!Qrb%G3zb,QY">
                            <value name="TEXT">
                              <shadow type="text" id="{;/xGx=q].P*!@viP7v*">
                                <field name="TEXT"></field>
                              </shadow>
                              <block type="last_digit" id="ss(^]_*}1a7Nu.-i|M/O"></block>
                            </value>
                          </block>
                        </next>
                      </block>
                    </statement>
                    <next>
                      <block type="notify" id="~,z;(r2X*+?bld%O?wV3">
                        <field name="NOTIFICATION_TYPE">warn</field>
                        <field name="NOTIFICATION_SOUND">silent</field>
                        <value name="MESSAGE">
                          <shadow type="text" id="ol=sjB80fqSZh1PFVHVb">
                            <field name="TEXT">abc</field>
                          </shadow>
                          <block type="variables_get" id="z}m(xt)]1}KA7M0TKh6o">
                            <field name="VAR" id="DNhF.v}O9v+rRicP?%xS">text</field>
                          </block>
                        </value>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </statement>
            <value name="SECONDS">
              <block type="math_number" id="jAofVB+nNjN32vgfrj!#">
                <field name="NUM">1</field>
              </block>
            </value>
          </block>
        </statement>
        <next>
          <block type="controls_if" id="c6)^OYffyG6q!LGvh_n{" collapsed="true">
            <value name="IF0">
              <block type="logic_compare" id="d1l(:|rB=Mtn{L`;P[gg">
                <field name="OP">EQ</field>
                <value name="A">
                  <block type="variables_get" id="2#U9-6yHRK.[=yIk]DbT">
                    <field name="VAR" id="`=|V?TV%1c6]^Pvh=CK/">Loss</field>
                  </block>
                </value>
                <value name="B">
                  <block type="variables_get" id="VvMa8N-1L10uV1?Bw:8W">
                    <field name="VAR" id="T{(x6lh:.m:xcqT0`!+3">maxLosses</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO0">
              <block type="variables_set" id="(!(S+5xjLT/GlhZ98_Qt">
                <field name="VAR" id="`=|V?TV%1c6]^Pvh=CK/">Loss</field>
                <value name="VALUE">
                  <block type="math_number" id="iCLa+sK;L;4%]E7]|SbQ">
                    <field name="NUM">0</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="Xi9,(fe|qwkc^AVca6H~">
                    <field name="VAR" id="9dQ4tsj$@`vWpu;:2{K=">Stake</field>
                    <value name="VALUE">
                      <block type="variables_get" id="{@_1gE3%X|jv=?UJ3PH5">
                        <field name="VAR" id="!P]:?:q)?v?}qINF%J42">Win Stake</field>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </statement>
            <next>
              <block type="controls_if" id="]l#mL|:4$knPt}wq}qo{" collapsed="true">
                <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                <value name="IF0">
                  <block type="logic_compare" id="A5`}-YOI2R~22bVP_bE]">
                    <field name="OP">EQ</field>
                    <value name="A">
                      <block type="variables_get" id="c7q)XXH{5F1.wG,}S8lv">
                        <field name="VAR" id="2;2wvV6eBpaZEjwJ#y(1">tradingOption</field>
                      </block>
                    </value>
                    <value name="B">
                      <block type="math_number" id="c.OL9/}0zRuX$Xl=vq4S">
                        <field name="NUM">0</field>
                      </block>
                    </value>
                  </block>
                </value>
                <statement name="DO0">
                  <block type="trade_definition_tradeoptions" id="z;O(U[6Qf8TmvMGz!TA!">
                    <mutation xmlns="http://www.w3.org/1999/xhtml" has_first_barrier="false" has_second_barrier="false" has_prediction="true"></mutation>
                    <field name="DURATIONTYPE_LIST">t</field>
                    <value name="DURATION">
                      <shadow type="math_number" id="u/..8Ms.~$A,T;Qd!`qi">
                        <field name="NUM">1</field>
                      </shadow>
                      <block type="math_number" id="OQ}czCExO)BhGb/$Kjyl">
                        <field name="NUM">1</field>
                      </block>
                    </value>
                    <value name="AMOUNT">
                      <shadow type="math_number" id="baSmP$+e^n3k%G(O3O/w">
                        <field name="NUM">1</field>
                      </shadow>
                      <block type="variables_get" id="l{hs`sReJ{[o=EjkqPlF">
                        <field name="VAR" id="9dQ4tsj$@`vWpu;:2{K=">Stake</field>
                      </block>
                    </value>
                    <value name="PREDICTION">
                      <shadow type="math_number_positive" id="ta]0[Wut|?;}Qp9XOoC0">
                        <field name="NUM">1</field>
                      </shadow>
                      <block type="logic_ternary" id="yt:1/1|T{:5VZzYDY3LN">
                        <value name="IF">
                          <block type="logic_compare" id="#DS2%-qyVOJB5ZIe=]8H">
                            <field name="OP">EQ</field>
                            <value name="A">
                              <block type="variables_get" id="pY5Vc|^s4vo$w)G`i91Y">
                                <field name="VAR" id="`=|V?TV%1c6]^Pvh=CK/">Loss</field>
                              </block>
                            </value>
                            <value name="B">
                              <block type="math_number" id="p`/KukZinM[W,VQtsKC_">
                                <field name="NUM">0</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <value name="THEN">
                          <block type="math_number" id="HqhnF9I]7OZYlrK$?@Eo">
                            <field name="NUM">9</field>
                          </block>
                        </value>
                        <value name="ELSE">
                          <block type="logic_ternary" id="`3p6NswE7Y97XsK-vGM5">
                            <value name="IF">
                              <block type="logic_compare" id="!Ur#6.8AvE[V7:@3X4XQ">
                                <field name="OP">GTE</field>
                                <value name="A">
                                  <block type="variables_get" id="AaVG}SvOgp47elND!H2/">
                                    <field name="VAR" id="`=|V?TV%1c6]^Pvh=CK/">Loss</field>
                                  </block>
                                </value>
                                <value name="B">
                                  <block type="math_number" id="]Y+tN{C3wnG$$^hJ9CB;">
                                    <field name="NUM">1</field>
                                  </block>
                                </value>
                              </block>
                            </value>
                            <value name="THEN">
                              <block type="math_number" id="`DW}[sAUV6krC9*K79Lc">
                                <field name="NUM">5</field>
                              </block>
                            </value>
                            <value name="ELSE">
                              <block type="logic_ternary" id="q+CDz^2,qtzQbSs6xFUU">
                                <value name="IF">
                                  <block type="logic_compare" id="A$;_~mX3r-q/Z[;YS[,x">
                                    <field name="OP">GTE</field>
                                    <value name="A">
                                      <block type="variables_get" id="M/w;j@G+%9]}~V$rwnf?">
                                        <field name="VAR" id="`=|V?TV%1c6]^Pvh=CK/">Loss</field>
                                      </block>
                                    </value>
                                    <value name="B">
                                      <block type="math_number" id="5LJ268I-]GbH$cOLwxu.">
                                        <field name="NUM">2</field>
                                      </block>
                                    </value>
                                  </block>
                                </value>
                                <value name="THEN">
                                  <block type="math_number" id="lq!TOZmM3tZPWpwx?sQd">
                                    <field name="NUM">5</field>
                                  </block>
                                </value>
                                <value name="ELSE">
                                  <block type="logic_ternary" id="rWr}k=I;Rt76f_v?_.=T">
                                    <value name="IF">
                                      <block type="logic_compare" id="b9s4;+6R,FVt:b0U_I?d">
                                        <field name="OP">GTE</field>
                                        <value name="A">
                                          <block type="variables_get" id=".Ub:Y#mmn9iYy?0D=ifF">
                                            <field name="VAR" id="`=|V?TV%1c6]^Pvh=CK/">Loss</field>
                                          </block>
                                        </value>
                                        <value name="B">
                                          <block type="math_number" id="`QSxmnDM,_:;]Qd=#48K">
                                            <field name="NUM">4</field>
                                          </block>
                                        </value>
                                      </block>
                                    </value>
                                    <value name="THEN">
                                      <block type="math_number" id="V=rn]U/#9U(R~eY,rz2(">
                                        <field name="NUM">5</field>
                                      </block>
                                    </value>
                                    <value name="ELSE">
                                      <block type="math_number" id="THDULG``x^?2M,WxeY+#">
                                        <field name="NUM">5</field>
                                      </block>
                                    </value>
                                  </block>
                                </value>
                              </block>
                            </value>
                          </block>
                        </value>
                      </block>
                    </value>
                  </block>
                </statement>
                <statement name="ELSE">
                  <block type="trade_definition_tradeoptions" id="0CP3!!JH`Wp5~L2p~:uY">
                    <mutation xmlns="http://www.w3.org/1999/xhtml" has_first_barrier="false" has_second_barrier="false" has_prediction="true"></mutation>
                    <field name="DURATIONTYPE_LIST">t</field>
                    <value name="DURATION">
                      <shadow type="math_number" id="D;9X_ctA+xCB`#Qs#4]@">
                        <field name="NUM">1</field>
                      </shadow>
                      <block type="math_number" id="i)eX;W1urjjv[W(]F1y#">
                        <field name="NUM">1</field>
                      </block>
                    </value>
                    <value name="AMOUNT">
                      <shadow type="math_number" id="SL,7lXJa;{LZ6]gy]kLD">
                        <field name="NUM">1</field>
                      </shadow>
                      <block type="variables_get" id="sV@4r^EIcbg4(S.CW4;K">
                        <field name="VAR" id="9dQ4tsj$@`vWpu;:2{K=">Stake</field>
                      </block>
                    </value>
                    <value name="PREDICTION">
                      <shadow type="math_number_positive" id="aJii_1En$v1ton?6RabR">
                        <field name="NUM">1</field>
                      </shadow>
                      <block type="logic_ternary" id="jHKiU`uAn6KOv*[cMt:T">
                        <value name="IF">
                          <block type="logic_compare" id="p_1,+gvl:Uv$+Un[E(H`">
                            <field name="OP">EQ</field>
                            <value name="A">
                              <block type="variables_get" id="pcOr#;3nM+P/lyf7G+*^">
                                <field name="VAR" id="`=|V?TV%1c6]^Pvh=CK/">Loss</field>
                              </block>
                            </value>
                            <value name="B">
                              <block type="math_number" id="qai@vQu`{hEe4}8mSMH2">
                                <field name="NUM">0</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <value name="THEN">
                          <block type="math_number" id="qN:b8jzq]}A,x4oVi*@K">
                            <field name="NUM">0</field>
                          </block>
                        </value>
                        <value name="ELSE">
                          <block type="logic_ternary" id="M595!;dJ`Jl%5K=$80gt">
                            <value name="IF">
                              <block type="logic_compare" id="sn(sG#7r)%o.fG_H}A!R">
                                <field name="OP">GTE</field>
                                <value name="A">
                                  <block type="variables_get" id="0KR04_uWhJkP#4YSz.lV">
                                    <field name="VAR" id="`=|V?TV%1c6]^Pvh=CK/">Loss</field>
                                  </block>
                                </value>
                                <value name="B">
                                  <block type="math_number" id="IC(DFKdj@PdOV(7%cJU1">
                                    <field name="NUM">1</field>
                                  </block>
                                </value>
                              </block>
                            </value>
                            <value name="THEN">
                              <block type="math_number" id="AZdH@$~T@5NNO9h#=wBz">
                                <field name="NUM">5</field>
                              </block>
                            </value>
                            <value name="ELSE">
                              <block type="logic_ternary" id="g,:h1@!08p9Kx=V%@G!g">
                                <value name="IF">
                                  <block type="logic_compare" id="gs|]7O4!H4U~F#x~zQ4l">
                                    <field name="OP">GTE</field>
                                    <value name="A">
                                      <block type="variables_get" id="1qy2KW;z`trY}d./T@8F">
                                        <field name="VAR" id="`=|V?TV%1c6]^Pvh=CK/">Loss</field>
                                      </block>
                                    </value>
                                    <value name="B">
                                      <block type="math_number" id="5)vtE,@uP(~97S{aBXof">
                                        <field name="NUM">2</field>
                                      </block>
                                    </value>
                                  </block>
                                </value>
                                <value name="THEN">
                                  <block type="math_number" id="A)X^T[0QgyVIzoYi+)RN">
                                    <field name="NUM">5</field>
                                  </block>
                                </value>
                                <value name="ELSE">
                                  <block type="logic_ternary" id="9@|B+w}-l`P::AsuyH8H">
                                    <value name="IF">
                                      <block type="logic_compare" id="OC?d9JjfJXvohiN(Yjwg">
                                        <field name="OP">GTE</field>
                                        <value name="A">
                                          <block type="variables_get" id="#;L1%!JgN8da@E5RWp:$">
                                            <field name="VAR" id="`=|V?TV%1c6]^Pvh=CK/">Loss</field>
                                          </block>
                                        </value>
                                        <value name="B">
                                          <block type="math_number" id="gX~j|7Fv]3p}uIO{H6|6">
                                            <field name="NUM">4</field>
                                          </block>
                                        </value>
                                      </block>
                                    </value>
                                    <value name="THEN">
                                      <block type="math_number" id=":YK:g2`Zk}Ti;SLnFn{9">
                                        <field name="NUM">5</field>
                                      </block>
                                    </value>
                                    <value name="ELSE">
                                      <block type="math_number" id="_1A$PJ|ZYatP]-z](3pU">
                                        <field name="NUM">5</field>
                                      </block>
                                    </value>
                                  </block>
                                </value>
                              </block>
                            </value>
                          </block>
                        </value>
                      </block>
                    </value>
                  </block>
                </statement>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
  </block>
  <block type="after_purchase" id="leXna=dZ|?aw|-]c1g=@" x="714" y="60">
    <statement name="AFTERPURCHASE_STACK">
      <block type="variables_set" id="/Vmjd2d:lPV6#5NjPJDb">
        <field name="VAR" id="!A+WNsCL7LW[X/-IL!Uk">loop</field>
        <value name="VALUE">
          <block type="logic_boolean" id="}Yrp%AxnS2Mm,_Y^w=bF">
            <field name="BOOL">FALSE</field>
          </block>
        </value>
        <next>
          <block type="controls_if" id="/e(Cu.:(caISq1%Zuw*x">
            <mutation xmlns="http://www.w3.org/1999/xhtml" elseif="1"></mutation>
            <value name="IF0">
              <block type="contract_check_result" id="T:H{e#z{PiP`);%4I|0*">
                <field name="CHECK_RESULT">win</field>
              </block>
            </value>
            <statement name="DO0">
              <block type="variables_set" id="yqb=B*pq[$53WZb/^UaQ">
                <field name="VAR" id="9dQ4tsj$@`vWpu;:2{K=">Stake</field>
                <value name="VALUE">
                  <block type="variables_get" id="MWK-^NGUpFdc=dZKbv6q">
                    <field name="VAR" id="!P]:?:q)?v?}qINF%J42">Win Stake</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="p[@^!AKkjH*ATLPijgZf">
                    <field name="VAR" id="`=|V?TV%1c6]^Pvh=CK/">Loss</field>
                    <value name="VALUE">
                      <block type="math_number" id="wXP):._*84@#,*g*{Y^C">
                        <field name="NUM">0</field>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </statement>
            <value name="IF1">
              <block type="contract_check_result" id="R$hVx1NCZLMrIbwn#TbN">
                <field name="CHECK_RESULT">loss</field>
              </block>
            </value>
            <statement name="DO1">
              <block type="variables_set" id="C#{9]4`5*aCBKncahZJD">
                <field name="VAR" id="`=|V?TV%1c6]^Pvh=CK/">Loss</field>
                <value name="VALUE">
                  <block type="math_arithmetic" id="QjnB}02;rvmn-AteNeKl">
                    <field name="OP">ADD</field>
                    <value name="A">
                      <shadow type="math_number" id="L.e?!.!eH%X5^Z7B~%7O">
                        <field name="NUM">1</field>
                      </shadow>
                      <block type="variables_get" id="27LBl]2Ti-c_9Oq5$f%N">
                        <field name="VAR" id="`=|V?TV%1c6]^Pvh=CK/">Loss</field>
                      </block>
                    </value>
                    <value name="B">
                      <shadow type="math_number" id=":V+ea/6,NB|^%T8p$W^J">
                        <field name="NUM">1</field>
                      </shadow>
                    </value>
                  </block>
                </value>
                <next>
                  <block type="controls_if" id="_z_?Uf+3A4+h`MWlLSX6">
                    <value name="IF0">
                      <block type="logic_compare" id="OguDrjMo]vz)2BaBI+]}">
                        <field name="OP">GTE</field>
                        <value name="A">
                          <block type="variables_get" id="*IbY#c[J*tV$9/;[_c%B">
                            <field name="VAR" id="`=|V?TV%1c6]^Pvh=CK/">Loss</field>
                          </block>
                        </value>
                        <value name="B">
                          <block type="math_number" id="L:1f|e.R2%c7Jj=EyGcU">
                            <field name="NUM">1</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="variables_set" id="Q%Fu[XQm|8hXOC3[u`Vl">
                        <field name="VAR" id="9dQ4tsj$@`vWpu;:2{K=">Stake</field>
                        <value name="VALUE">
                          <block type="math_arithmetic" id="=|4S.ZN-iwoLo~[X#0iW">
                            <field name="OP">MULTIPLY</field>
                            <value name="A">
                              <shadow type="math_number" id="qwABHm%]l44Ba);N.,H2">
                                <field name="NUM">1</field>
                              </shadow>
                              <block type="math_single" id="tbg(F}st2AHE1|a8h}pi">
                                <field name="OP">ABS</field>
                                <value name="NUM">
                                  <shadow type="math_number" id="Qb8}+h/*+wXc6QZYdb!G">
                                    <field name="NUM">9</field>
                                  </shadow>
                                  <block type="read_details" id="ePl9{/zxmvI17dLWJ;-$">
                                    <field name="DETAIL_INDEX">4</field>
                                  </block>
                                </value>
                              </block>
                            </value>
                            <value name="B">
                              <shadow type="math_number" id="8Tk3TerB/=!57m{;@Z$p">
                                <field name="NUM">2.1</field>
                              </shadow>
                            </value>
                          </block>
                        </value>
                      </block>
                    </statement>
                  </block>
                </next>
              </block>
            </statement>
            <next>
              <block type="controls_if" id="OMy^Eu]f(Bh/lX{tOM-}">
                <mutation xmlns="http://www.w3.org/1999/xhtml" elseif="1"></mutation>
                <value name="IF0">
                  <block type="contract_check_result" id="C#a*J~LN2AN2`[w[Ad%u">
                    <field name="CHECK_RESULT">loss</field>
                  </block>
                </value>
                <statement name="DO0">
                  <block type="controls_if" id="AOAYHb9`2TV=pIsA)8WH">
                    <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                    <value name="IF0">
                      <block type="logic_operation" id="Fs2OaN1d^GD5Wd*7!+Yq">
                        <field name="OP">AND</field>
                        <value name="A">
                          <block type="math_number_property" id="KnDB.oxYV.r#:$5SLHJ0">
                            <mutation xmlns="http://www.w3.org/1999/xhtml" divisor_input="false"></mutation>
                            <field name="PROPERTY">NEGATIVE</field>
                            <value name="NUMBER_TO_CHECK">
                              <shadow type="math_number" id="U?68$%]qkax,T$F7D9W+">
                                <field name="NUM">0</field>
                              </shadow>
                              <block type="total_profit" id="[jIl$2yGy%ZSMnP5O,;y"></block>
                            </value>
                          </block>
                        </value>
                        <value name="B">
                          <block type="logic_compare" id="gR4fe+x=~=^jZ/yL)k/}">
                            <field name="OP">GTE</field>
                            <value name="A">
                              <block type="math_single" id=";D#X1iiGp(.TbM,@z=|.">
                                <field name="OP">ABS</field>
                                <value name="NUM">
                                  <shadow type="math_number" id="C@x#a3Hd:GugOWRr}F7|">
                                    <field name="NUM">9</field>
                                  </shadow>
                                  <block type="total_profit" id="QKbKo}a$}nj}Ij|H;k(}"></block>
                                </value>
                              </block>
                            </value>
                            <value name="B">
                              <block type="variables_get" id=",U=[Gj*22L$$:Ug@A0!R">
                                <field name="VAR" id="BTQ{$u318X:bRnhP(mQ9">Stop Loss</field>
                              </block>
                            </value>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="set_sl" id="w!6au6?#W{xa8u@5[wLM"></block>
                    </statement>
                    <statement name="ELSE">
                      <block type="trade_again" id="c/Bz@kkaX?Aq(4M!6M@k"></block>
                    </statement>
                  </block>
                </statement>
                <value name="IF1">
                  <block type="contract_check_result" id="dk8w2eQAD+#?b/y@D7D4">
                    <field name="CHECK_RESULT">win</field>
                  </block>
                </value>
                <statement name="DO1">
                  <block type="controls_if" id="q.qVz8V.y,(!Q#MKO?cH">
                    <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                    <value name="IF0">
                      <block type="logic_compare" id="n=;m!qqYT|_EK{,@|r#]">
                        <field name="OP">GTE</field>
                        <value name="A">
                          <block type="total_profit" id="*RtO={eD+m]7}8.9z0I`"></block>
                        </value>
                        <value name="B">
                          <block type="variables_get" id="U.Rg#_h$A,|}d+y[.EqY">
                            <field name="VAR" id=":Fbza.{0*q*jalJ+tc#.">Expected Profit</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="set_tp" id="r`uQWwEu*NB}sK,Hfxkc"></block>
                    </statement>
                    <statement name="ELSE">
                      <block type="trade_again" id="!T$=CRH0;vj2y9ws8nA%"></block>
                    </statement>
                  </block>
                </statement>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
  </block>
  <block type="before_purchase" id="zvZA6zCqb3jc9L!VOb2q" collapsed="true" deletable="false" x="0" y="1448">
    <statement name="BEFOREPURCHASE_STACK">
      <block type="variables_set" id="i~+~)lIe3o:n,c=f{XWy">
        <field name="VAR" id="`05B+e.2ANRJ9_i|v_CA">First_trade </field>
        <value name="VALUE">
          <block type="logic_boolean" id="#qQ1u8#~YpP)/7(s%}dY">
            <field name="BOOL">TRUE</field>
          </block>
        </value>
        <next>
          <block type="variables_set" id="`{MH*b?Bs[o@M#B:sc*Q">
            <field name="VAR" id="!T,V6]5_UQwL4;[wVu,s">tradesCounter</field>
            <value name="VALUE">
              <block type="math_arithmetic" id="ulRR9U6C/1_.:0;quyI@">
                <field name="OP">ADD</field>
                <value name="A">
                  <shadow type="math_number" id="atg*7AgDz?N3=XT-5IK$">
                    <field name="NUM">1</field>
                  </shadow>
                  <block type="variables_get" id="uO[M1?rw0:JgY^pkX^hG">
                    <field name="VAR" id="!T,V6]5_UQwL4;[wVu,s">tradesCounter</field>
                  </block>
                </value>
                <value name="B">
                  <shadow type="math_number" id="%L^2Kz.Xe37p{^O,sN2:">
                    <field name="NUM">1</field>
                  </shadow>
                </value>
              </block>
            </value>
            <next>
              <block type="notify" id="y8D)3}d.A][=3rHKpv9:" collapsed="true">
                <field name="NOTIFICATION_TYPE">info</field>
                <field name="NOTIFICATION_SOUND">silent</field>
                <value name="MESSAGE">
                  <shadow type="text" id="-J2`Ru+x5?aixepZy_gv">
                    <field name="TEXT">AUTO DREAM V5 [BY PK]</field>
                  </shadow>
                </value>
                <next>
                  <block type="text_join" id="}$?fWC[x2Xmr3dM6ojK[">
                    <field name="VARIABLE" id="G5b%GTn.jQKlh,1)ap7t">text1</field>
                    <statement name="STACK">
                      <block type="text_statement" id="lEFe`^_dW(:p$]8ZZwfo">
                        <value name="TEXT">
                          <shadow type="text" id="vif!*dn?C`~KpkJQVGji">
                            <field name="TEXT"></field>
                          </shadow>
                          <block type="text" id="|fRL+9ix+k7g/oBqbq2T">
                            <field name="TEXT">Last Digit &gt;&gt; </field>
                          </block>
                        </value>
                        <next>
                          <block type="text_statement" id="CLA3*0VB@ur10khiu{Cp">
                            <value name="TEXT">
                              <shadow type="text" id="JOLHXX7DID+QcX4^[3.?">
                                <field name="TEXT"></field>
                              </shadow>
                              <block type="last_digit" id="6*}S?_{W(7SfrAZ9AvZt"></block>
                            </value>
                            <next>
                              <block type="text_statement" id="q8cc%xPW6el)~]b3`AO4">
                                <value name="TEXT">
                                  <shadow type="text" id="9gr;:(}Ydzo!_#u8]:75">
                                    <field name="TEXT"></field>
                                  </shadow>
                                </value>
                              </block>
                            </next>
                          </block>
                        </next>
                      </block>
                    </statement>
                    <next>
                      <block type="notify" id="n/MT]~Fm(~0vT_ij4Yuu">
                        <field name="NOTIFICATION_TYPE">info</field>
                        <field name="NOTIFICATION_SOUND">silent</field>
                        <value name="MESSAGE">
                          <shadow type="text" id="PD;:4uT6`%4S1fdgnyTl">
                            <field name="TEXT">[Optimus Binary Traders]</field>
                          </shadow>
                          <block type="variables_get" id="i~sl7piLnvtN%MNp@+lq">
                            <field name="VAR" id="G5b%GTn.jQKlh,1)ap7t">text1</field>
                          </block>
                        </value>
                        <next>
                          <block type="variables_set" id=":v;Ap$Nd4(8^6p!~19h_">
                            <field name="VAR" id="7/Cs|{m_XjDwo::I6g5A">Random</field>
                            <value name="VALUE">
                              <block type="math_random_int" id="]soQ`R(n@NyGlIZE]bEE">
                                <value name="FROM">
                                  <shadow type="math_number" id="(,h0^%MTSVQ%*Shz|qmc">
                                    <field name="NUM">0</field>
                                  </shadow>
                                </value>
                                <value name="TO">
                                  <shadow type="math_number" id="o(^$,cI6w^Dn)nCQx,IM">
                                    <field name="NUM">9</field>
                                  </shadow>
                                </value>
                              </block>
                            </value>
                            <next>
                              <block type="controls_if" id="ljZR#*!@iDEiNV2lPY28">
                                <mutation xmlns="http://www.w3.org/1999/xhtml" elseif="2" else="1"></mutation>
                                <value name="IF0">
                                  <block type="logic_operation" id="@!dX`98p?M{6I1@,!$e@">
                                    <field name="OP">AND</field>
                                    <value name="A">
                                      <block type="logic_compare" id="r3-Z.~`py(/!uwGf-/qH">
                                        <field name="OP">EQ</field>
                                        <value name="A">
                                          <block type="variables_get" id="i,E;@|A+B~iw;kfAibmy">
                                            <field name="VAR" id="`=|V?TV%1c6]^Pvh=CK/">Loss</field>
                                          </block>
                                        </value>
                                        <value name="B">
                                          <block type="math_number" id="[Ixk-_R`1l^6Y*tyz+j,">
                                            <field name="NUM">0</field>
                                          </block>
                                        </value>
                                      </block>
                                    </value>
                                    <value name="B">
                                      <block type="logic_compare" id="v?83(3@gH1K50Xx:8q*8">
                                        <field name="OP">GTE</field>
                                        <value name="A">
                                          <block type="variables_get" id="E4UCC^r|1!b#1+d_,]RW">
                                            <field name="VAR" id="7/Cs|{m_XjDwo::I6g5A">Random</field>
                                          </block>
                                        </value>
                                        <value name="B">
                                          <block type="math_number" id="?R9NbCn=Y1cRZ)ON)JdH">
                                            <field name="NUM">1</field>
                                          </block>
                                        </value>
                                      </block>
                                    </value>
                                  </block>
                                </value>
                                <statement name="DO0">
                                  <block type="notify" id="jHiA$lF*jk;Qbu-NJA;*">
                                    <field name="NOTIFICATION_TYPE">success</field>
                                    <field name="NOTIFICATION_SOUND">silent</field>
                                    <value name="MESSAGE">
                                      <shadow type="text" id="!jo6PfKw`ut75,TZGigw">
                                        <field name="TEXT">Now Trading Under 9</field>
                                      </shadow>
                                    </value>
                                    <next>
                                      <block type="controls_if" id="we{Ff(OYYD,Ax)lU_Epj">
                                        <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                                        <value name="IF0">
                                          <block type="logic_compare" id="uX}Y8!,#n7GiLmAtz!Vk">
                                            <field name="OP">EQ</field>
                                            <value name="A">
                                              <block type="variables_get" id="0?bfVUa-{U]~cn*ENsES">
                                                <field name="VAR" id="2;2wvV6eBpaZEjwJ#y(1">tradingOption</field>
                                              </block>
                                            </value>
                                            <value name="B">
                                              <block type="math_number" id="]k,H1I~J=udsGi@B*(9K">
                                                <field name="NUM">0</field>
                                              </block>
                                            </value>
                                          </block>
                                        </value>
                                        <statement name="DO0">
                                          <block type="apollo_purchase" id="%ON7W?|Rf:(LB/`ZoF$E">
                                            <field name="PURCHASE_LIST">DIGITUNDER</field>
                                          </block>
                                        </statement>
                                        <statement name="ELSE">
                                          <block type="apollo_purchase" id="AkA05g5d]nSdMf*G^?$s">
                                            <field name="PURCHASE_LIST">DIGITUNDER</field>
                                          </block>
                                        </statement>
                                      </block>
                                    </next>
                                  </block>
                                </statement>
                                <value name="IF1">
                                  <block type="logic_operation" id="nFB2qeK@J4_:SN2,Gl+U">
                                    <field name="OP">AND</field>
                                    <value name="A">
                                      <block type="logic_compare" id="Np?h2gtiPzJFdoc~VSNs">
                                        <field name="OP">GTE</field>
                                        <value name="A">
                                          <block type="variables_get" id="naV19emBuW9krh(KznM8">
                                            <field name="VAR" id="`=|V?TV%1c6]^Pvh=CK/">Loss</field>
                                          </block>
                                        </value>
                                        <value name="B">
                                          <block type="math_number" id="o?r,omR+/#ZUgl%sO{+U">
                                            <field name="NUM">1</field>
                                          </block>
                                        </value>
                                      </block>
                                    </value>
                                    <value name="B">
                                      <block type="logic_compare" id="Q`KeAO/r_I!v!-Qv]cri">
                                        <field name="OP">GTE</field>
                                        <value name="A">
                                          <block type="variables_get" id=";|ka3sG*P`BpESYt0ZrJ">
                                            <field name="VAR" id="7/Cs|{m_XjDwo::I6g5A">Random</field>
                                          </block>
                                        </value>
                                        <value name="B">
                                          <block type="math_number" id="Lzw3yvyIjdG2r9b}QhS{">
                                            <field name="NUM">6</field>
                                          </block>
                                        </value>
                                      </block>
                                    </value>
                                  </block>
                                </value>
                                <statement name="DO1">
                                  <block type="notify" id="=y2?F)HWDQU:eGGns3C}">
                                    <field name="NOTIFICATION_TYPE">success</field>
                                    <field name="NOTIFICATION_SOUND">silent</field>
                                    <value name="MESSAGE">
                                      <shadow type="text" id="^vrq!5$zRK5WZ9/Uv4XA">
                                        <field name="TEXT">Now Trading Under 5</field>
                                      </shadow>
                                    </value>
                                    <next>
                                      <block type="controls_if" id="p:(Uz]1eXFHTukX:UK~7">
                                        <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                                        <value name="IF0">
                                          <block type="logic_compare" id="CYGC$zWSBBXf1}V6^s.b">
                                            <field name="OP">EQ</field>
                                            <value name="A">
                                              <block type="variables_get" id="@l:9rr%29tGCclP2Aen6">
                                                <field name="VAR" id="2;2wvV6eBpaZEjwJ#y(1">tradingOption</field>
                                              </block>
                                            </value>
                                            <value name="B">
                                              <block type="math_number" id="qLE8pU,h*6EFVUvC2,w/">
                                                <field name="NUM">0</field>
                                              </block>
                                            </value>
                                          </block>
                                        </value>
                                        <statement name="DO0">
                                          <block type="apollo_purchase" id="B;Afvu4Yas[G9H%Ty$H`">
                                            <field name="PURCHASE_LIST">DIGITUNDER</field>
                                          </block>
                                        </statement>
                                        <statement name="ELSE">
                                          <block type="apollo_purchase" id="~{d|CJ:PK~b8hY|%1WRI">
                                            <field name="PURCHASE_LIST">DIGITUNDER</field>
                                          </block>
                                        </statement>
                                      </block>
                                    </next>
                                  </block>
                                </statement>
                                <value name="IF2">
                                  <block type="logic_operation" id="OD3H;QghitvA?VdTTlt;">
                                    <field name="OP">AND</field>
                                    <value name="A">
                                      <block type="logic_compare" id="g6`:Ncjk_LBYc~F8y$rm">
                                        <field name="OP">GTE</field>
                                        <value name="A">
                                          <block type="variables_get" id="14svWZF5[~/b]fJ;3/J@">
                                            <field name="VAR" id="`=|V?TV%1c6]^Pvh=CK/">Loss</field>
                                          </block>
                                        </value>
                                        <value name="B">
                                          <block type="math_number" id="Ig;g.XjmJPOeJ~:c)K%O">
                                            <field name="NUM">2</field>
                                          </block>
                                        </value>
                                      </block>
                                    </value>
                                    <value name="B">
                                      <block type="logic_compare" id="twO6]9X{J^6*y~?kGQ(s">
                                        <field name="OP">LTE</field>
                                        <value name="A">
                                          <block type="variables_get" id="u*tY$MpokQs}e_,V(Ej4">
                                            <field name="VAR" id="7/Cs|{m_XjDwo::I6g5A">Random</field>
                                          </block>
                                        </value>
                                        <value name="B">
                                          <block type="math_number" id="9oG-ndJ:pHD,trHWDXgm">
                                            <field name="NUM">4</field>
                                          </block>
                                        </value>
                                      </block>
                                    </value>
                                  </block>
                                </value>
                                <statement name="DO2">
                                  <block type="notify" id="QXH_r6IZY{=#WfV#@7PC">
                                    <field name="NOTIFICATION_TYPE">success</field>
                                    <field name="NOTIFICATION_SOUND">silent</field>
                                    <value name="MESSAGE">
                                      <shadow type="text" id="B01cj!o{xw,Me;QKO)di">
                                        <field name="TEXT">Still Trading Under 5</field>
                                      </shadow>
                                    </value>
                                    <next>
                                      <block type="controls_if" id="o.QGV^4M@#a(:W|1@fq:">
                                        <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                                        <value name="IF0">
                                          <block type="logic_compare" id="FaZwp8fm(aon{$]GDnoK">
                                            <field name="OP">EQ</field>
                                            <value name="A">
                                              <block type="variables_get" id="ObX.oK,gZ%Td(*MSD1:x">
                                                <field name="VAR" id="2;2wvV6eBpaZEjwJ#y(1">tradingOption</field>
                                              </block>
                                            </value>
                                            <value name="B">
                                              <block type="math_number" id="*3mKbOg-1q{?4{SJFVUq">
                                                <field name="NUM">0</field>
                                              </block>
                                            </value>
                                          </block>
                                        </value>
                                        <statement name="DO0">
                                          <block type="apollo_purchase" id=":5ei$jKFoxQ#W2s,;w}Y">
                                            <field name="PURCHASE_LIST">DIGITUNDER</field>
                                          </block>
                                        </statement>
                                        <statement name="ELSE">
                                          <block type="apollo_purchase" id="+,5gu%/7||E[GJ7X/ZzD">
                                            <field name="PURCHASE_LIST">DIGITUNDER</field>
                                          </block>
                                        </statement>
                                      </block>
                                    </next>
                                  </block>
                                </statement>
                                <statement name="ELSE">
                                  <block type="controls_if" id="nwtZFp+4J{po/O]I?)C$">
                                    <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                                    <value name="IF0">
                                      <block type="logic_compare" id="wW{?Sy_-DZ}}/rjQ]n.b">
                                        <field name="OP">EQ</field>
                                        <value name="A">
                                          <block type="variables_get" id="?SxOIeK=U3i,9%PC7cSU">
                                            <field name="VAR" id="2;2wvV6eBpaZEjwJ#y(1">tradingOption</field>
                                          </block>
                                        </value>
                                        <value name="B">
                                          <block type="math_number" id="3}P34bo{C/N=gQA-s6vU">
                                            <field name="NUM">0</field>
                                          </block>
                                        </value>
                                      </block>
                                    </value>
                                    <statement name="DO0">
                                      <block type="apollo_purchase" id="zhNE![[nVoAE`o((2x,9">
                                        <field name="PURCHASE_LIST">DIGITUNDER</field>
                                      </block>
                                    </statement>
                                    <statement name="ELSE">
                                      <block type="apollo_purchase" id="s~vIc)Ip~j4^no0@kKy=">
                                        <field name="PURCHASE_LIST">DIGITUNDER</field>
                                      </block>
                                    </statement>
                                  </block>
                                </statement>
                                <next>
                                  <block type="controls_if" id="pO.NAv~?`npn=|BIW3Mz">
                                    <value name="IF0">
                                      <block type="logic_compare" id="?U|-JkOl.m4v30`%B.du">
                                        <field name="OP">GTE</field>
                                        <value name="A">
                                          <block type="variables_get" id="Ey]B_kqXx9+IREL/x8eV">
                                            <field name="VAR" id="!T,V6]5_UQwL4;[wVu,s">tradesCounter</field>
                                          </block>
                                        </value>
                                        <value name="B">
                                          <block type="variables_get" id="[*fc#bOZ|]|*-6~xG_/b">
                                            <field name="VAR" id="*fDA|@cvrn4wRkcZ,PWX">tradesNo</field>
                                          </block>
                                        </value>
                                      </block>
                                    </value>
                                    <statement name="DO0">
                                      <block type="controls_if" id=";ambQb+zW/(g0}XH6*Y{">
                                        <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                                        <value name="IF0">
                                          <block type="logic_compare" id="zdxqJp5D-eS[sJp#)+11">
                                            <field name="OP">EQ</field>
                                            <value name="A">
                                              <block type="variables_get" id="Zd%~3TCF^{aYE|n@lTJq">
                                                <field name="VAR" id="2;2wvV6eBpaZEjwJ#y(1">tradingOption</field>
                                              </block>
                                            </value>
                                            <value name="B">
                                              <block type="math_number" id="@12p%UQ._e*rpYDAT_?8">
                                                <field name="NUM">0</field>
                                              </block>
                                            </value>
                                          </block>
                                        </value>
                                        <statement name="DO0">
                                          <block type="variables_set" id="ev_f1k.yw(9D}ygIva`r">
                                            <field name="VAR" id="2;2wvV6eBpaZEjwJ#y(1">tradingOption</field>
                                            <value name="VALUE">
                                              <block type="math_number" id="Xhn3eJzH6|WUeq;9MB|[">
                                                <field name="NUM">1</field>
                                              </block>
                                            </value>
                                            <next>
                                              <block type="variables_set" id="~yL37{rRL*eM^5J*xyXL">
                                                <field name="VAR" id="!T,V6]5_UQwL4;[wVu,s">tradesCounter</field>
                                                <value name="VALUE">
                                                  <block type="math_number" id="IP@cB0MWAhA=mO0XfbB(">
                                                    <field name="NUM">0</field>
                                                  </block>
                                                </value>
                                              </block>
                                            </next>
                                          </block>
                                        </statement>
                                        <statement name="ELSE">
                                          <block type="variables_set" id="fUN31{DH$Y/On]LTjDhh">
                                            <field name="VAR" id="2;2wvV6eBpaZEjwJ#y(1">tradingOption</field>
                                            <value name="VALUE">
                                              <block type="math_number" id="oD?s(=Q~YVxt0y|:%y6U">
                                                <field name="NUM">0</field>
                                              </block>
                                            </value>
                                            <next>
                                              <block type="variables_set" id="$0pztvXxe,1jnumen#x}">
                                                <field name="VAR" id="!T,V6]5_UQwL4;[wVu,s">tradesCounter</field>
                                                <value name="VALUE">
                                                  <block type="math_number" id="kCMj4{}]E}U9Gx7vm,nF">
                                                    <field name="NUM">0</field>
                                                  </block>
                                                </value>
                                              </block>
                                            </next>
                                          </block>
                                        </statement>
                                      </block>
                                    </statement>
                                  </block>
                                </next>
                              </block>
                            </next>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
  </block>
</xml>