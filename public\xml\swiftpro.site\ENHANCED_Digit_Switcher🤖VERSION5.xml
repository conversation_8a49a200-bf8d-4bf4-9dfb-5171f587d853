<xml xmlns="https://developers.google.com/blockly/xml" is_dbot="true" collection="false">
  <variables>
    <variable id="Lbx,j;|@5lNVBvFf,wvR" type="" islocal="false" iscloud="false">Sum</variable>
    <variable id="_.:Q.F!2e1yKQ9hOfkpT" type="" islocal="false" iscloud="false">text</variable>
    <variable id="~J7_KCa,mrD@a`wFHCJr" type="" islocal="false" iscloud="false">LDP</variable>
  </variables>
  <block type="trade_definition" id="//NvY.+#[0JTo/r#8v(O" deletable="false" x="0" y="0">
    <statement name="TRADE_OPTIONS">
      <block type="trade_definition_market" id="w;rR8=S5w,b)|s#|[oZv" deletable="false" movable="false">
        <field name="MARKET_LIST">synthetic_index</field>
        <field name="SUBMARKET_LIST">random_index</field>
        <field name="SYMBOL_LIST">1HZ100V</field>
        <next>
          <block type="trade_definition_tradetype" id="|Y[![Fy3EwOJY%YkVk;V" deletable="false" movable="false">
            <field name="TRADETYPECAT_LIST">digits</field>
            <field name="TRADETYPE_LIST">overunder</field>
            <next>
              <block type="trade_definition_contracttype" id=",;NI/zn4*)xhF2YcCIX!" deletable="false" movable="false">
                <field name="TYPE_LIST">both</field>
                <next>
                  <block type="trade_definition_candleinterval" id=")vG.:*k7vky/UK{,o4-I" deletable="false" movable="false">
                    <field name="CANDLEINTERVAL_LIST">60</field>
                    <next>
                      <block type="trade_definition_restartbuysell" id="Y!*SEi4@Nqgf:0(%B6zL" deletable="false" movable="false">
                        <field name="TIME_MACHINE_ENABLED">FALSE</field>
                        <next>
                          <block type="trade_definition_restartonerror" id="?_Vz|p:S~wyWf4,wA+1?" deletable="false" movable="false">
                            <field name="RESTARTONERROR">TRUE</field>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="INITIALIZATION">
      <block type="variables_set" id="D+N^Kj6=[u0(v1/`j2+N">
        <field name="VAR" id="Lbx,j;|@5lNVBvFf,wvR">Sum</field>
        <value name="VALUE">
          <block type="math_number" id="H3K!LRZeF6]D%=VC*e`U">
            <field name="NUM">5.97</field>
          </block>
        </value>
        <next>
          <block type="variables_set" id="VCc.^%jQxgFidfydF(80">
            <field name="VAR" id="~J7_KCa,mrD@a`wFHCJr">LDP</field>
            <value name="VALUE">
              <block type="math_number" id="0W4sz.9qrhVV!UkK%1.d">
                <field name="NUM">1</field>
              </block>
            </value>
          </block>
        </next>
      </block>
    </statement>
    <statement name="SUBMARKET">
      <block type="trade_definition_tradeoptions" id="%hy.3y0o~7#c(;pt0-K3">
        <mutation xmlns="http://www.w3.org/1999/xhtml" has_first_barrier="false" has_second_barrier="false" has_prediction="true"></mutation>
        <field name="DURATIONTYPE_LIST">t</field>
        <value name="DURATION">
          <block type="math_number" id=".=}1gdAby)/D`.KFgBql">
            <field name="NUM">1</field>
          </block>
        </value>
        <value name="AMOUNT">
          <block type="variables_get" id="2.rm#*e~^Q]H?C`q#*jJ">
            <field name="VAR" id="Lbx,j;|@5lNVBvFf,wvR">Sum</field>
          </block>
        </value>
        <value name="PREDICTION">
          <shadow type="math_number_positive" id="Oa-K0$zRl]NBC(5PS#!y">
            <field name="NUM">1</field>
          </shadow>
          <block type="variables_get" id="ksa:^YU?y)B6NimyTuEu">
            <field name="VAR" id="~J7_KCa,mrD@a`wFHCJr">LDP</field>
          </block>
        </value>
      </block>
    </statement>
  </block>
  <block type="after_purchase" id="LQx9.UatHwZ%N%!v/DCP" x="1123" y="0">
    <statement name="AFTERPURCHASE_STACK">
      <block type="controls_if" id="hY?;.^yM}WgT}~=icv?k">
        <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
        <value name="IF0">
          <block type="contract_check_result" id="0oN^5kW@n(M[tgN#_$V=">
            <field name="CHECK_RESULT">win</field>
          </block>
        </value>
        <statement name="DO0">
          <block type="variables_set" id="U)z,:p(NcT)w)oxrf-r%">
            <field name="VAR" id="Lbx,j;|@5lNVBvFf,wvR">Sum</field>
            <value name="VALUE">
              <block type="math_number" id="f+S:rnU]9QUM6,j#l7VY">
                <field name="NUM">5.97</field>
              </block>
            </value>
          </block>
        </statement>
        <statement name="ELSE">
          <block type="math_change" id="k}fSj]qL2|PV/:jw.7ED">
            <field name="VAR" id="Lbx,j;|@5lNVBvFf,wvR">Sum</field>
            <value name="DELTA">
              <shadow type="math_number" id="rZ,kUQBybq)ftyVSIRvr">
                <field name="NUM">1</field>
              </shadow>
              <block type="math_number" id="YT_1fRu/.e#,^R}]m-QD">
                <field name="NUM">1</field>
              </block>
            </value>
          </block>
        </statement>
        <next>
          <block type="controls_if" id="yJD.$6,iTZh|OE-M}P!P">
            <value name="IF0">
              <block type="logic_compare" id="~*ipLi:sN|;S`+S4mz5+">
                <field name="OP">LTE</field>
                <value name="A">
                  <block type="variables_get" id="XpHXtrZ8p6K%A]#;=L+O">
                    <field name="VAR" id="Lbx,j;|@5lNVBvFf,wvR">Sum</field>
                  </block>
                </value>
                <value name="B">
                  <block type="math_number" id=".Ddpi/u-rB?*vl~:RAP9">
                    <field name="NUM">5000</field>
                  </block>
                </value>
              </block>
            </value>
            <statement name="DO0">
              <block type="controls_if" id="NHsR8D(@*$-*Jb2{]9OR">
                <value name="IF0">
                  <block type="math_number_property" id="8x]|#^s2mlNr46^Y~Cj6">
                    <mutation xmlns="http://www.w3.org/1999/xhtml" divisor_input="false"></mutation>
                    <field name="PROPERTY">EVEN</field>
                    <value name="NUMBER_TO_CHECK">
                      <shadow type="math_number" id="`x0zK]V6,ApEsYVOj!lt">
                        <field name="NUM">0</field>
                      </shadow>
                      <block type="variables_get" id="XcA;3c,@Y6itiD/@}2Gm">
                        <field name="VAR" id="~J7_KCa,mrD@a`wFHCJr">LDP</field>
                      </block>
                    </value>
                  </block>
                </value>
                <statement name="DO0">
                  <block type="variables_set" id="R0yp#+;C~kst_=;:*CKE">
                    <field name="VAR" id="~J7_KCa,mrD@a`wFHCJr">LDP</field>
                    <value name="VALUE">
                      <block type="math_number" id="C#$8T/U{Ic|6zWL1GBC%">
                        <field name="NUM">1</field>
                      </block>
                    </value>
                    <next>
                      <block type="trade_again" id=",|ge*5BZ9!1^m-:mRsvr"></block>
                    </next>
                  </block>
                </statement>
                <next>
                  <block type="controls_if" id="M]g^0VXYeqPVDaI87Z-@">
                    <value name="IF0">
                      <block type="math_number_property" id="vZ{[+L5UpolYrMe{=vaq">
                        <mutation xmlns="http://www.w3.org/1999/xhtml" divisor_input="false"></mutation>
                        <field name="PROPERTY">ODD</field>
                        <value name="NUMBER_TO_CHECK">
                          <shadow type="math_number" id="#p4-ZDBY+/E$3,wfG##m">
                            <field name="NUM">0</field>
                          </shadow>
                          <block type="variables_get" id="y-fe]*q;+~(?v5HX*4C[">
                            <field name="VAR" id="~J7_KCa,mrD@a`wFHCJr">LDP</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="variables_set" id="K^Kc:k`0S~q|R2KE3zr{">
                        <field name="VAR" id="~J7_KCa,mrD@a`wFHCJr">LDP</field>
                        <value name="VALUE">
                          <block type="math_number" id="EN1}X6[Q#O6n2H[7SWA[">
                            <field name="NUM">8</field>
                          </block>
                        </value>
                        <next>
                          <block type="trade_again" id="zd@Z_$xW8I%d=5MjbuzH"></block>
                        </next>
                      </block>
                    </statement>
                  </block>
                </next>
              </block>
            </statement>
          </block>
        </next>
      </block>
    </statement>
  </block>
  <block type="before_purchase" id="?7rW%1W4zDse1oS54cMh" deletable="false" x="0" y="676">
    <statement name="BEFOREPURCHASE_STACK">
      <block type="text_join" id=".-;(5.uTk2P]P91@?c}7">
        <field name="VARIABLE" id="_.:Q.F!2e1yKQ9hOfkpT">text</field>
        <statement name="STACK">
          <block type="text_statement" id="V/)/MaH|fYmkSe[5)e2l">
            <value name="TEXT">
              <shadow type="text" id=";KmJz]E(6T#GDDGnT~@C">
                <field name="TEXT"></field>
              </shadow>
              <block type="text" id="ldFteB;1/{z-f!$!ng0%">
                <field name="TEXT">Last Digit = </field>
              </block>
            </value>
            <next>
              <block type="text_statement" id="y,)hEukCYfA(yLPEDmGK">
                <value name="TEXT">
                  <shadow type="text" id="ml`h((7P7Vj+Gu9#V{q[">
                    <field name="TEXT"></field>
                  </shadow>
                  <block type="last_digit" id="5jAl_GI96tlwngDk(cwM"></block>
                </value>
              </block>
            </next>
          </block>
        </statement>
        <next>
          <block type="notify" id="gb!K4O@WtxE8(p_F:X?z">
            <field name="NOTIFICATION_TYPE">success</field>
            <field name="NOTIFICATION_SOUND">silent</field>
            <value name="MESSAGE">
              <block type="variables_get" id="Hi.NsI*9C40V6|pe6~cs">
                <field name="VAR" id="_.:Q.F!2e1yKQ9hOfkpT">text</field>
              </block>
            </value>
            <next>
              <block type="controls_if" id="3_CIag{]g}0}+k`TtyW.">
                <value name="IF0">
                  <block type="logic_operation" id="V(/IJ1k3l!uJSFU?p!}|">
                    <field name="OP">AND</field>
                    <value name="A">
                      <block type="logic_compare" id="q($v*C%XOot*I?.hyGWF">
                        <field name="OP">EQ</field>
                        <value name="A">
                          <block type="variables_get" id=")TWmvlPcH,i{vjwhwH/-">
                            <field name="VAR" id="~J7_KCa,mrD@a`wFHCJr">LDP</field>
                          </block>
                        </value>
                        <value name="B">
                          <block type="math_number" id=".L(g=ML=$6-.OhVm15v/">
                            <field name="NUM">1</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <value name="B">
                      <block type="logic_operation" id=",+i6}P*E_Bcp]TZKgIR7">
                        <field name="OP">OR</field>
                        <value name="A">
                          <block type="logic_compare" id="Ru.(nnn(?s9=[`OTG_NZ">
                            <field name="OP">EQ</field>
                            <value name="A">
                              <block type="last_digit" id="1cT$9e=g{eZ+Wvsg:l9Y"></block>
                            </value>
                            <value name="B">
                              <block type="math_number" id="vQh!|+:2Xe{,V0jT;}po">
                                <field name="NUM">5</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <value name="B">
                          <block type="logic_compare" id="cCL.P,WPUkkAvx@zTWnw">
                            <field name="OP">EQ</field>
                            <value name="A">
                              <block type="last_digit" id="):[IY:up][H`rGNT.qv:"></block>
                            </value>
                            <value name="B">
                              <block type="math_number" id=",^blzR;]#QX4Rgv^93M:">
                                <field name="NUM">6</field>
                              </block>
                            </value>
                          </block>
                        </value>
                      </block>
                    </value>
                  </block>
                </value>
                <statement name="DO0">
                  <block type="apollo_purchase" id="qWum^3a$B1vdrVxW$:6?">
                    <field name="PURCHASE_LIST">DIGITOVER</field>
                    <field name="MULTIPLE_CONTRACTS">FALSE</field>
                    <field name="CONTRACT_QUANTITY">1</field>
                  </block>
                </statement>
                <next>
                  <block type="controls_if" id="%#9DDZFCaJ4M;SBE^x8u">
                    <value name="IF0">
                      <block type="logic_operation" id=")B}?,YRN6~!=JO]99FQc">
                        <field name="OP">AND</field>
                        <value name="A">
                          <block type="logic_compare" id="8bI!GWpK*IkJPc]jAKM3">
                            <field name="OP">EQ</field>
                            <value name="A">
                              <block type="variables_get" id="nMWD,]|$s`jFz}HB1*tA">
                                <field name="VAR" id="~J7_KCa,mrD@a`wFHCJr">LDP</field>
                              </block>
                            </value>
                            <value name="B">
                              <block type="math_number" id="ba3]7xu/A.pw#06d|[na">
                                <field name="NUM">8</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <value name="B">
                          <block type="logic_operation" id="S]Z]RYU*i+caWFqGI%@T">
                            <field name="OP">OR</field>
                            <value name="A">
                              <block type="logic_compare" id="Wm;d8L?_B`%v)O.x,`K?">
                                <field name="OP">EQ</field>
                                <value name="A">
                                  <block type="last_digit" id="U|5n~oyWFA48DpXpp$V7"></block>
                                </value>
                                <value name="B">
                                  <block type="math_number" id="5g{XM?KM-rbRAA$QGcws">
                                    <field name="NUM">0</field>
                                  </block>
                                </value>
                              </block>
                            </value>
                            <value name="B">
                              <block type="logic_compare" id="k!UlIU@ZOsHWQDHbof2;">
                                <field name="OP">EQ</field>
                                <value name="A">
                                  <block type="last_digit" id="du2~K]2Pjz-11$g[Ni!R"></block>
                                </value>
                                <value name="B">
                                  <block type="math_number" id="5R]a/zln0#LL[}%oLPC?">
                                    <field name="NUM">9</field>
                                  </block>
                                </value>
                              </block>
                            </value>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="apollo_purchase" id="a#4!Oj`SG+Ih|%B]f{$S">
                        <field name="PURCHASE_LIST">DIGITUNDER</field>
                        <field name="MULTIPLE_CONTRACTS">FALSE</field>
                        <field name="CONTRACT_QUANTITY">1</field>
                      </block>
                    </statement>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
  </block>
</xml>