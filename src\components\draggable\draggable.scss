.draggable {
    user-select: none;
    transition: opacity 0.25s cubic-bezier(0.25, 0.1, 0.1, 0.25);
    color: var(--text-general);
    background: var(--general-main-2);
    border: 1px solid rgb(0 0 0 / 12%);
    border-radius: 8px;
    box-shadow: 0px 0px 8px 4px var(--shadow-menu);
    padding: 4px;

    .draggable-content {
        background: var(--general-main-1);
        &__header {
            cursor: move;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            width: 100%;
            &__title {
                font-size: 16px;
                font-weight: 700;
            }
            &__close {
                cursor: pointer;
            }
            &-title {
                width: 100%;
            }
        }
        &__body {
            padding: 2px;
            display: contents;
        }
    }

    .resizable-handle {
        &__top {
            position: absolute;
            width: 100%;
            height: 10px;
            top: -5px;
            left: 0;
            cursor: row-resize;
        }
        &__right {
            position: absolute;
            width: 10px;
            height: 100%;
            top: 0;
            right: -5px;
            cursor: col-resize;
        }
        &__bottom {
            position: absolute;
            width: 100%;
            height: 10px;
            bottom: -5px;
            left: 0;
            cursor: row-resize;
        }
        &__left {
            position: absolute;
            width: 10px;
            height: 100%;
            top: 0;
            left: -5px;
            cursor: col-resize;
        }
        &__top-left {
            position: absolute;
            width: 10px;
            height: 10px;
            top: -5px;
            left: -5px;
            cursor: nwse-resize;
        }
        &__top-right {
            position: absolute;
            width: 10px;
            height: 10px;
            top: -5px;
            right: -5px;
            cursor: nesw-resize;
        }
        &__bottom-right {
            position: absolute;
            width: 10px;
            height: 10px;
            bottom: -5px;
            right: -5px;
            cursor: nwse-resize;
        }
        &__bottom-left {
            position: absolute;
            width: 10px;
            height: 10px;
            bottom: -5px;
            left: -5px;
            cursor: nesw-resize;
        }
    }
}

.draggable.dragging {
    cursor: no-drop;
}
