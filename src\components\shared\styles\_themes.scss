@use './constants';

:host,
:root {
    // Text sizes
    --text-size-xxxxs: 0.8rem;
    --text-size-xxxs: 1rem;
    --text-size-xxs: 1.2rem;
    --text-size-xs: 1.4rem;
    --text-size-s: 1.6rem;
    --text-size-xsm: 1.8rem;
    --text-size-sm: 2rem;
    --text-size-m: 2.4rem;
    --text-size-l: 3.2rem;
    --text-size-xl: 4.8rem;
    --text-size-xxl: 6.4rem;

    // Line Height
    --text-lh-xxs: 1;
    --text-lh-xs: 1.25;
    --text-lh-s: 1.4;
    --text-lh-m: 1.5;
    --text-lh-l: 1.75;
    --text-lh-xl: 2;
    --text-lh-xxl: 2.4;

    // Font Weight
    --text-weight-lighter: lighter;
    --text-weight-normal: normal;
    --text-weight-bold: bold;
    --text-weight-bolder: bolder;

    // Text Align
    --text-align-left: start;
    --text-align-right: right;
    --text-align-center: center;

    // Brand primary colors
    --brand-white: #{constants.$color-white};
    --brand-dark-grey: #{constants.$color-black};
    --brand-red-coral: #{constants.$color-red};
    --brand-orange: #2323ff;

    // Brand secondary colors
    --brand-secondary: #{constants.$color-green};

    // App Cards gradient background
    --app-card-virtual: #{constants.$gradient-virtual};
    --app-card-virtual-swap-free: #{constants.$gradient-virtual-swap-free};

    // Opacity
    --tw-border-opacity: 1;

    .theme--light {
        // General
        --general-main-1: #{constants.$color-white};
        --general-main-2: #{constants.$color-white};
        --general-main-3: #{constants.$color-grey-1};
        --general-main-4: #{constants.$alpha-color-white-4};
        --general-section-1: #{constants.$color-grey-2};
        --general-section-2: #{constants.$color-grey-2};
        --general-section-3: #{constants.$color-grey-11};
        --general-section-4: #{constants.$color-grey-12};
        --general-section-5: #{constants.$color-grey-2};
        --general-section-6: #{constants.$color-grey-2};
        --general-disabled: #{constants.$color-grey-3};
        --general-hover: #{constants.$color-grey-4};
        --general-active: #{constants.$color-grey-5};

        // Icons and Texts
        --text-general: #{constants.$color-black-1};
        --text-primary: #{constants.$color-grey-1};
        --text-less-prominent: #{constants.$color-grey-1};
        --text-prominent: #{constants.$color-black-1};
        --text-disabled: #{constants.$color-grey-1};
        --text-disabled-1: #{constants.$color-grey-6};
        --text-loss-danger: #{constants.$color-red-1};
        --text-profit-success: #{constants.$color-green-1};
        --text-warning: #{constants.$color-yellow};
        --text-red: #{constants.$color-red};
        --text-green: #{constants.$color-green-6};
        --text-blue: #{constants.$color-blue-3};
        --text-info-blue: #{constants.$color-blue};
        --text-info-blue-background: #{constants.$color-blue-5};
        --text-colored-background: #{constants.$color-white};
        --icon-light-background: #{constants.$color-black-9};
        --icon-dark-background: #{constants.$color-white};
        --icon-grey-background: #{constants.$color-grey-2};
        --icon-black-plus: #{constants.$color-black-7};
        --text-status-info-blue: #{constants.$color-blue};
        --text-hint: #{constants.$color-black-1};

        // Purchase
        --purchase-main-1: #{constants.$color-green-1};
        --purchase-section-1: #{constants.$color-green-2};
        --purchase-main-2: #{constants.$color-red-1};
        --purchase-section-2: #{constants.$color-red-4};
        --purchase-disabled-main: #{constants.$color-grey-3};
        --purchase-disabled-section: #{constants.$color-grey-4};

        // Buttons
        --button-primary-default: #2323ff;
        --button-secondary-default: #{constants.$color-grey-1};
        --button-tertiary-default: transparent;
        --button-primary-light-default: #{constants.$alpha-color-red-2};
        --button-primary-hover: #1f1fe6;
        --button-secondary-hover: #{constants.$alpha-color-black-3};
        --button-tertiary-hover: #{constants.$alpha-color-red-1};
        --button-primary-light-hover: #{constants.$alpha-color-red-3};
        --button-toggle-primary: #{constants.$color-blue-3};
        --button-toggle-secondary: #{constants.$color-grey-5};
        --button-toggle-alternate: #{constants.$color-white};

        // Overlay
        --overlay-outside-dialog: #{constants.$alpha-color-black-1};
        --overlay-inside-dialog: #{constants.$alpha-color-white-1};

        // Shadow
        --shadow-menu: #{constants.$alpha-color-black-4};
        --shadow-menu-2: #{constants.$alpha-color-black-4};
        --shadow-drop: #{constants.$alpha-color-black-3};
        --shadow-box: #{constants.$COLOR_LIGHT_BLACK_2};

        // States
        --state-normal: #{constants.$color-white};
        --state-hover: #{constants.$color-grey-4};
        --state-active: #{constants.$color-grey-5};
        --state-disabled: #{constants.$color-grey-3};
        --checkbox-disabled-grey: #{constants.$color-grey-6};
        --sidebar-tab: #{constants.$color-grey-6};

        // Border
        --border-normal: #{constants.$color-grey-5};
        --border-normal-1: #{constants.$color-grey-5};
        --border-normal-2: #{constants.$color-grey-5};
        --border-normal-3: #{constants.$color-grey-6};
        --border-hover: #{constants.$color-grey-1};
        --border-hover-1: #{constants.$color-black-9};
        --border-active: var(--brand-secondary);
        --border-disabled: #{constants.$color-grey-3};
        --border-divider: #{constants.$color-grey-2};

        // Fill
        --fill-normal: #{constants.$color-white};
        --fill-normal-1: #{constants.$color-grey};
        --fill-hover: #{constants.$color-grey-1};
        --fill-active: var(--brand-secondary);
        --fill-disabled: #{constants.$color-grey-3};

        // Status
        --status-default: #{constants.$color-grey-3};
        --status-adjustment: #{constants.$color-grey-1};
        --status-danger: #{constants.$color-red-1};
        --status-success: #{constants.$color-green-1};
        --status-warning: #{constants.$color-yellow};
        --status-warning-transparent: #{constants.$alpha-color-yellow-1};
        --status-transfer: #{constants.$color-orange};
        --status-info: #{constants.$color-blue};
        --status-colored-background: #{constants.$color-white};
        --status-alert-background: #{constants.$color-yellow-3};

        // Dashboard
        --dashboard-swap-free: #{constants.$gradient-color-black-3};
        --dashboard-app: #{constants.$gradient-color-black-4};

        // Transparentize
        --transparent-success: #{constants.$alpha-color-green-1};
        --transparent-info: #{constants.$alpha-color-blue-1};
        --transparent-hint: #{constants.$alpha-color-blue-3};
        --transparent-danger: #{constants.$alpha-color-red-2};
        --transparent-correct-message: #{constants.$color-green-5};
        --transparent-fake-message: #{constants.$color-red-6};

        // Gradient
        --gradient-success: #{constants.$gradient-color-green-1};
        --gradient-danger: #{constants.$gradient-color-red-1};
        --contract-gradient-danger: #{constants.$contract-gradient-color-red-1};
        --gradient-right-edge: #{constants.$gradient-color-white};
        --gradient-blue: #{constants.$gradient-color-blue-5};
        --gradient-gold: #{constants.$gradient-color-gold};
        --gradient-green: #{constants.$gradient-color-green-4};

        // Badge
        --badge-white: #{constants.$color-white};
        --badge-blue: #{constants.$color-blue-4};
        --badge-violet: #{constants.$color-blue-2};
        --badge-green: #{constants.$color-green-3};

        // TradersHub Banner
        --traders-hub-banner-border-color: #{constants.$color-grey-4};

        // Demo view
        --demo-text-color-1: #{constants.$color-grey};
        --demo-text-color-2: #{constants.$color-white};

        // Header
        --header-background-mt5: #{constants.$color-blue-8};
        --header-background-others: #{constants.$color-green-7};
    }

    .theme--dark {
        // General
        --general-main-1: #{constants.$color-black};
        --general-main-2: #{constants.$color-black-3};
        --general-main-3: #{constants.$color-black-4};
        --general-main-4: #{constants.$alpha-color-black-7};
        --general-section-1: #{constants.$color-black-3};
        --general-section-2: #{constants.$color-black};
        --general-section-3: #{constants.$color-black-5};
        --general-section-4: #{constants.$color-black-5};
        --general-section-5: #{constants.$color-black-5};
        --general-section-6: #{constants.$color-grey-7};
        --general-disabled: #{constants.$color-black-4};
        --general-hover: #{constants.$color-black-5};
        --general-active: #{constants.$color-black-8};

        // Icons and Texts
        --text-prominent: #{constants.$color-white};
        --text-general: #{constants.$color-grey};
        --text-less-prominent: #{constants.$color-grey-7};
        --text-primary: #{constants.$color-grey-1};
        --text-disabled: #{constants.$color-black-6};
        --text-disabled-1: #{constants.$color-black-6};
        --text-profit-success: #{constants.$color-green-3};
        --text-loss-danger: #{constants.$color-red-2};
        --text-red: #{constants.$color-red};
        --text-colored-background: #{constants.$color-white};
        --text-info-blue: #{constants.$color-blue-6};
        --text-info-blue-background: #{constants.$color-blue-7};
        --text-status-info-blue: #{constants.$color-blue};
        --text-hint: #{constants.$color-grey};
        --icon-light-background: #{constants.$color-black-9};
        --icon-dark-background: #{constants.$color-white};
        --icon-grey-background: #{constants.$color-black-1};
        --icon-black-plus: #{constants.$color-white};

        // Purchase
        --purchase-main-1: #{constants.$color-green-3};
        --purchase-section-1: #{constants.$color-green-4};
        --purchase-main-2: #{constants.$color-red-2};
        --purchase-section-2: #{constants.$color-red-3};
        --purchase-disabled-main: #{constants.$color-black-4};
        --purchase-disabled-section: #{constants.$color-black};

        // Buttons
        --button-primary-default: #2323ff;
        --button-secondary-default: #{constants.$color-grey-7};
        --button-tertiary-default: transparent;
        --button-primary-light-default: #{constants.$alpha-color-red-2};
        --button-primary-hover: #1f1fe6;
        --button-secondary-hover: #{constants.$alpha-color-white-3};
        --button-tertiary-hover: #{constants.$alpha-color-red-1};
        --button-primary-light-hover: #{constants.$alpha-color-red-3};
        --button-toggle-primary: #{constants.$color-blue-3};
        --button-toggle-secondary: #{constants.$color-black-8};
        --button-toggle-alternate: #{constants.$color-black-8};
        --button-get-started-bg: #{constants.$color-white};

        // Overlay
        --overlay-outside-dialog: #{constants.$alpha-color-black-1};
        --overlay-inside-dialog: #{constants.$alpha-color-black-2};

        // Shadow
        --shadow-menu: #{constants.$alpha-color-black-5};
        --shadow-menu-2: #{constants.$alpha-color-black-1};
        --shadow-drop: #{constants.$alpha-color-black-6};
        --shadow-box: #{constants.$COLOR_DARK_GRAY_3};

        // States
        --state-normal: #{constants.$color-black};
        --state-hover: #{constants.$color-black-5};
        --state-active: #{constants.$color-black-8};
        --state-disabled: #{constants.$color-black-4};
        --checkbox-disabled-grey: #{constants.$color-grey-6};
        --sidebar-tab: #{constants.$color-grey-7};

        // Border
        --border-normal: #{constants.$color-black-8};
        --border-normal-1: #{constants.$color-grey-5};
        --border-normal-2: #{constants.$color-grey-1};
        --border-normal-3: #{constants.$color-grey-7};
        --border-hover: #{constants.$color-grey-7};
        --border-hover-1: #{constants.$color-black-9};
        --border-active: var(--brand-secondary);
        --border-disabled: #{constants.$color-black-4};
        --border-divider: #{constants.$color-grey-13};

        // Fill
        --fill-normal: #{constants.$color-black};
        --fill-normal-1: #{constants.$color-black-1};
        --fill-hover: #{constants.$color-grey-7};
        --fill-active: var(--brand-secondary);
        --fill-disabled: #{constants.$color-black-4};

        // Status
        --status-default: #{constants.$color-grey-3};
        --status-adjustment: #{constants.$color-grey-1};
        --status-danger: #{constants.$color-red-2};
        --status-warning: #{constants.$color-yellow};
        --status-warning-transparent: #{constants.$alpha-color-yellow-1};
        --status-success: #{constants.$color-green-3};
        --status-transfer: #{constants.$color-orange};
        --status-info: #{constants.$color-blue};
        --status-colored-background: #{constants.$color-white};
        --status-alert-background: #{constants.$color-yellow-3};

        // Transparentize
        --transparent-success: #{constants.$alpha-color-green-2};
        --transparent-info: #{constants.$alpha-color-blue-1};
        --transparent-hint: #{constants.$alpha-color-blue-1};
        --transparent-danger: #{constants.$alpha-color-red-2};
        --transparent-correct-message: #{constants.$color-green-5};
        --transparent-fake-message: #{constants.$color-red-6};

        // Gradient
        --gradient-success: #{constants.$gradient-color-green-2};
        --gradient-danger: #{constants.$gradient-color-red-2};
        --contract-gradient-danger: #{constants.$contract-gradient-color-red-2};
        --gradient-right-edge: #{constants.$gradient-color-black};
        --gradient-blue: #{constants.$gradient-color-blue-5};
        --gradient-gold: #{constants.$gradient-color-gold};
        --gradient-green: #{constants.$gradient-color-green-4};

        // Badge
        --badge-white: #{constants.$color-white};
        --badge-blue: #{constants.$color-blue-4};
        --badge-violet: #{constants.$color-blue-2};
        --badge-green: #{constants.$color-green-3};

        // TradersHub Banner
        --traders-hub-banner-border-color: #{constants.$color-black-5};

        // Demo view
        --demo-text-color-1: #{constants.$color-black-1};
        --demo-text-color-2: #{constants.$color-black-1};

        // Header
        --header-background-mt5: #{constants.$color-blue-8};
        --header-background-others: #{constants.$color-green-7};
    }
}
