<xml xmlns="https://developers.google.com/blockly/xml" is_dbot="true" collection="false">
  
  <variables>
    
    <variable id="W4$:ZQCmEz#8+=4ysv5Y">Loss</variable>
    
    <variable id="j}8O`Vs+RJljIwPu-_:_">Stake</variable>
    
    <variable id="@1^Qc:ZmUG!!g,c$=~aV">text</variable>
    
    <variable id="b{,~*3*`%/Pc=9p^,|$P">text1</variable>
    
    <variable id="$hzv!Z_T7z5rRrLA{S5_">text2</variable>
    
    <variable id="mXtFswo{p,|%W1:V-$+r">Target Profit</variable>
    
    <variable id="%L?;380E6Lr^3b.%}t5Q">stake 2</variable>
    
    <variable id="dhpl!.uWBt`]RbJ4~liH">text3</variable>
  
  </variables>
  
  <block type="trade_definition" id="1#[S[W}OC|2oF#?r]!Q:" deletable="false" x="0" y="110">
    
    <statement name="TRADE_OPTIONS">
      
      <block type="trade_definition_market" id="PpTYCikO-Vl3xW7EvY;9" deletable="false" movable="false">
        
        <field name="MARKET_LIST">synthetic_index</field>
        
        <field name="SUBMARKET_LIST">random_index</field>
        
        <field name="SYMBOL_LIST">R_100</field>
        
        <next>
          
          <block type="trade_definition_tradetype" id="_]^,~{o$]3uX4sZ2OB!7" deletable="false" movable="false">
            
            <field name="TRADETYPECAT_LIST">digits</field>
            
            <field name="TRADETYPE_LIST">overunder</field>
            
            <next>
              
              <block type="trade_definition_contracttype" id="ldtHR:/t$,8Y^%9yWwya" deletable="false" movable="false">
                
                <field name="TYPE_LIST">DIGITOVER</field>
                
                <next>
                  
                  <block type="trade_definition_candleinterval" id="f]S;-}9H6+#EPsfb+I4p" deletable="false" movable="false">
                    
                    <field name="CANDLEINTERVAL_LIST">60</field>
                    
                    <next>
                      
                      <block type="trade_definition_restartbuysell" id="@;Mot_]u|gES)y_r./tC" deletable="false" movable="false">
                        
                        <field name="TIME_MACHINE_ENABLED">FALSE</field>
                        
                        <next>
                          
                          <block type="trade_definition_restartonerror" id=";T,;FM_tw4C;RFNRw)s." deletable="false" movable="false">
                            
                            <field name="RESTARTONERROR">TRUE</field>
                          
                          </block>
                        
                        </next>
                      
                      </block>
                    
                    </next>
                  
                  </block>
                
                </next>
              
              </block>
            
            </next>
          
          </block>
        
        </next>
      
      </block>
    
    </statement>
    
    <statement name="INITIALIZATION">
      
      <block type="text_print" id="Pc7|fxqq^GS#~E![)0:q" disabled="true">
        
        <value name="TEXT">
          
          <shadow type="text" id="%S~G_]0/Vd|W4l~tRg)~">
            
            <field name="TEXT">Another Day Another Dollar 💵🥳All The Best📈📉</field>
          
          </shadow>
        
        </value>
        
        <next>
          
          <block type="variables_set" id="z18LL#EOuXV/ot~jOSwU">
            
            <field name="VAR" id="W4$:ZQCmEz#8+=4ysv5Y">Loss</field>
            
            <value name="VALUE">
              
              <block type="math_number" id="{TpcuVb,~K#zZ#}lS({J">
                
                <field name="NUM">1000</field>
              
              </block>
            
            </value>
            
            <next>
              
              <block type="variables_set" id="ijf@o:zVg0FT0y9nPa@Z">
                
                <field name="VAR" id="mXtFswo{p,|%W1:V-$+r">Target Profit</field>
                
                <value name="VALUE">
                  
                  <block type="math_number" id="W.5F|X1T}Ki@]^K3PM34">
                    
                    <field name="NUM">500</field>
                  
                  </block>
                
                </value>
                
                <next>
                  
                  <block type="variables_set" id="wi;LzooRcdm1?=@Ye^M#">
                    
                    <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">Stake</field>
                    
                    <value name="VALUE">
                      
                      <block type="math_number" id="cwQn8]iJ[{izD*!JTkSu">
                        
                        <field name="NUM">50</field>
                      
                      </block>
                    
                    </value>
                    
                    <next>
                      
                      <block type="variables_set" id="16+%3$HbUgg:saYjP={x">
                        
                        <field name="VAR" id="%L?;380E6Lr^3b.%}t5Q">stake 2</field>
                        
                        <value name="VALUE">
                          
                          <block type="math_number" id="u6AbX[*ix9:/IqF)oh3^">
                            
                            <field name="NUM">50</field>
                          
                          </block>
                        
                        </value>
                      
                      </block>
                    
                    </next>
                  
                  </block>
                
                </next>
              
              </block>
            
            </next>
          
          </block>
        
        </next>
      
      </block>
    
    </statement>
    
    <statement name="SUBMARKET">
      
      <block type="trade_definition_tradeoptions" id="dA;i2c-MQZh`.8w6fUe/">
        
        <mutation xmlns="http://www.w3.org/1999/xhtml" has_first_barrier="false" has_second_barrier="false" has_prediction="true"></mutation>
        
        <field name="DURATIONTYPE_LIST">t</field>
        
        <value name="DURATION">
          
          <block type="math_number" id="qU{[-V%m3ny@0kgleWfI">
            
            <field name="NUM">1</field>
          
          </block>
        
        </value>
        
        <value name="AMOUNT">
          
          <block type="variables_get" id="6)tVmK)kNQZEld*g:FEZ">
            
            <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">Stake</field>
          
          </block>
        
        </value>
        
        <value name="PREDICTION">
          
          <shadow type="math_number" id="K;JD:1=_S!Y]8zl`/pb.">
            
            <field name="NUM">5</field>
          
          </shadow>
        
        </value>
      
      </block>
    
    </statement>
  
  </block>
  
  <block type="during_purchase" id="WK;)4B%_3F6e;^`*1U/U" collapsed="true" x="714" y="110"></block>
  
  <block type="after_purchase" id=";N+NF2Y^)C.@z-}-]O]U" x="714" y="256">
    
    <statement name="AFTERPURCHASE_STACK">
      
      <block type="controls_if" id="QX}izG@:OQMRc%H9J)|,">
        
        <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
        
        <value name="IF0">
          
          <block type="contract_check_result" id="~|?GP~1]PS-W-(BaZ`y#">
            
            <field name="CHECK_RESULT">win</field>
          
          </block>
        
        </value>
        
        <statement name="DO0">
          
          <block type="text_join" id="cxa;1c)P7!nP#V39TeSl">
            
            <field name="VARIABLE" id="@1^Qc:ZmUG!!g,c$=~aV">text</field>
            
            <statement name="STACK">
              
              <block type="text_statement" id="WDJxL+;fE2o[$x_=uHAE">
                
                <value name="TEXT">
                  
                  <shadow type="text" id="B2l.r889*+o^nDz,GB3O">
                    
                    <field name="TEXT"></field>
                  
                  </shadow>
                  
                  <block type="text" id=":eOVX;TQ^qkW6Bt2}35N">
                    
                    <field name="TEXT">Good Job🤑 Won it💥🥳💥🎉✔️</field>
                  
                  </block>
                
                </value>
                
                <next>
                  
                  <block type="text_statement" id="aGZ5s+OVU5Ag(K%H{@c|">
                    
                    <value name="TEXT">
                      
                      <shadow type="text" id="@:jEjHr}|NQugAHW%*de">
                        
                        <field name="TEXT"></field>
                      
                      </shadow>
                      
                      <block type="read_details" id="02s-yT%-?rQ2mBpNUYoS">
                        
                        <field name="DETAIL_INDEX">4</field>
                      
                      </block>
                    
                    </value>
                  
                  </block>
                
                </next>
              
              </block>
            
            </statement>
            
            <next>
              
              <block type="notify" id="gT~?^F77@Q]bTT](}ld5">
                
                <field name="NOTIFICATION_TYPE">success</field>
                
                <field name="NOTIFICATION_SOUND">earned-money</field>
                
                <value name="MESSAGE">
                  
                  <block type="variables_get" id="u#+LitNgn3t|e`(OF)m8">
                    
                    <field name="VAR" id="@1^Qc:ZmUG!!g,c$=~aV">text</field>
                  
                  </block>
                
                </value>
                
                <next>
                  
                  <block type="variables_set" id="zHPO,uV(Y91Ai:y(TZbl">
                    
                    <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">Stake</field>
                    
                    <value name="VALUE">
                      
                      <block type="variables_get" id="Qdq[CCcf9hitG)C7!DPa">
                        
                        <field name="VAR" id="%L?;380E6Lr^3b.%}t5Q">stake 2</field>
                      
                      </block>
                    
                    </value>
                  
                  </block>
                
                </next>
              
              </block>
            
            </next>
          
          </block>
        
        </statement>
        
        <statement name="ELSE">
          
          <block type="text_join" id="/@|sJ@o1kY[.:l4Qim!2">
            
            <field name="VARIABLE" id="b{,~*3*`%/Pc=9p^,|$P">text1</field>
            
            <statement name="STACK">
              
              <block type="text_statement" id="O|:m|2pOokz/Zo{DFvaC">
                
                <value name="TEXT">
                  
                  <shadow type="text" id="]yExvue[r}N|e+!F7sXN">
                    
                    <field name="TEXT"></field>
                  
                  </shadow>
                  
                  <block type="text" id="{7!*zMmQB3xow[eg`1;E">
                    
                    <field name="TEXT">sorry we lost but we will win🙏</field>
                  
                  </block>
                
                </value>
                
                <next>
                  
                  <block type="text_statement" id="VdemyKnjcku?6g2q)o;0">
                    
                    <value name="TEXT">
                      
                      <shadow type="text" id="mAlm31JJ`.4Rg.]`[;hD">
                        
                        <field name="TEXT"></field>
                      
                      </shadow>
                      
                      <block type="math_single" id="[%Rp4+?.(YT),,o(rCVm">
                        
                        <field name="OP">ABS</field>
                        
                        <value name="NUM">
                          
                          <shadow type="math_number" id="s8(9Qdw+xyQ#*XrFhyA7">
                            
                            <field name="NUM">9</field>
                          
                          </shadow>
                          
                          <block type="read_details" id="rZPoo`Y%!efplaDJe.;T">
                            
                            <field name="DETAIL_INDEX">4</field>
                          
                          </block>
                        
                        </value>
                      
                      </block>
                    
                    </value>
                  
                  </block>
                
                </next>
              
              </block>
            
            </statement>
            
            <next>
              
              <block type="notify" id="tr;Z_1+c!rRqGCF)P,ec">
                
                <field name="NOTIFICATION_TYPE">warn</field>
                
                <field name="NOTIFICATION_SOUND">error</field>
                
                <value name="MESSAGE">
                  
                  <block type="variables_get" id="35**c]q@na$@E9k82?e(">
                    
                    <field name="VAR" id="b{,~*3*`%/Pc=9p^,|$P">text1</field>
                  
                  </block>
                
                </value>
                
                <next>
                  
                  <block type="math_change" id="cI81oI?|6Rlv@YIO)$T^">
                    
                    <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">Stake</field>
                    
                    <value name="DELTA">
                      
                      <shadow type="math_number" id="XT7A-nRjSN_XMybVKU.;">
                        
                        <field name="NUM">1</field>
                      
                      </shadow>
                      
                      <block type="math_arithmetic" id="an~TS~37*a$3kn@}0,Wv">
                        
                        <field name="OP">MULTIPLY</field>
                        
                        <value name="A">
                          
                          <shadow type="math_number" id="Vjj*b[urRaU$dP2V/sTu">
                            
                            <field name="NUM">1</field>
                          
                          </shadow>
                          
                          <block type="math_single" id="/+uX|hH$}_(VJO:@o:7.">
                            
                            <field name="OP">ABS</field>
                            
                            <value name="NUM">
                              
                              <shadow type="math_number" id="%.(2VeCPa5b]?4@1oT}L">
                                
                                <field name="NUM">9</field>
                              
                              </shadow>
                              
                              <block type="read_details" id="QT.S~CALNtcP^=83vN6B">
                                
                                <field name="DETAIL_INDEX">4</field>
                              
                              </block>
                            
                            </value>
                          
                          </block>
                        
                        </value>
                        
                        <value name="B">
                          
                          <shadow type="math_number" id="%9$F?hNp[(rslgyRQ4My">
                            
                            <field name="NUM">1</field>
                          
                          </shadow>
                          
                          <block type="math_number" id="6$.ZZo^D,xYo87EphTJ*">
                            
                            <field name="NUM">1</field>
                          
                          </block>
                        
                        </value>
                      
                      </block>
                    
                    </value>
                    
                    <next>
                      
                      <block type="controls_if" id="{AKD%4?DSJiCYZh}k3Gu">
                        
                        <value name="IF0">
                          
                          <block type="logic_compare" id="eL(1yq:c##Ul,d].lPc.">
                            
                            <field name="OP">GTE</field>
                            
                            <value name="A">
                              
                              <block type="math_single" id="(kh8:cv/x_6A/IaQg@lU">
                                
                                <field name="OP">ABS</field>
                                
                                <value name="NUM">
                                  
                                  <shadow type="math_number" id="wm-2T5L~}O[$0k.(n1i[">
                                    
                                    <field name="NUM">9</field>
                                  
                                  </shadow>
                                  
                                  <block type="read_details" id="^a9yD{/-56r=R2!qec76">
                                    
                                    <field name="DETAIL_INDEX">4</field>
                                  
                                  </block>
                                
                                </value>
                              
                              </block>
                            
                            </value>
                            
                            <value name="B">
                              
                              <block type="variables_get" id="|JZc!Xh{!|vEFgn[:Vi=">
                                
                                <field name="VAR" id="W4$:ZQCmEz#8+=4ysv5Y">Loss</field>
                              
                              </block>
                            
                            </value>
                          
                          </block>
                        
                        </value>
                        
                        <statement name="DO0">
                          
                          <block type="variables_set" id="YGQL7nDsy{fSD/K2dn*H">
                            
                            <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">Stake</field>
                            
                            <value name="VALUE">
                              
                              <block type="variables_get" id="l:KzQPQF]UK~zqAplRhn">
                                
                                <field name="VAR" id="%L?;380E6Lr^3b.%}t5Q">stake 2</field>
                              
                              </block>
                            
                            </value>
                          
                          </block>
                        
                        </statement>
                      
                      </block>
                    
                    </next>
                  
                  </block>
                
                </next>
              
              </block>
            
            </next>
          
          </block>
        
        </statement>
        
        <next>
          
          <block type="text_join" id="X.~qn#+|?/3Zjt[XD5EV">
            
            <field name="VARIABLE" id="$hzv!Z_T7z5rRrLA{S5_">text2</field>
            
            <statement name="STACK">
              
              <block type="text_statement" id="NXZSZ~NJir^r|k?Lz2s5">
                
                <value name="TEXT">
                  
                  <shadow type="text" id="g8;~:CD6akaMg$^bkep4">
                    
                    <field name="TEXT"></field>
                  
                  </shadow>
                  
                  <block type="text" id="9/O.T92/ES(uDTn^1kz$">
                    
                    <field name="TEXT">Total Profit: </field>
                  
                  </block>
                
                </value>
                
                <next>
                  
                  <block type="text_statement" id="G[uC#-GbY-,aiTZ(#t-p">
                    
                    <value name="TEXT">
                      
                      <shadow type="text" id="}hs:WU@.$Crhj%4`^wC]">
                        
                        <field name="TEXT"></field>
                      
                      </shadow>
                      
                      <block type="total_profit" id="A*4_}Zz=K?VbeQnl#%Kf"></block>
                    
                    </value>
                  
                  </block>
                
                </next>
              
              </block>
            
            </statement>
            
            <next>
              
              <block type="notify" id="hyxv0z|jtu7_19-2FV_z">
                
                <field name="NOTIFICATION_TYPE">info</field>
                
                <field name="NOTIFICATION_SOUND">silent</field>
                
                <value name="MESSAGE">
                  
                  <block type="variables_get" id="o2iLiB},6f@n!nv,k.?-">
                    
                    <field name="VAR" id="$hzv!Z_T7z5rRrLA{S5_">text2</field>
                  
                  </block>
                
                </value>
                
                <next>
                  
                  <block type="controls_if" id="vMERE88|zg2c=#/]$?xV">
                    
                    <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                    
                    <value name="IF0">
                      
                      <block type="logic_compare" id="4jp7,@;t-VaiB*A{O(9q">
                        
                        <field name="OP">LT</field>
                        
                        <value name="A">
                          
                          <block type="total_profit" id="r%cgq[P,hb6N5v|!=G[8"></block>
                        
                        </value>
                        
                        <value name="B">
                          
                          <block type="variables_get" id="ac^:@t9)Y=jl[3*-kNn1">
                            
                            <field name="VAR" id="mXtFswo{p,|%W1:V-$+r">Target Profit</field>
                          
                          </block>
                        
                        </value>
                      
                      </block>
                    
                    </value>
                    
                    <statement name="DO0">
                      
                      <block type="trade_again" id="oMzV#w4Ry*oj!34)iD]."></block>
                    
                    </statement>
                    
                    <statement name="ELSE">
                      
                      <block type="text_join" id="~o{YMg~H?$M}!W[vI%y}">
                        
                        <field name="VARIABLE" id="dhpl!.uWBt`]RbJ4~liH">text3</field>
                        
                        <statement name="STACK">
                          
                          <block type="text_statement" id="YPV,lNcN%Vap|rZ)@/N2">
                            
                            <value name="TEXT">
                              
                              <shadow type="text" id="A0}a/?5#c,_C*;n^.vBh">
                                
                                <field name="TEXT"></field>
                              
                              </shadow>
                              
                              <block type="text" id="2/zpBXMK9%3EU%zRS;De">
                                
                                <field name="TEXT">Dollar Printer strategy worked ! 🎉🎉💥Congratulations 🥳</field>
                              
                              </block>
                            
                            </value>
                            
                            <next>
                              
                              <block type="text_statement" id="^SsViN3H:V01#3mtJ4uk">
                                
                                <value name="TEXT">
                                  
                                  <shadow type="text" id="=jqugB8Z#s8/lcxTlnfW">
                                    
                                    <field name="TEXT"></field>
                                  
                                  </shadow>
                                  
                                  <block type="total_profit" id="+uL(kE?0jtR;RQ;c4{cf"></block>
                                
                                </value>
                              
                              </block>
                            
                            </next>
                          
                          </block>
                        
                        </statement>
                        
                        <next>
                          
                          <block type="text_print" id="(,C,R)s#t_?6r5t]Ge?4">
                            
                            <value name="TEXT">
                              
                              <shadow type="text" id="1|*c)7ZA{i]HNG(rm@-3">
                                
                                <field name="TEXT">abc</field>
                              
                              </shadow>
                              
                              <block type="variables_get" id="MUjP0tO@K*^.VdFCupAR">
                                
                                <field name="VAR" id="dhpl!.uWBt`]RbJ4~liH">text3</field>
                              
                              </block>
                            
                            </value>
                          
                          </block>
                        
                        </next>
                      
                      </block>
                    
                    </statement>
                  
                  </block>
                
                </next>
              
              </block>
            
            </next>
          
          </block>
        
        </next>
      
      </block>
    
    </statement>
  
  </block>
  
  <block type="before_purchase" id="%qFO+D9Q]cuaBo{alwAM" collapsed="true" deletable="false" x="0" y="980">
    
    <statement name="BEFOREPURCHASE_STACK">
      
      <block type="purchase" id="ntmp=s5BHO$*yKqzk8y`">
        
        <field name="PURCHASE_LIST">DIGITOVER</field>
      
      </block>
    
    </statement>
  
  </block>
  
  <block type="math_number" id=",jHRAJUBxLfGJ1lWi7*4" disabled="true" x="0" y="1874">
    
    <field name="NUM">5</field>
  
  </block>
  
  <block type="text" id="ALO@+9VQ~b]-Hi_.KBB3" collapsed="true" disabled="true" x="0" y="1962">
    
    <field name="TEXT">Expert  Speed Bot</field>
  
  </block>

</xml>