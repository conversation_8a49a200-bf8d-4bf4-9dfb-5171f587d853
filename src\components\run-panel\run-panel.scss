@use 'components/shared/styles/mixins' as *;

/**
* @define -panel
**/
.run-panel {
    height: 0;

    &__container {
        height: var(--bot-content-height) !important;
        top: 10.4rem !important;
        width: 36.6rem !important;
        right: 0;

        &--mobile {
            position: absolute;
            top: 0;
            left: 0;
            width: 100vw;
            z-index: var(--zindex-drawer);
            height: calc(100% - env(safe-area-inset-bottom, 0));
            padding-bottom: env(safe-area-inset-bottom, 0);

            &-closed {
                position: unset;
            }
        }

        /* Fix for iOS devices to prevent run panel overlap */
        // @supports (-webkit-touch-callout: none) {
        //     &--mobile {
        //         //height: calc(100% - env(safe-area-inset-bottom, 0));
        //         //padding-bottom: env(safe-area-inset-bottom, 0);
        //     }
        // }
    }

    &__tile {
        @include flex-center;

        flex-direction: column;
        height: 100%;

        &-title {
            min-height: 1.8rem;
            margin-bottom: 4px;

            @include typeface(--small-center-bold-black, none);
        }

        &-content {
            height: 18px;
            margin-bottom: 4px;

            @include typeface(--small-center-normal-black, none);
        }
    }

    &__stat {
        @include flex-center(flex-start, flex-end);

        flex-direction: column;
        width: 35rem;
        background-color: var(--general-section-2);

        @include mobile-or-tablet-screen {
            margin: 0;
            position: fixed;

            /* Fix for iOS devices to prevent stats panel overlap */
            @supports (-webkit-touch-callout: none) {
                padding-bottom: env(safe-area-inset-bottom, 0);
            }
        }

        &--info {
            @include flex-center(center, flex-start);

            width: 33%;
            padding: 16px 0 2px;
            cursor: pointer;
            color: var(--text-general);
            -webkit-tap-highlight-color: transparent;

            &-item {
                display: inline-block;
                border-bottom: 1px dotted var(--text-general);
            }
        }

        &--tiles {
            width: 100%;
            display: grid;
            grid-template: 64px 64px / 1fr 1fr 1fr;
        }

        &-amount {
            @include typeface(--small-center-normal-black, none);

            &--positive {
                font-weight: bold;
                color: var(--text-profit-success);
            }

            &--negative {
                font-weight: bold;
                color: var(--text-loss-danger);
            }
        }

        &--mobile {
            width: 100%;
            bottom: 0;
        }
    }

    &__footer {
        text-align: center;

        @include flex-center(space-between);

        flex-direction: column;

        &-button {
            width: 318px;
            height: 32px !important;
            margin: 12px 24px;
        }
    }

    &__content {
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        overflow: hidden !important;

        .dc-tabs {
            &__item {
                overflow: hidden;
                text-overflow: ellipsis;
                padding: 0 0.8rem;
            }
        }
    }

    &__buttons {
        display: inline-flex !important;
        justify-content: space-between;
        width: 67%;
        margin-top: 1rem;
        align-items: center;
    }

    &-tab {
        &__content {
            height: calc(100vh - 42rem);
            overflow: hidden;

            &--no-stat {
                height: var(--drawer-content-height-no-stat);
            }

            &--mobile {
                display: flex;
                height: var(--drawer-content-height-mobile);
                position: fixed;
                bottom: 15.7rem;
                width: 100vw;
                padding: 0.4rem 0;

                /* Fix for iOS devices to prevent tab content overlap with bottom controls */
                @supports (-webkit-touch-callout: none) {
                    bottom: calc(15.7rem + env(safe-area-inset-bottom, 0));
                }
            }

            &--summary-tab {
                padding: 0.8rem 1.6rem;
            }
        }
    }

    &__clear-button {
        @include mobile-or-tablet-screen {
            position: absolute !important;
            top: 0.5rem;
            right: 1.6rem;
            height: 2.6rem !important;
            min-width: 8rem;
        }
    }
}

.controls {
    &__section {
        @include flex-center(space-between);

        flex-direction: column;
        position: fixed;
        bottom: 0;
        width: 100%;
        background-color: var(--general-main-1);
        border-top: solid 2px var(--general-section-1);
        z-index: 8;

        /* Fix for iOS devices to respect safe area */
        @supports (-webkit-touch-callout: none) {
            bottom: env(safe-area-inset-bottom, 0);
            padding-bottom: env(safe-area-inset-bottom, 0);
        }

        /* Additional class for iOS detection via JS */
        &.ios-device {
            bottom: env(safe-area-inset-bottom, 0);
            padding-bottom: env(safe-area-inset-bottom, 0);
        }
    }

    &__buttons {
        padding: 0.8rem 2.4rem;
        height: 6rem;
        display: flex;
        width: inherit;

        @include tablet-screen {
            max-width: 60rem;
            padding: 0.8rem 0;
        }

        /* Fix for iOS devices to ensure buttons are properly sized */
        @supports (-webkit-touch-callout: none) {
            height: auto;
            min-height: 6rem;
        }
    }

    &__stop-button,
    &__run-button {
        width: 40%;
        border-radius: 4px 0 0 4px !important;
    }

    &__animation {
        width: 100%;
        height: 4rem;
        border-radius: 0 4px 4px 0;
    }
}

// animation
.list {
    &__animation {
        &-enter {
            height: 0;
            transform: translateX(200%);

            &-active {
                height: auto;
                transform: translateX(0%);
                transition:
                    height 500ms,
                    transform 500ms;
            }
        }

        &-exit {
            opacity: 1;

            &-active {
                opacity: 0;
                transition: opacity 300ms;
            }
        }
    }
}

.db-self-exclusion {
    font-size: var(--text-size-xs);
    font-weight: normal;
    line-height: 1.43;
    color: var(--text-general);

    &__content {
        margin: 2.4rem;
        margin-right: 1.4rem;
        padding-right: 1rem;
    }

    & .dc-themed-scrollbars__track--vertical {
        right: -0.2rem;
    }

    &__dialog {
        max-height: 500px !important;
        width: 460px;
    }

    &__footer {
        height: 72px;
        display: block;
        position: fixed;
        width: 100%;
        left: 0;
        bottom: 0;
        padding: 1.4rem;
        border-top: 2px solid var(--general-section-2);

        /* Fix for iOS devices to respect safe area */
        @supports (-webkit-touch-callout: none) {
            bottom: env(safe-area-inset-bottom, 0);
            padding-bottom: calc(1.4rem + env(safe-area-inset-bottom, 0));
        }

        @include mobile-or-tablet-screen {
            position: absolute;
            width: 100%;
            bottom: 40px;
            left: 0;

            /* Fix for iOS devices on mobile */
            @supports (-webkit-touch-callout: none) {
                bottom: calc(40px + env(safe-area-inset-bottom, 0));
            }

            &--relative {
                position: relative;
                margin-top: 5rem;
            }
        }

        &-btn-group {
            display: flex;
            justify-content: flex-end;
        }
    }

    &__info {
        margin-bottom: 2rem;
    }

    &__limit-status {
        margin: 1rem 0;

        &--bold {
            font-weight: 700;
        }

        &--danger {
            font-weight: 700;
            color: var(--status-danger);
        }
    }

    &--danger {
        color: var(--status-danger);
    }

    & .dc-btn {
        margin-left: 0.8rem;
    }
}

.limits__wrapper {
    @include mobile-or-tablet-screen {
        position: fixed;
        z-index: 5;
        width: 100%;
        left: 0;
        top: 0;
        background: var(--general-main-1);

        .db-self-exclusion {
            height: calc(100vh - 40px);
            display: flex;
            justify-content: space-between;
            flex-direction: column;

            &__content {
                margin-top: 56px;
            }

            &__form-group {
                margin-bottom: 1.6rem;
                display: flex;
            }
        }
    }
}

.dc-modal__container_self-exclusion__modal {
    display: flex;
    flex-direction: row;
    align-content: space-between;
    width: 440px !important;
    height: 374px !important;
}

.statistics {
    &__modal {
        height: 28.4rem !important;
        width: 44rem !important;
        font-size: 1.6rem;
        padding: 2.4rem;

        &--mobile {
            font-size: 1.6rem !important;
        }

        &-body {
            height: calc(100vh - 40.6rem);
            min-height: 10rem;
            max-height: 45rem;

            &--mobile {
                padding: 0.6rem 0 1.6rem !important;
                height: 40.4rem;
            }

            &--content {
                margin-top: 1rem;

                &-stake {
                    margin-top: unset;
                    font-weight: bold;
                }
            }
        }

        &-scrollbar {
            padding-right: 1.2rem;
        }
    }
}

.dc-modal__container_statistics__modal {
    @include mobile-or-tablet-screen {
        width: 31.2rem !important;
    }

    .dc-modal-body {
        padding: 2.4rem 1.2rem 2.4rem 2.4rem;
    }
}

.dc-dialog {
    &__button {
        @include mobile-or-tablet-screen {
            flex-basis: 100%;
        }
    }
}
