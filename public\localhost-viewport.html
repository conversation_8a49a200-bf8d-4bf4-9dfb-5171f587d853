<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>PromoClub - Localhost Development Viewport</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
            }

            .container {
                background: white;
                border-radius: 20px;
                padding: 40px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                max-width: 800px;
                width: 100%;
            }

            .header {
                text-align: center;
                margin-bottom: 40px;
            }

            .logo {
                font-size: 2.5rem;
                font-weight: bold;
                color: #667eea;
                margin-bottom: 10px;
            }

            .subtitle {
                color: #666;
                font-size: 1.1rem;
            }

            .status {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 10px;
                margin: 20px 0;
                padding: 15px;
                background: #f0f9ff;
                border-radius: 10px;
                border-left: 4px solid #0ea5e9;
            }

            .status.online {
                background: #f0fdf4;
                border-left-color: #22c55e;
            }

            .status-dot {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                background: #22c55e;
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0% {
                    opacity: 1;
                }
                50% {
                    opacity: 0.5;
                }
                100% {
                    opacity: 1;
                }
            }

            .links {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin: 30px 0;
            }

            .link-card {
                background: #f8fafc;
                border: 2px solid #e2e8f0;
                border-radius: 12px;
                padding: 20px;
                text-decoration: none;
                color: inherit;
                transition: all 0.3s ease;
                display: block;
            }

            .link-card:hover {
                border-color: #667eea;
                transform: translateY(-2px);
                box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            }

            .link-title {
                font-weight: bold;
                color: #1e293b;
                margin-bottom: 8px;
                font-size: 1.1rem;
            }

            .link-desc {
                color: #64748b;
                font-size: 0.9rem;
                line-height: 1.4;
            }

            .link-url {
                color: #667eea;
                font-family: monospace;
                font-size: 0.85rem;
                margin-top: 8px;
                word-break: break-all;
            }

            .info-section {
                margin-top: 30px;
                padding: 20px;
                background: #fafafa;
                border-radius: 10px;
            }

            .info-title {
                font-weight: bold;
                color: #1e293b;
                margin-bottom: 15px;
            }

            .info-list {
                list-style: none;
            }

            .info-list li {
                padding: 5px 0;
                color: #64748b;
            }

            .info-list li::before {
                content: '→';
                color: #667eea;
                margin-right: 10px;
            }

            .domain-test {
                margin-top: 20px;
            }

            .domain-select {
                width: 100%;
                padding: 10px;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                font-size: 1rem;
                margin-bottom: 10px;
            }

            .test-button {
                background: #667eea;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-size: 1rem;
                cursor: pointer;
                transition: background 0.3s ease;
            }

            .test-button:hover {
                background: #5a67d8;
            }

            @media (max-width: 768px) {
                .container {
                    padding: 20px;
                }

                .links {
                    grid-template-columns: 1fr;
                }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">🚀 PromoClub</div>
                <div class="subtitle">Localhost Development Viewport</div>
            </div>

            <div class="status online">
                <div class="status-dot"></div>
                <span>Development server is running</span>
            </div>

            <div class="links">
                <a href="http://localhost:3000" class="link-card" target="_blank">
                    <div class="link-title">🏠 Main Application</div>
                    <div class="link-desc">Primary development server with hot reload</div>
                    <div class="link-url">http://localhost:3000</div>
                </a>

                <a href="http://localhost:8443" class="link-card" target="_blank">
                    <div class="link-title">📦 Static Build</div>
                    <div class="link-desc">Serve built files (run npm run build first)</div>
                    <div class="link-url">http://localhost:8443</div>
                </a>

                <a href="#" class="link-card" id="networkLink">
                    <div class="link-title">🌐 Network Access</div>
                    <div class="link-desc">Access from other devices on your network</div>
                    <div class="link-url" id="networkUrl">Detecting network IP...</div>
                </a>

                <a href="http://localhost:3000?bots_domain=promoclub.site" class="link-card" target="_blank">
                    <div class="link-title">🎯 Domain Testing</div>
                    <div class="link-desc">Test with PromoClub domain configuration</div>
                    <div class="link-url">http://localhost:3000?bots_domain=promoclub.site</div>
                </a>
                <a href="http://localhost:3000?bots_domain=swiftpro.site" class="link-card" target="_blank">
                    <div class="link-title">⚡ Swift Pro Testing</div>
                    <div class="link-desc">Test with SwiftPro domain configuration</div>
                    <div class="link-url">http://localhost:3000?bots_domain=swiftpro.site</div>
                </a>
            </div>

            <div class="info-section">
                <div class="info-title">📋 Quick Commands</div>
                <ul class="info-list">
                    <li><code>npm start</code> - Start development server</li>
                    <li><code>npm run dev</code> - Enhanced development server with utilities</li>
                    <li><code>npm run build</code> - Build for production</li>
                    <li><code>npm run serve</code> - Serve built files</li>
                    <li><code>npm run localhost:info</code> - Show development info</li>
                </ul>
            </div>

            <div class="info-section domain-test">
                <div class="info-title">🔧 Domain Testing</div>
                <select class="domain-select" id="domainSelect">
                    <option value="">Select a domain to test...</option>
                    <option value="promoclub.site">promoclub.site</option>
                    <option value="tradermaster.site">tradermaster.site</option>
                    <option value="gletraders.site">gletraders.site</option>
                    <option value="legoo.site">legoo.site</option>
                    <option value="wallacetraders.site">wallacetraders.site</option>
                    <option value="kingstraders.site">kingstraders.site</option>
                    <option value="dbotprinters.site">dbotprinters.site</option>
                    <option value="kenyanhennessy.site">kenyanhennessy.site</option>
                    <option value="masterhunter.site">masterhunter.site</option>
                    <option value="swiftpro.site">swiftpro.site</option>
                </select>
                <button class="test-button" onclick="testDomain()">Test Domain Configuration</button>
            </div>
        </div>

        <script>
            // Detect network IP
            async function detectNetworkIP() {
                try {
                    // This is a simple way to get the local IP, though it may not work in all browsers
                    const response = await fetch('/api/network-info').catch(() => null);
                    if (response) {
                        const data = await response.json();
                        return data.ip;
                    }
                } catch (e) {
                    // Fallback: try to detect from WebRTC
                    return new Promise(resolve => {
                        const pc = new RTCPeerConnection({ iceServers: [] });
                        pc.createDataChannel('');
                        pc.createOffer().then(offer => pc.setLocalDescription(offer));
                        pc.onicecandidate = ice => {
                            if (ice && ice.candidate && ice.candidate.candidate) {
                                const ip = /([0-9]{1,3}(\.[0-9]{1,3}){3})/.exec(ice.candidate.candidate);
                                if (ip) {
                                    resolve(ip[1]);
                                    pc.close();
                                }
                            }
                        };
                        setTimeout(() => resolve('localhost'), 3000);
                    });
                }
            }

            // Update network link
            detectNetworkIP().then(ip => {
                const networkLink = document.getElementById('networkLink');
                const networkUrl = document.getElementById('networkUrl');
                const url = `http://${ip}:3000`;
                networkLink.href = url;
                networkUrl.textContent = url;
            });

            // Test domain function
            function testDomain() {
                const select = document.getElementById('domainSelect');
                const domain = select.value;
                if (domain) {
                    const url = `http://localhost:3000?bots_domain=${domain}`;
                    window.open(url, '_blank');
                } else {
                    alert('Please select a domain to test');
                }
            }

            // Check if development server is running
            async function checkServerStatus() {
                try {
                    const response = await fetch('http://localhost:3000', { mode: 'no-cors' });
                    // If we get here, server is likely running
                    document.querySelector('.status').classList.add('online');
                } catch (e) {
                    // Server might not be running
                    document.querySelector('.status').classList.remove('online');
                    document.querySelector('.status span').textContent = 'Development server not detected';
                }
            }

            // Check server status on load
            checkServerStatus();

            // Refresh status every 30 seconds
            setInterval(checkServerStatus, 30000);
        </script>
    </body>
</html>
