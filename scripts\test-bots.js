#!/usr/bin/env node

/**
 * Bot Testing Script
 * Tests all domain-specific bots for structural integrity and required blocks
 */

const fs = require('fs');
const path = require('path');

// Try to import xmldom, fallback gracefully if not available
let DOMParser;
try {
    DOMParser = require('xmldom').DOMParser;
} catch (e) {
    console.warn('xmldom not available, bot testing will be limited');
    DOMParser = null;
}

const ROOT = process.cwd();
const XML_DIR = path.join(ROOT, 'public', 'xml');

// Required blocks for a valid bot
const REQUIRED_BLOCKS = ['trade_definition', 'trade_definition_tradeoptions', 'before_purchase'];

// Purchase-like blocks (at least one required)
const PURCHASE_BLOCKS = ['purchase', 'apollo_purchase', 'apollo_purchase2', 'traderlegoo_purchase'];

// Domains to test
const DOMAINS = [
    'tradermaster.site',
    'promoclub.site',
    'gletraders.site',
    'd-analysis.site',
    'kingstraders.site',
    'wallacetraders.site',
    'legoo.site',
    'dbotprinters.site',
    'kenyanhennessy.site',
    'masterhunter.site',
];

function readJSON(p, fallback) {
    if (!fs.existsSync(p)) return fallback;
    try {
        return JSON.parse(fs.readFileSync(p, 'utf8'));
    } catch (e) {
        return fallback;
    }
}

function parseXML(xmlContent) {
    if (!DOMParser) {
        console.warn('XML parsing not available - xmldom package missing');
        return null;
    }
    try {
        const parser = new DOMParser();
        return parser.parseFromString(xmlContent, 'text/xml');
    } catch (e) {
        return null;
    }
}

function getBlockTypes(xmlDoc) {
    if (!xmlDoc) return [];
    const blocks = xmlDoc.getElementsByTagName('block');
    const types = [];
    for (let i = 0; i < blocks.length; i++) {
        const type = blocks[i].getAttribute('type');
        if (type) types.push(type);
    }
    return [...new Set(types)]; // Remove duplicates
}

function validateBot(xmlPath, botName) {
    const results = {
        name: botName,
        path: xmlPath,
        valid: false,
        errors: [],
        warnings: [],
        blockTypes: [],
    };

    try {
        if (!fs.existsSync(xmlPath)) {
            results.errors.push('File does not exist');
            return results;
        }

        const xmlContent = fs.readFileSync(xmlPath, 'utf8');
        const xmlDoc = parseXML(xmlContent);

        if (!xmlDoc) {
            results.errors.push('Invalid XML format');
            return results;
        }

        const blockTypes = getBlockTypes(xmlDoc);
        results.blockTypes = blockTypes;

        // Check for required blocks
        const missingRequired = REQUIRED_BLOCKS.filter(type => !blockTypes.includes(type));
        if (missingRequired.length > 0) {
            results.errors.push(`Missing required blocks: ${missingRequired.join(', ')}`);
        }

        // Check for at least one purchase block
        const hasPurchaseBlock = PURCHASE_BLOCKS.some(type => blockTypes.includes(type));
        if (!hasPurchaseBlock) {
            results.errors.push(`Missing purchase block. Expected one of: ${PURCHASE_BLOCKS.join(', ')}`);
        }

        // Check for common issues
        if (!blockTypes.includes('trade_definition_market')) {
            results.warnings.push('Missing trade_definition_market block');
        }
        if (!blockTypes.includes('trade_definition_tradetype')) {
            results.warnings.push('Missing trade_definition_tradetype block');
        }
        if (!blockTypes.includes('trade_definition_contracttype')) {
            results.warnings.push('Missing trade_definition_contracttype block');
        }

        // Bot is valid if no errors
        results.valid = results.errors.length === 0;
    } catch (error) {
        results.errors.push(`Error reading file: ${error.message}`);
    }

    return results;
}

function testDomain(domain) {
    console.log(`\n🔍 Testing domain: ${domain}`);
    console.log('─'.repeat(50));

    const domainDir = path.join(XML_DIR, domain);
    const manifestPath = path.join(domainDir, 'bots.json');

    if (!fs.existsSync(domainDir)) {
        console.log(`  ❌ Domain directory does not exist`);
        return { domain, tested: 0, valid: 0, invalid: 0, errors: [] };
    }

    const manifest = readJSON(manifestPath, []);
    if (manifest.length === 0) {
        console.log(`  ℹ️  No bots to test (empty manifest)`);
        return { domain, tested: 0, valid: 0, invalid: 0, errors: [] };
    }

    let tested = 0,
        valid = 0,
        invalid = 0;
    const errors = [];

    for (const bot of manifest) {
        const xmlPath = path.join(domainDir, bot.file);
        const result = validateBot(xmlPath, bot.name);
        tested++;

        if (result.valid) {
            valid++;
            console.log(`  ✅ ${bot.name}`);
        } else {
            invalid++;
            console.log(`  ❌ ${bot.name}`);
            result.errors.forEach(error => {
                console.log(`     • ${error}`);
                errors.push(`${bot.name}: ${error}`);
            });
        }

        if (result.warnings.length > 0) {
            result.warnings.forEach(warning => {
                console.log(`     ⚠️  ${warning}`);
            });
        }
    }

    console.log(`  📊 Summary: ${valid}/${tested} valid bots`);
    return { domain, tested, valid, invalid, errors };
}

function generateReport(results) {
    console.log('\n' + '='.repeat(60));
    console.log('📋 COMPREHENSIVE BOT TEST REPORT');
    console.log('='.repeat(60));

    let totalTested = 0,
        totalValid = 0,
        totalInvalid = 0;
    const allErrors = [];

    results.forEach(result => {
        totalTested += result.tested;
        totalValid += result.valid;
        totalInvalid += result.invalid;
        allErrors.push(...result.errors);
    });

    console.log(`\n📈 Overall Statistics:`);
    console.log(`  • Total bots tested: ${totalTested}`);
    console.log(`  • Valid bots: ${totalValid} (${((totalValid / totalTested) * 100).toFixed(1)}%)`);
    console.log(`  • Invalid bots: ${totalInvalid} (${((totalInvalid / totalTested) * 100).toFixed(1)}%)`);

    console.log(`\n🏆 Domain Performance:`);
    results.forEach(result => {
        if (result.tested > 0) {
            const percentage = ((result.valid / result.tested) * 100).toFixed(1);
            const status = result.valid === result.tested ? '✅' : result.valid > 0 ? '⚠️' : '❌';
            console.log(`  ${status} ${result.domain}: ${result.valid}/${result.tested} (${percentage}%)`);
        }
    });

    if (allErrors.length > 0) {
        console.log(`\n🚨 Common Issues Found:`);
        const errorCounts = {};
        allErrors.forEach(error => {
            const key = error.split(': ')[1] || error;
            errorCounts[key] = (errorCounts[key] || 0) + 1;
        });

        Object.entries(errorCounts)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .forEach(([error, count]) => {
                console.log(`  • ${error} (${count} bots)`);
            });
    }

    console.log(`\n✨ Recommendations:`);
    if (totalInvalid > 0) {
        console.log(`  • Fix ${totalInvalid} invalid bots by adding missing required blocks`);
        console.log(`  • Ensure all bots have: trade_definition, trade_definition_tradeoptions, before_purchase`);
        console.log(`  • Ensure all bots have at least one purchase block: ${PURCHASE_BLOCKS.join(', ')}`);
    } else {
        console.log(`  • All bots are structurally valid! 🎉`);
        console.log(`  • Consider running functional tests to verify bot behavior`);
    }

    console.log('\n' + '='.repeat(60));
}

function main() {
    console.log('🤖 Bot Testing Suite');
    console.log('Testing all domain-specific bots for structural integrity...\n');

    const results = [];

    for (const domain of DOMAINS) {
        const result = testDomain(domain);
        results.push(result);
    }

    generateReport(results);
}

if (require.main === module) {
    main();
}
