#!/usr/bin/env node

/**
 * Enhanced Development Server Script
 * Provides additional utilities for local development
 */

const { execSync, spawn } = require('child_process');
const os = require('os');
const path = require('path');

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    green: '\x1b[32m',
    blue: '\x1b[34m',
    yellow: '\x1b[33m',
    red: '\x1b[31m',
    cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function getLocalIP() {
    const interfaces = os.networkInterfaces();
    for (const name of Object.keys(interfaces)) {
        for (const interface of interfaces[name]) {
            if (interface.family === 'IPv4' && !interface.internal) {
                return interface.address;
            }
        }
    }
    return 'localhost';
}

function checkPort(port) {
    try {
        execSync(`netstat -an | findstr :${port}`, { stdio: 'pipe' });
        return true;
    } catch {
        return false;
    }
}

function killPort(port) {
    try {
        if (process.platform === 'win32') {
            execSync(`for /f "tokens=5" %a in ('netstat -aon ^| findstr :${port}') do taskkill /f /pid %a`, {
                stdio: 'pipe',
            });
        } else {
            execSync(`lsof -ti:${port} | xargs kill -9`, { stdio: 'pipe' });
        }
        log(`✅ Killed process on port ${port}`, 'green');
    } catch (error) {
        log(`❌ Could not kill process on port ${port}`, 'red');
    }
}

function displayInfo() {
    const localIP = getLocalIP();
    const port = process.env.PORT || 3000;

    log('\n🚀 PromoClub Development Server', 'bright');
    log('═══════════════════════════════════════', 'cyan');
    log(`📍 Local:    http://localhost:${port}`, 'green');
    log(`🌐 Network:  http://${localIP}:${port}`, 'blue');
    log('═══════════════════════════════════════', 'cyan');
    log('📱 Mobile Testing:', 'yellow');
    log(`   Use network URL on mobile devices on same WiFi`, 'yellow');
    log('\n🛠️  Development Features:', 'yellow');
    log('   • Hot reload enabled', 'yellow');
    log('   • Source maps enabled', 'yellow');
    log('   • Service worker caching', 'yellow');
    log('   • External connections allowed', 'yellow');
    log('\n💡 Tips:', 'cyan');
    log('   • Press Ctrl+C to stop the server', 'cyan');
    log('   • Use ?bots_domain=domain.site to test domain-specific features', 'cyan');
    log('   • Check localhost-setup.md for more details', 'cyan');
    log('═══════════════════════════════════════\n', 'cyan');
}

function startServer() {
    const port = process.env.PORT || 3000;

    // Check if port is in use
    if (checkPort(port)) {
        log(`⚠️  Port ${port} is already in use`, 'yellow');
        log(`🔧 Attempting to free port ${port}...`, 'yellow');
        killPort(port);

        // Wait a moment for the port to be freed
        setTimeout(() => {
            startActualServer();
        }, 2000);
    } else {
        startActualServer();
    }
}

function startActualServer() {
    displayInfo();

    // Start the development server
    const server = spawn('npm', ['start'], {
        stdio: 'inherit',
        shell: true,
        cwd: path.resolve(__dirname, '..'),
    });

    server.on('error', error => {
        log(`❌ Failed to start server: ${error.message}`, 'red');
    });

    server.on('close', code => {
        if (code !== 0) {
            log(`❌ Server exited with code ${code}`, 'red');
        } else {
            log('👋 Server stopped', 'yellow');
        }
    });

    // Handle graceful shutdown
    process.on('SIGINT', () => {
        log('\n🛑 Shutting down development server...', 'yellow');
        server.kill('SIGINT');
        process.exit(0);
    });
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
    log('🚀 PromoClub Development Server', 'bright');
    log('\nUsage:', 'cyan');
    log('  node scripts/dev-server.js [options]', 'green');
    log('\nOptions:', 'cyan');
    log('  --help, -h     Show this help message', 'yellow');
    log('  --port, -p     Specify port (default: 3000)', 'yellow');
    log('  --kill-port    Kill process on specified port', 'yellow');
    log('\nExamples:', 'cyan');
    log('  node scripts/dev-server.js', 'green');
    log('  node scripts/dev-server.js --port 3001', 'green');
    log('  node scripts/dev-server.js --kill-port 3000', 'green');
    process.exit(0);
}

if (args.includes('--kill-port')) {
    const portIndex = args.indexOf('--kill-port') + 1;
    const port = args[portIndex] || 3000;
    killPort(port);
    process.exit(0);
}

if (args.includes('--port') || args.includes('-p')) {
    const portIndex = Math.max(args.indexOf('--port'), args.indexOf('-p')) + 1;
    const port = args[portIndex];
    if (port && !isNaN(port)) {
        process.env.PORT = port;
    }
}

// Start the server
startServer();
