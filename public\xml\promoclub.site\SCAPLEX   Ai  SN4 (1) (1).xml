<xml xmlns="https://developers.google.com/blockly/xml" is_dbot="true" collection="false">
  <variables>
    <variable id="6o_9Nu#$3m#.?zV=ahjK">Prediction</variable>
    <variable id="RHgF*C%#h{|Ha5u;#5j?">Stake one</variable>
    <variable id="rUPB[)tUi3we;byHr`7]">value</variable>
    <variable id="N5h(uF)6G2cql)h{ZR;h">text</variable>
    <variable id=")-9T~6%gxe29l#C#)qts">text1</variable>
    <variable id="LN}r@t3f(x%mM[7-P|ft">text2</variable>
    <variable id="(obU$~2J]hr/k0`hgX8/">Stake two</variable>
    <variable id="4+UcGJ_mE@7^Z8-d@JaJ">VTY</variable>
    <variable id="kQ+Ke(]*eQpM[qgfd~+g">Contador</variable>
    <variable id="^3d~6DUQ!o8XQTn.XB!c">text3</variable>
    <variable id="FlX%77!9AMWGEu#,)Nme">Y</variable>
    <variable id="a?$LW}C4v+XX:Lc%6-OS">A</variable>
    <variable id="MgsL8H;~`;r/^Tuh1)y=">BBFTY</variable>
  </variables>
  <block type="trade_definition" id="|,wpfgCRK_O,JHE[$xte" deletable="false" x="0" y="50">
    <statement name="TRADE_OPTIONS">
      <block type="trade_definition_market" id="dqYC5Hzv(BU~|R.^0oBf" deletable="false" movable="false">
        <field name="MARKET_LIST">synthetic_index</field>
        <field name="SUBMARKET_LIST">random_index</field>
        <field name="SYMBOL_LIST">1HZ100V</field>
        <next>
          <block type="trade_definition_tradetype" id="R%UeAWK76$}y,1vTBL9J" deletable="false" movable="false">
            <field name="TRADETYPECAT_LIST">digits</field>
            <field name="TRADETYPE_LIST">overunder</field>
            <next>
              <block type="trade_definition_contracttype" id="JfVv3q5!6HG+a{Z;UcT~" deletable="false" movable="false">
                <field name="TYPE_LIST">DIGITUNDER</field>
                <next>
                  <block type="trade_definition_candleinterval" id="=.C+ze:~jIzo%`@vmpxG" deletable="false" movable="false">
                    <field name="CANDLEINTERVAL_LIST">60</field>
                    <next>
                      <block type="trade_definition_restartbuysell" id="-[*XT*?4Y.x*GF[i_*9?" deletable="false" movable="false">
                        <field name="TIME_MACHINE_ENABLED">FALSE</field>
                        <next>
                          <block type="trade_definition_restartonerror" id="gLmQYbPhRZTP;$NyXRfF" deletable="false" movable="false">
                            <field name="RESTARTONERROR">TRUE</field>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="INITIALIZATION">
      <block type="variables_set" id="$*k6w0w?HoW%@WJ[CQ[G">
        <field name="VAR" id="6o_9Nu#$3m#.?zV=ahjK">Prediction</field>
        <value name="VALUE">
          <block type="math_number" id="32Y!`!Hso?aG%-1Uy8(b">
            <field name="NUM">9</field>
          </block>
        </value>
        <next>
          <block type="variables_set" id="i,bH`bnP@u(WV|7i!Sz`">
            <field name="VAR" id="RHgF*C%#h{|Ha5u;#5j?">Stake one</field>
            <value name="VALUE">
              <block type="math_number" id="7B-XT,+7XN#v|1Aa($uz">
                <field name="NUM">6</field>
              </block>
            </value>
            <next>
              <block type="variables_set" id="IxCR1Na7WMsp2@803{L+">
                <field name="VAR" id="(obU$~2J]hr/k0`hgX8/">Stake two</field>
                <value name="VALUE">
                  <block type="math_number" id="JCmZC^lr+W)d!Wm0(~pL">
                    <field name="NUM">6</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="gOH1q(Lqj,Kv4+[K|s=-" collapsed="true">
                    <field name="VAR" id="4+UcGJ_mE@7^Z8-d@JaJ">VTY</field>
                    <value name="VALUE">
                      <block type="math_number" id="t`]#Z4#DVjSp^{RXbqGP">
                        <field name="NUM">2</field>
                      </block>
                    </value>
                    <next>
                      <block type="variables_set" id="S~{pMtR)[Lvvqd~`Z8;P" collapsed="true">
                        <field name="VAR" id="4+UcGJ_mE@7^Z8-d@JaJ">VTY</field>
                        <value name="VALUE">
                          <block type="math_number" id="4}^V3mkM4DsZ0BU0=mah">
                            <field name="NUM">2</field>
                          </block>
                        </value>
                        <next>
                          <block type="variables_set" id="0!#{3_;P@j;~oXkHmGY|" collapsed="true">
                            <field name="VAR" id="FlX%77!9AMWGEu#,)Nme">Y</field>
                            <value name="VALUE">
                              <block type="math_number" id="49t$ms:#8*kc6Uer8#](">
                                <field name="NUM">10000</field>
                              </block>
                            </value>
                            <next>
                              <block type="variables_set" id="Qc67z:e{[5e.IXaaBjyc" collapsed="true">
                                <field name="VAR" id="MgsL8H;~`;r/^Tuh1)y=">BBFTY</field>
                                <value name="VALUE">
                                  <block type="math_number" id="vql3]S5A-Vy3F8hyVNw2">
                                    <field name="NUM">0.8</field>
                                  </block>
                                </value>
                                <next>
                                  <block type="variables_set" id="5#2ZirLJF@(|:)/2$qkw" collapsed="true">
                                    <field name="VAR" id="rUPB[)tUi3we;byHr`7]">value</field>
                                    <value name="VALUE">
                                      <block type="math_number" id=":N}6E]-XIDi$;w[B!qq}">
                                        <field name="NUM">1</field>
                                      </block>
                                    </value>
                                    <next>
                                      <block type="variables_set" id=")Ew(HOT3Ya`h~X@tWbFH" collapsed="true">
                                        <field name="VAR" id="FlX%77!9AMWGEu#,)Nme">Y</field>
                                        <value name="VALUE">
                                          <block type="math_number" id="vWP1WDdBvi,8ov:[;)r!">
                                            <field name="NUM">10000</field>
                                          </block>
                                        </value>
                                        <next>
                                          <block type="variables_set" id="rY9MYKWTV39sxDv;+kdr" collapsed="true">
                                            <field name="VAR" id="a?$LW}C4v+XX:Lc%6-OS">A</field>
                                            <value name="VALUE">
                                              <block type="math_number" id="4cF?LqK#AJV6Jx.44($6">
                                                <field name="NUM">10000</field>
                                              </block>
                                            </value>
                                            <next>
                                              <block type="variables_set" id="CpM6_k*o?xE[xaZ[Q@_-" collapsed="true">
                                                <field name="VAR" id="MgsL8H;~`;r/^Tuh1)y=">BBFTY</field>
                                                <value name="VALUE">
                                                  <block type="math_number" id="`2(cyquM9#yV^pDqZ`gJ">
                                                    <field name="NUM">0.8</field>
                                                  </block>
                                                </value>
                                                <next>
                                                  <block type="variables_set" id="AVjL=h`o6*I47so]klAr" collapsed="true">
                                                    <field name="VAR" id="a?$LW}C4v+XX:Lc%6-OS">A</field>
                                                    <value name="VALUE">
                                                      <block type="math_number" id="SfHER4$J,mYKd}S93dG5">
                                                        <field name="NUM">10000</field>
                                                      </block>
                                                    </value>
                                                    <next>
                                                      <block type="variables_set" id="(63$FG$BlqjfrDaXveCu" collapsed="true">
                                                        <field name="VAR" id="a?$LW}C4v+XX:Lc%6-OS">A</field>
                                                        <value name="VALUE">
                                                          <block type="math_number" id=".xqtE04:82AJItf29Lz-">
                                                            <field name="NUM">10000</field>
                                                          </block>
                                                        </value>
                                                        <next>
                                                          <block type="variables_set" id="2D6y0K:f,6X2z]x;Wsk]" collapsed="true">
                                                            <field name="VAR" id="MgsL8H;~`;r/^Tuh1)y=">BBFTY</field>
                                                            <value name="VALUE">
                                                              <block type="math_number" id="pJ7Sq_=uOcYd.ekmDs|u">
                                                                <field name="NUM">0.8</field>
                                                              </block>
                                                            </value>
                                                            <next>
                                                              <block type="variables_set" id="_b@TFjsl9pRigdtZawwI" collapsed="true">
                                                                <field name="VAR" id="4+UcGJ_mE@7^Z8-d@JaJ">VTY</field>
                                                                <value name="VALUE">
                                                                  <block type="math_number" id="oTx*uP=@uTBR=WqbNghK">
                                                                    <field name="NUM">2</field>
                                                                  </block>
                                                                </value>
                                                              </block>
                                                            </next>
                                                          </block>
                                                        </next>
                                                      </block>
                                                    </next>
                                                  </block>
                                                </next>
                                              </block>
                                            </next>
                                          </block>
                                        </next>
                                      </block>
                                    </next>
                                  </block>
                                </next>
                              </block>
                            </next>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="SUBMARKET">
      <block type="trade_definition_tradeoptions" id="Kszy$t;dmMnU=/jxC1]D" collapsed="true">
        <mutation xmlns="http://www.w3.org/1999/xhtml" has_first_barrier="false" has_second_barrier="false" has_prediction="true"></mutation>
        <field name="DURATIONTYPE_LIST">t</field>
        <value name="DURATION">
          <block type="variables_get" id="7a8:-3ae0d--8A)@0h[z" collapsed="true">
            <field name="VAR" id="rUPB[)tUi3we;byHr`7]">value</field>
          </block>
        </value>
        <value name="AMOUNT">
          <block type="variables_get" id="IuC0}Qm@Z=mX]d}3?w(3">
            <field name="VAR" id="RHgF*C%#h{|Ha5u;#5j?">Stake one</field>
          </block>
        </value>
        <value name="PREDICTION">
          <shadow type="math_number_positive" id="a_H_[)/|rA%3|$8j*k6P">
            <field name="NUM">1</field>
          </shadow>
          <block type="variables_get" id="+:eFWwjF^q~1G=PBd)#q">
            <field name="VAR" id="6o_9Nu#$3m#.?zV=ahjK">Prediction</field>
          </block>
        </value>
      </block>
    </statement>
  </block>
  <block type="after_purchase" id="tv^t?%1u*:W4IT%pX|zi" x="714" y="50">
    <statement name="AFTERPURCHASE_STACK">
      <block type="controls_if" id="sJy=W27to^xWtEfx^!In">
        <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
        <value name="IF0">
          <block type="contract_check_result" id="f]tjuq1|sUj(]nr6M7kS" collapsed="true">
            <field name="CHECK_RESULT">win</field>
          </block>
        </value>
        <statement name="DO0">
          <block type="text_join" id="0S$z$|5B/gB6]hVHfFNe" collapsed="true">
            <field name="VARIABLE" id="N5h(uF)6G2cql)h{ZR;h">text</field>
            <statement name="STACK">
              <block type="text_statement" id="X%Gq.^p$,4upbkB:6uG6" collapsed="true">
                <value name="TEXT">
                  <shadow type="text" id="wR[F5j:j}_}AIHKj`/@]">
                    <field name="TEXT"></field>
                  </shadow>
                  <block type="text" id="ofXQ2#?`|+.v^e{h7qwK">
                    <field name="TEXT">AI</field>
                  </block>
                </value>
                <next>
                  <block type="text_statement" id="712]i{I(V{L6i9.Wc?T^">
                    <value name="TEXT">
                      <shadow type="text" id="4M~2JZK-6NWn8_tB%Lfz">
                        <field name="TEXT"></field>
                      </shadow>
                      <block type="read_details" id="ntYjAA|;7owvxr2YL!M;" collapsed="true">
                        <field name="DETAIL_INDEX">4</field>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </statement>
            <next>
              <block type="notify" id=".`1tJfY^10WASKPQ$J_~" collapsed="true">
                <field name="NOTIFICATION_TYPE">success</field>
                <field name="NOTIFICATION_SOUND">silent</field>
                <value name="MESSAGE">
                  <block type="variables_get" id="P:a_6!^X|`N-X~g:PrFP">
                    <field name="VAR" id="N5h(uF)6G2cql)h{ZR;h">text</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="$qa*:ge{6Bl[W~fx545#" collapsed="true">
                    <field name="VAR" id="RHgF*C%#h{|Ha5u;#5j?">Stake one</field>
                    <value name="VALUE">
                      <block type="variables_get" id="Gkvu^[]/3G:cW|lCmedw">
                        <field name="VAR" id="(obU$~2J]hr/k0`hgX8/">Stake two</field>
                      </block>
                    </value>
                    <next>
                      <block type="variables_set" id="?wi*6(=={p4QrTE?E`2~" collapsed="true">
                        <field name="VAR" id="kQ+Ke(]*eQpM[qgfd~+g">Contador</field>
                        <value name="VALUE">
                          <block type="math_number" id="Yq%a{YtZLKX!w;j1RXP?">
                            <field name="NUM">0</field>
                          </block>
                        </value>
                        <next>
                          <block type="variables_set" id="6[t)pQa;ZT2W@-@9v[$u">
                            <field name="VAR" id="6o_9Nu#$3m#.?zV=ahjK">Prediction</field>
                            <value name="VALUE">
                              <block type="math_number" id="q2_E,aeD4j$(0ZpBYdbz">
                                <field name="NUM">9</field>
                              </block>
                            </value>
                            <next>
                              <block type="variables_set" id="MQ=0$V5s(xkFcK*yxOA_" collapsed="true">
                                <field name="VAR" id="rUPB[)tUi3we;byHr`7]">value</field>
                                <value name="VALUE">
                                  <block type="variables_get" id="ENK7$E3PQkxTYfCWa`QH">
                                    <field name="VAR" id="rUPB[)tUi3we;byHr`7]">value</field>
                                  </block>
                                </value>
                                <next>
                                  <block type="trade_again" id="cw].p*FJWZBupT$eqR!M"></block>
                                </next>
                              </block>
                            </next>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </statement>
        <statement name="ELSE">
          <block type="text_join" id=":4hsJB@ruUmH-fJS,4m0" collapsed="true">
            <field name="VARIABLE" id=")-9T~6%gxe29l#C#)qts">text1</field>
            <statement name="STACK">
              <block type="text_statement" id="u9XqKYD#Vai.X-[`,uso">
                <value name="TEXT">
                  <shadow type="text" id="y/YHKw_5m-@Kf^i`(yRS">
                    <field name="TEXT"></field>
                  </shadow>
                  <block type="text" id="MiNQ+-d*C683iU0`o4md">
                    <field name="TEXT">AI</field>
                  </block>
                </value>
                <next>
                  <block type="text_statement" id="Do5M]Ky9%Gr{(pV#Cz/5">
                    <value name="TEXT">
                      <shadow type="text" id="vmMsInvTJ-hw~JKi6lWx">
                        <field name="TEXT"></field>
                      </shadow>
                      <block type="math_single" id="?fQk@]behN#[bIF{rF[7">
                        <field name="OP">ABS</field>
                        <value name="NUM">
                          <shadow type="math_number" id="9~o+DI$6c3rP,K3E2x%|">
                            <field name="NUM">9</field>
                          </shadow>
                          <block type="read_details" id="0SbR!Yt]O*F2Cb4A+|lS">
                            <field name="DETAIL_INDEX">4</field>
                          </block>
                        </value>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </statement>
            <next>
              <block type="notify" id="F)C~I.3[,jjDu_F;ICpi" collapsed="true">
                <field name="NOTIFICATION_TYPE">warn</field>
                <field name="NOTIFICATION_SOUND">silent</field>
                <value name="MESSAGE">
                  <block type="variables_get" id="px#T3t]nmR@pu-!CQe;#">
                    <field name="VAR" id=")-9T~6%gxe29l#C#)qts">text1</field>
                  </block>
                </value>
                <next>
                  <block type="math_change" id="0c)nVxs6Nrk$QLnPk)w:">
                    <field name="VAR" id="kQ+Ke(]*eQpM[qgfd~+g">Contador</field>
                    <value name="DELTA">
                      <shadow type="math_number" id="*qO$Mp_db8_!D8g#XKBP">
                        <field name="NUM">1</field>
                      </shadow>
                    </value>
                    <next>
                      <block type="variables_set" id="2F,Ffs8?a5dKUilg%@i[" collapsed="true">
                        <field name="VAR" id="rUPB[)tUi3we;byHr`7]">value</field>
                        <value name="VALUE">
                          <block type="variables_get" id="3FZHSh;f4e.sAFJ1A`}=">
                            <field name="VAR" id="rUPB[)tUi3we;byHr`7]">value</field>
                          </block>
                        </value>
                        <next>
                          <block type="variables_set" id="C?C4r`^,0v,D9e*(h^Iw">
                            <field name="VAR" id="6o_9Nu#$3m#.?zV=ahjK">Prediction</field>
                            <value name="VALUE">
                              <block type="math_number" id="Hj6D[]Ga/fY{~w/5@TCh">
                                <field name="NUM">5</field>
                              </block>
                            </value>
                            <next>
                              <block type="controls_if" id="6L?zK,]Qws.xo?*(GemE" collapsed="true">
                                <value name="IF0">
                                  <block type="logic_compare" id="P)iuYC-KN0]}(W?=;)H+">
                                    <field name="OP">GTE</field>
                                    <value name="A">
                                      <block type="variables_get" id="BS1Kb/,`O@y}{9Od4rV5">
                                        <field name="VAR" id="kQ+Ke(]*eQpM[qgfd~+g">Contador</field>
                                      </block>
                                    </value>
                                    <value name="B">
                                      <block type="variables_get" id="@L`XIY~OfzFLV?E~`X~Q" collapsed="true">
                                        <field name="VAR" id="4+UcGJ_mE@7^Z8-d@JaJ">VTY</field>
                                      </block>
                                    </value>
                                  </block>
                                </value>
                                <statement name="DO0">
                                  <block type="math_change" id="R7QtsoT2WFk`*MK0_fy*">
                                    <field name="VAR" id="RHgF*C%#h{|Ha5u;#5j?">Stake one</field>
                                    <value name="DELTA">
                                      <shadow type="math_number" id="h(2BZh#T.]odF-bqQKcd">
                                        <field name="NUM">1</field>
                                      </shadow>
                                      <block type="math_arithmetic" id="Gl2+IAN)5b!-?RayfW3W">
                                        <field name="OP">MULTIPLY</field>
                                        <value name="A">
                                          <shadow type="math_number" id="3J6xZNy/`wRJZUYz4DLX">
                                            <field name="NUM">1</field>
                                          </shadow>
                                          <block type="math_single" id="Dnw,PYK`H_I-M2voTUTl">
                                            <field name="OP">ABS</field>
                                            <value name="NUM">
                                              <shadow type="math_number" id="F8ts_dw*D9O8R7_^C/Mq">
                                                <field name="NUM">9</field>
                                              </shadow>
                                              <block type="read_details" id="cj527u@:f[5O%EPU/jOk">
                                                <field name="DETAIL_INDEX">4</field>
                                              </block>
                                            </value>
                                          </block>
                                        </value>
                                        <value name="B">
                                          <shadow type="math_number" id=");k~+=ZUM,8V7ovDrIH/">
                                            <field name="NUM">1</field>
                                          </shadow>
                                          <block type="variables_get" id="Z~)63Nf5@3llDqm$7(xT">
                                            <field name="VAR" id="MgsL8H;~`;r/^Tuh1)y=">BBFTY</field>
                                          </block>
                                        </value>
                                      </block>
                                    </value>
                                  </block>
                                </statement>
                              </block>
                            </next>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </statement>
        <next>
          <block type="text_join" id="fS~U8K1ETkV}kGezbOad" collapsed="true">
            <field name="VARIABLE" id="LN}r@t3f(x%mM[7-P|ft">text2</field>
            <statement name="STACK">
              <block type="text_statement" id="0.hK03^|s./];j4,8F0#">
                <value name="TEXT">
                  <shadow type="text" id="INApBo@yKFM/;3@?@643">
                    <field name="TEXT"></field>
                  </shadow>
                  <block type="text" id=";pRO7vFM;D,$uWpJQ7lz">
                    <field name="TEXT">AI</field>
                  </block>
                </value>
                <next>
                  <block type="text_statement" id="Mdm6aP_wz;NLoG8Tg,5F">
                    <value name="TEXT">
                      <shadow type="text" id="-id||!ljv_ck6p],Hr3Y">
                        <field name="TEXT"></field>
                      </shadow>
                      <block type="total_profit" id="g?#o-:15|FA^3)X?~EV["></block>
                    </value>
                  </block>
                </next>
              </block>
            </statement>
            <next>
              <block type="notify" id="eb[Y2^{r2HB9GDtlA1lb" collapsed="true">
                <field name="NOTIFICATION_TYPE">info</field>
                <field name="NOTIFICATION_SOUND">silent</field>
                <value name="MESSAGE">
                  <block type="variables_get" id=":elwQ?kI@R)@Hga*[Fn{">
                    <field name="VAR" id="LN}r@t3f(x%mM[7-P|ft">text2</field>
                  </block>
                </value>
                <next>
                  <block type="controls_if" id="FLa][VYobXnlFN$6x]O#" collapsed="true">
                    <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                    <value name="IF0">
                      <block type="logic_compare" id="P3aB~_;AV$cEuSque9+m">
                        <field name="OP">LT</field>
                        <value name="A">
                          <block type="total_profit" id="rFO7]5)Jjs9$+b2SUSPi" collapsed="true"></block>
                        </value>
                        <value name="B">
                          <block type="variables_get" id="7I/|[$VXtY-)v_kHw=1T">
                            <field name="VAR" id="a?$LW}C4v+XX:Lc%6-OS">A</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="controls_if" id="([L*WBMg~O565fbH|+*?">
                        <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                        <value name="IF0">
                          <block type="logic_operation" id=":XEl)hs$uy/s?bvYCc0F">
                            <field name="OP">AND</field>
                            <value name="A">
                              <block type="math_number_property" id="C38hi(S8$HDRG{A8,X$$">
                                <mutation xmlns="http://www.w3.org/1999/xhtml" divisor_input="false"></mutation>
                                <field name="PROPERTY">NEGATIVE</field>
                                <value name="NUMBER_TO_CHECK">
                                  <shadow type="math_number" id="nUOBSl#cC[ZI/+spB7wR">
                                    <field name="NUM">0</field>
                                  </shadow>
                                  <block type="total_profit" id="Oe]K,lj}NJFWB2MWDd6^" collapsed="true"></block>
                                </value>
                              </block>
                            </value>
                            <value name="B">
                              <block type="logic_compare" id="rw*[bmNBySP];v-H$~W*">
                                <field name="OP">GTE</field>
                                <value name="A">
                                  <block type="math_single" id="l9S0Z`^8|s{V9{7OgBPr">
                                    <field name="OP">ABS</field>
                                    <value name="NUM">
                                      <shadow type="math_number" id="TcnDT+bnC{BD.]cZB32f">
                                        <field name="NUM">9</field>
                                      </shadow>
                                      <block type="total_profit" id="iKiJ.QGJrpE8@DN1TZ:8"></block>
                                    </value>
                                  </block>
                                </value>
                                <value name="B">
                                  <block type="variables_get" id="3,3+c=7xscv63o!sg+|7">
                                    <field name="VAR" id="FlX%77!9AMWGEu#,)Nme">Y</field>
                                  </block>
                                </value>
                              </block>
                            </value>
                          </block>
                        </value>
                        <statement name="DO0">
                          <block type="text_print" id="Az*o2P=-$tl_JI#(Ppn}">
                            <value name="TEXT">
                              <shadow type="text" id="xfPeIzBSRfDoK%u-*`*?">
                                <field name="TEXT">Stop Loss</field>
                              </shadow>
                            </value>
                          </block>
                        </statement>
                        <statement name="ELSE">
                          <block type="timeout" id="W`$Um/y[eJMUFV~[k2q,">
                            <statement name="TIMEOUTSTACK">
                              <block type="trade_again" id="ZP/%[]2Z][ypf^N]KtdN"></block>
                            </statement>
                            <value name="SECONDS">
                              <shadow type="math_number" id="AZ%nLTP@%x4,M2_ao[:Y">
                                <field name="NUM">0</field>
                              </shadow>
                            </value>
                          </block>
                        </statement>
                      </block>
                    </statement>
                    <statement name="ELSE">
                      <block type="text_join" id="hJC6;.1VNm:|+k^!YHkZ">
                        <field name="VARIABLE" id="^3d~6DUQ!o8XQTn.XB!c">text3</field>
                        <statement name="STACK">
                          <block type="text_statement" id="2,rW;qA:(lJ,DC]3|3.9">
                            <value name="TEXT">
                              <shadow type="text" id="^HSAwEMu*cN@f)x?[znV">
                                <field name="TEXT"></field>
                              </shadow>
                              <block type="text" id="dkaG*J8K^vbB[W9:3{Dy">
                                <field name="TEXT">AI</field>
                              </block>
                            </value>
                            <next>
                              <block type="text_statement" id="[.-J|u^m}oG9-~tv@9NT">
                                <value name="TEXT">
                                  <shadow type="text" id="q+:H%Y=!J~C/YZV^_iBH">
                                    <field name="TEXT"></field>
                                  </shadow>
                                  <block type="total_profit" id="7E3THS+!(5Cb3Wu-j5#K"></block>
                                </value>
                              </block>
                            </next>
                          </block>
                        </statement>
                        <next>
                          <block type="text_print" id=".FvHuk5.bw.pyf8G*b57">
                            <value name="TEXT">
                              <shadow type="text" id="L{thT}[kyuD~x%2xR7`E">
                                <field name="TEXT">abc</field>
                              </shadow>
                              <block type="variables_get" id="=M9cG}jNv`EDYOmL)x$0">
                                <field name="VAR" id="^3d~6DUQ!o8XQTn.XB!c">text3</field>
                              </block>
                            </value>
                          </block>
                        </next>
                      </block>
                    </statement>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
  </block>
  <block type="before_purchase" id="_P/][8`46qGCyQ$AvmG!" collapsed="true" deletable="false" x="0" y="1380">
    <statement name="BEFOREPURCHASE_STACK">
      <block type="apollo_purchase" id="U]c~],9#*VpiiX^=R!M1">
        <field name="PURCHASE_LIST">DIGITUNDER</field>
      </block>
    </statement>
  </block>
  <block type="text" id="#}~^b3MFl?GCJ_Y!eo{~" disabled="true" x="0" y="1476">
    <field name="TEXT">Killer market Ai</field>
  </block>
</xml>