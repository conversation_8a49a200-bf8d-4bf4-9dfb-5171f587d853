[{"name": "COOLKID.xml", "file": "COOLKID.xml"}, {"name": "Candle mine version 3.1.xml", "file": "Candle mine version 3.1.xml"}, {"name": "Deriv wizard 1.xml", "file": "Deriv wizard 1.xml"}, {"name": "Digit hyper.xml", "file": "Digit hyper.xml"}, {"name": "ENHANCED_Digit_Switcher🤖VERSION5.xml", "file": "ENHANCED_Digit_Switcher🤖VERSION5.xml"}, {"name": "Even odd speed bot.xml", "file": "Even odd speed bot.xml"}, {"name": "FALCON BOT.xml", "file": "FALCON BOT.xml"}, {"name": "GIBUU V8 PRO.xml", "file": "GIBUU V8 PRO.xml"}, {"name": "M27 Original version.xml", "file": "M27 Original version.xml"}, {"name": "MATCHES AND DIFFERS BOT.xml", "file": "MATCHES AND DIFFERS BOT.xml"}, {"name": "MEGA PRO BOT.xml", "file": "MEGA PRO BOT.xml"}, {"name": "Mask evenodd bot.xml", "file": "Mask evenodd bot.xml"}, {"name": "NIGHT  CAP PRINTER BOT.xml", "file": "NIGHT  CAP PRINTER BOT.xml"}, {"name": "Over-Destroyer💀by state fx.xml", "file": "Over-Destroyer💀by state fx.xml"}, {"name": "SCAPLEX   Ai  SN4 (1) (1).xml", "file": "SCAPLEX   Ai  SN4 (1) (1).xml"}, {"name": "SCAUCER SPEED BOT 🇱🇷.xml", "file": "SCAUCER SPEED BOT 🇱🇷.xml"}, {"name": "STATE.HnR.xml", "file": "STATE.HnR.xml"}, {"name": "STATES_Digit_Switcher🤖V2.xml", "file": "STATES_Digit_Switcher🤖V2.xml"}, {"name": "THE DOLLAR PRO.xml", "file": "THE DOLLAR PRO.xml"}, {"name": "THE TREND LOVER.xml", "file": "THE TREND LOVER.xml"}, {"name": "TRADE CITY BOT VERSION 2.1.xml", "file": "TRADE CITY BOT VERSION 2.1.xml"}, {"name": "The Over 19 switcher5.xml", "file": "The Over 19 switcher5.xml"}, {"name": "ULTRA AI 2025.xml", "file": "ULTRA AI 2025.xml"}, {"name": "_version 4. AI bot 2025..by states fx.xml", "file": "_version 4. AI bot 2025..by states fx.xml"}, {"name": "mask matches speed bot 📈.xml", "file": "mask matches speed bot 📈.xml"}, {"name": "master G8 (OVER UNDER) BY STATE FX VERSION 1 (2026).xml", "file": "master G8 (OVER UNDER) BY STATE FX VERSION 1 (2026).xml"}]