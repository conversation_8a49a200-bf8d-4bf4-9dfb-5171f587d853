<xml xmlns="https://developers.google.com/blockly/xml" is_dbot="true" collection="false">
  <variables>
    <variable id="W4$:ZQCmEz#8+=4ysv5Y">Loss</variable>
    <variable id="j}8O`Vs+RJljIwPu-_:_">Stake</variable>
    <variable id="mXtFswo{p,|%W1:V-$+r">Target Profit</variable>
    <variable id="Z$1~ke%ib-X*$jvHvTZ-">text</variable>
    <variable id="~$!A`|EX}S5t/D4JsEJl">text1</variable>
    <variable id="0@xj{M^u?~ln2y~+o;h9">text2</variable>
    <variable id="%L?;380E6Lr^3b.%}t5Q">stake 2</variable>
    <variable id="lgSt*w)b%TEj7!b{j4qF">text3</variable>
  </variables>
  <block type="trade_definition" id="hHpZx~@w+/mhzb~Z6fGY" deletable="false" x="0" y="0">
    <statement name="TRADE_OPTIONS">
      <block type="trade_definition_market" id="=mo]QKPowy0lvYTo)2`n" deletable="false" movable="false">
        <field name="MARKET_LIST">synthetic_index</field>
        <field name="SUBMARKET_LIST">random_index</field>
        <field name="SYMBOL_LIST">1HZ10V</field>
        <next>
          <block type="trade_definition_tradetype" id="rfWN*h57T62Pm+Twc^;X" deletable="false" movable="false">
            <field name="TRADETYPECAT_LIST">digits</field>
            <field name="TRADETYPE_LIST">overunder</field>
            <next>
              <block type="trade_definition_contracttype" id="H@.HoLlp%3:GixJ9_3D!" deletable="false" movable="false">
                <field name="TYPE_LIST">both</field>
                <next>
                  <block type="trade_definition_candleinterval" id="q!BFRb(69^n(fuN;7Y{q" deletable="false" movable="false">
                    <field name="CANDLEINTERVAL_LIST">60</field>
                    <next>
                      <block type="trade_definition_restartbuysell" id="f,3Y2l?ZU8wg8Rx{#B(9" deletable="false" movable="false">
                        <field name="TIME_MACHINE_ENABLED">FALSE</field>
                        <next>
                          <block type="trade_definition_restartonerror" id="(Pj!]~u9c?-tw,4Q{^~~" deletable="false" movable="false">
                            <field name="RESTARTONERROR">TRUE</field>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="INITIALIZATION">
      <block type="variables_set" id="?o$$koRygTD-|f`;Q+!~">
        <field name="VAR" id="W4$:ZQCmEz#8+=4ysv5Y">Loss</field>
        <value name="VALUE">
          <block type="math_number" id="#V_Ih64Gi)6*y5XUXD.9">
            <field name="NUM">60</field>
          </block>
        </value>
        <next>
          <block type="variables_set" id="1Oh)q3_67sOkoA+H,Y):">
            <field name="VAR" id="mXtFswo{p,|%W1:V-$+r">Target Profit</field>
            <value name="VALUE">
              <block type="math_number" id="{[2lYX5?+Tr,*NR7`|qc">
                <field name="NUM">60</field>
              </block>
            </value>
            <next>
              <block type="variables_set" id="Zf5-L[7Zz*o!W!DBEYC:">
                <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">Stake</field>
                <value name="VALUE">
                  <block type="math_number" id="|9JNc9G(jSgjT5AkHle[">
                    <field name="NUM">5</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="]|LR6aZ68?e=Czx]d.=x">
                    <field name="VAR" id="%L?;380E6Lr^3b.%}t5Q">stake 2</field>
                    <value name="VALUE">
                      <block type="math_number" id="VFs)3LZm{3u+bMqEOX5=">
                        <field name="NUM">5</field>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="SUBMARKET">
      <block type="trade_definition_tradeoptions" id=")o+SW~63)9*3D1f`h?AZ">
        <mutation xmlns="http://www.w3.org/1999/xhtml" has_first_barrier="false" has_second_barrier="false" has_prediction="true"></mutation>
        <field name="DURATIONTYPE_LIST">t</field>
        <value name="DURATION">
          <block type="math_number" id="fcaC]OZ]KlhUYzUz[f7K">
            <field name="NUM">1</field>
          </block>
        </value>
        <value name="AMOUNT">
          <block type="variables_get" id="iUWyDVf273C#S#ckZnI@">
            <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">Stake</field>
          </block>
        </value>
        <value name="PREDICTION">
          <shadow type="math_number" id="AM,s:lp/r3lolhiJEW_X">
            <field name="NUM">1</field>
          </shadow>
        </value>
      </block>
    </statement>
  </block>
  <block type="during_purchase" id="b+dQDlxpGV$J,_LEOo+L" collapsed="true" x="714" y="0"></block>
  <block type="after_purchase" id="{:,Skg0fqC$b*U7hJ2Z," collapsed="true" x="714" y="96">
    <statement name="AFTERPURCHASE_STACK">
      <block type="controls_if" id="~^W8{kJGE;pyMy6(6|mj">
        <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
        <value name="IF0">
          <block type="contract_check_result" id="Tc+$cs6?Jn@*BN5!$%Pp">
            <field name="CHECK_RESULT">win</field>
          </block>
        </value>
        <statement name="DO0">
          <block type="text_join" id="r83/uX#`D*V^:1f!iYWx">
            <field name="VARIABLE" id="Z$1~ke%ib-X*$jvHvTZ-">text</field>
            <statement name="STACK">
              <block type="text_statement" id="IbHu[Gy4Rg-iRdyadq6N">
                <value name="TEXT">
                  <shadow type="text" id=")3vTl/tqnSH7Gqg5+SOD">
                    <field name="TEXT"></field>
                  </shadow>
                  <block type="text" id="KGZ#SJ6e8F?h1P,wO3pT">
                    <field name="TEXT">TITAN TRADERS HAS PRINTED</field>
                  </block>
                </value>
                <next>
                  <block type="text_statement" id="r;lU)(})!DNe@P3[M~L,">
                    <value name="TEXT">
                      <shadow type="text" id="IV*Tb3S,@)sdBRt+,~p2">
                        <field name="TEXT"></field>
                      </shadow>
                      <block type="read_details" id="O0JG`Q,cKesI%uG1bAyX">
                        <field name="DETAIL_INDEX">4</field>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </statement>
            <next>
              <block type="notify" id="l@97MRW+D?e5)L6p1$[c">
                <field name="NOTIFICATION_TYPE">success</field>
                <field name="NOTIFICATION_SOUND">silent</field>
                <value name="MESSAGE">
                  <block type="variables_get" id="}t(#]M-Ln^I+XKo|*e#`">
                    <field name="VAR" id="Z$1~ke%ib-X*$jvHvTZ-">text</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="@H9]_e]m4=yIKq+!!g#{">
                    <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">Stake</field>
                    <value name="VALUE">
                      <block type="variables_get" id="0B0GK}ueI:]2,s%3RW6S">
                        <field name="VAR" id="%L?;380E6Lr^3b.%}t5Q">stake 2</field>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </statement>
        <statement name="ELSE">
          <block type="text_join" id="@dpnl8_/|-Rz~!v@t{_C">
            <field name="VARIABLE" id="~$!A`|EX}S5t/D4JsEJl">text1</field>
            <statement name="STACK">
              <block type="text_statement" id="][sVZc+k4-b.#[0Y+PDA">
                <value name="TEXT">
                  <shadow type="text" id="pqJu4iWc.S9j1?hK5).h">
                    <field name="TEXT"></field>
                  </shadow>
                  <block type="text" id="#_tIQkcNN[*i!$7.imyI">
                    <field name="TEXT">LOSS ENCOUNTERED</field>
                  </block>
                </value>
                <next>
                  <block type="text_statement" id="E#;Va!e,}hZ/$d|8684;">
                    <value name="TEXT">
                      <shadow type="text" id="{^B/cY:mc%UY{IKQa(E[">
                        <field name="TEXT"></field>
                      </shadow>
                      <block type="math_single" id="`oo_=C$.U$5d[R-G+aqG">
                        <field name="OP">ABS</field>
                        <value name="NUM">
                          <shadow type="math_number" id="DH_bv:/@Lluj`G)SZb2)">
                            <field name="NUM">9</field>
                          </shadow>
                          <block type="read_details" id="Ll6C.XcuG4(,*3ZaYU4,">
                            <field name="DETAIL_INDEX">4</field>
                          </block>
                        </value>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </statement>
            <next>
              <block type="notify" id="X.nS(^,mhv/=RQ`}aphv">
                <field name="NOTIFICATION_TYPE">warn</field>
                <field name="NOTIFICATION_SOUND">silent</field>
                <value name="MESSAGE">
                  <block type="variables_get" id="Cb6l(_SZF2RK([bhc1Wl">
                    <field name="VAR" id="~$!A`|EX}S5t/D4JsEJl">text1</field>
                  </block>
                </value>
                <next>
                  <block type="math_change" id="X_/;T$4!];`?iFJG(4x2">
                    <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">Stake</field>
                    <value name="DELTA">
                      <shadow type="math_number" id="s87^D4]1#`9zg{uLnzJ*">
                        <field name="NUM">1</field>
                      </shadow>
                      <block type="math_arithmetic" id="r+YWay6!pO4qa%#cAZBP">
                        <field name="OP">MULTIPLY</field>
                        <value name="A">
                          <shadow type="math_number" id="*0uw@{6nT+?dHV|1-P1H">
                            <field name="NUM">1</field>
                          </shadow>
                          <block type="math_single" id="x2-pp1;(}GPb*m*BM^8d">
                            <field name="OP">ABS</field>
                            <value name="NUM">
                              <shadow type="math_number" id="hzv@wUx({Vm?LM;,)[[e">
                                <field name="NUM">9</field>
                              </shadow>
                              <block type="read_details" id="+]hxK@dtSqj8zbVx0Sy?">
                                <field name="DETAIL_INDEX">4</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <value name="B">
                          <shadow type="math_number" id="-Y%$$Ti#?H0P`0Oo}bP|">
                            <field name="NUM">1</field>
                          </shadow>
                          <block type="math_number" id="wDpbaUAM.t@^SqD.2;-_">
                            <field name="NUM">1</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <next>
                      <block type="controls_if" id="Uf=]PM,Lwb9xLez_`7y.">
                        <value name="IF0">
                          <block type="logic_compare" id="qJZ[JH$y9H`qd*)6buD=">
                            <field name="OP">GTE</field>
                            <value name="A">
                              <block type="math_single" id="(U}t=|N]Lg?4(`Chn4j[">
                                <field name="OP">ABS</field>
                                <value name="NUM">
                                  <shadow type="math_number" id="ZwCbd]nG*sq1/71{@$7g">
                                    <field name="NUM">9</field>
                                  </shadow>
                                  <block type="read_details" id="S;=eJC`VLkK!lQL7Dl`E">
                                    <field name="DETAIL_INDEX">4</field>
                                  </block>
                                </value>
                              </block>
                            </value>
                            <value name="B">
                              <block type="variables_get" id="[+Y(~jG%Iti=~O5.8Ep{">
                                <field name="VAR" id="W4$:ZQCmEz#8+=4ysv5Y">Loss</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <statement name="DO0">
                          <block type="variables_set" id="-^q]7wgQ:HEV{sMX?6?Y">
                            <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">Stake</field>
                            <value name="VALUE">
                              <block type="variables_get" id="TY}agqYPS%l3wAh-HG}p">
                                <field name="VAR" id="%L?;380E6Lr^3b.%}t5Q">stake 2</field>
                              </block>
                            </value>
                          </block>
                        </statement>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </statement>
        <next>
          <block type="text_join" id="2Zq`Q^v[U9*?7evT(p:b">
            <field name="VARIABLE" id="0@xj{M^u?~ln2y~+o;h9">text2</field>
            <statement name="STACK">
              <block type="text_statement" id="5%cFnG.`4ANvc)y%K#j(">
                <value name="TEXT">
                  <shadow type="text" id="2(kRs[Qcl-Ou7fNNj0Z0">
                    <field name="TEXT"></field>
                  </shadow>
                  <block type="text" id="3aygOX8+;0K6Ey4q))L~">
                    <field name="TEXT">Total Profit: </field>
                  </block>
                </value>
                <next>
                  <block type="text_statement" id=".^p.D^(`x2ZuC-Gxt3$V">
                    <value name="TEXT">
                      <shadow type="text" id="N`Kq8l*#H;G}|j~3pjiy">
                        <field name="TEXT"></field>
                      </shadow>
                      <block type="total_profit" id="Ho@|i#1bBO[j`j6$Ay_M"></block>
                    </value>
                  </block>
                </next>
              </block>
            </statement>
            <next>
              <block type="notify" id="C2W%_xZsiPFdwT)}Tb^0">
                <field name="NOTIFICATION_TYPE">info</field>
                <field name="NOTIFICATION_SOUND">silent</field>
                <value name="MESSAGE">
                  <block type="variables_get" id="+q=kaeZ!;S(~1D_~dU2w">
                    <field name="VAR" id="0@xj{M^u?~ln2y~+o;h9">text2</field>
                  </block>
                </value>
                <next>
                  <block type="controls_if" id="tbLrvyn$q/o!J4OR|8IE">
                    <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                    <value name="IF0">
                      <block type="logic_compare" id="Hv~=uW:Ia5IjVQ9ZhgEF">
                        <field name="OP">LT</field>
                        <value name="A">
                          <block type="total_profit" id=",765gH]]`*(rdWsooFyW"></block>
                        </value>
                        <value name="B">
                          <block type="variables_get" id="65I8e{UoF(0pT`W4`2!c">
                            <field name="VAR" id="mXtFswo{p,|%W1:V-$+r">Target Profit</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="trade_again" id="fRWBm;s8*eHMJv=Eu=4^"></block>
                    </statement>
                    <statement name="ELSE">
                      <block type="text_join" id="GYMlII}mzrIkPeaK1A-r">
                        <field name="VARIABLE" id="lgSt*w)b%TEj7!b{j4qF">text3</field>
                        <statement name="STACK">
                          <block type="text_statement" id="?:XBi;f%-%eZW6n!*l(P">
                            <value name="TEXT">
                              <shadow type="text" id="}(|<EMAIL>-Y">
                                <field name="TEXT"></field>
                              </shadow>
                              <block type="text" id="gU:M}qeW2HvNViG5:o}#">
                                <field name="TEXT">TITANTRADER BOT WORKED</field>
                              </block>
                            </value>
                            <next>
                              <block type="text_statement" id="/qrz%FCqZMwv9p;?SZr;">
                                <value name="TEXT">
                                  <shadow type="text" id="9~v@o5)@p`sltr)7iP[K">
                                    <field name="TEXT"></field>
                                  </shadow>
                                  <block type="total_profit" id="26GaB4s$AUuhDIO}5z1M"></block>
                                </value>
                              </block>
                            </next>
                          </block>
                        </statement>
                        <next>
                          <block type="text_print" id="+]%lyF5l4KM{vE}1K.{c">
                            <value name="TEXT">
                              <shadow type="text" id="triFqoh]MPIzH1?eQHp|">
                                <field name="TEXT">abc</field>
                              </shadow>
                              <block type="variables_get" id="LDv8})K}gr`{FO^u5iA8">
                                <field name="VAR" id="lgSt*w)b%TEj7!b{j4qF">text3</field>
                              </block>
                            </value>
                          </block>
                        </next>
                      </block>
                    </statement>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
  </block>
  <block type="before_purchase" id="Er[Vx{rVky5?AMXkJ#@D" deletable="false" x="0" y="820">
    <statement name="BEFOREPURCHASE_STACK">
      <block type="purchase" id="ye{lf|a_o6wy6w50+YT0">
        <field name="PURCHASE_LIST">DIGITUNDER</field>
      </block>
    </statement>
  </block>
  <block type="math_number" id="aMcE)uwz_UJsa@moH^-x" disabled="true" x="0" y="1714">
    <field name="NUM">5</field>
  </block>
  <block type="text" id="}-c{R*kCN~kvgOO?JWiT" collapsed="true" disabled="true" x="0" y="1802">
    <field name="TEXT">Expert  Speed Bot</field>
  </block>
</xml>