@use 'components/shared/styles/constants' as *;
@use 'components/shared/styles/mixins' as *;

.app-footer {
    height: 3.6rem;
    display: flex;
    flex-direction: row-reverse;
    padding: 0 1rem;
    align-items: center;
    position: fixed;
    bottom: 0;
    right: 0;
    left: 0;
    border-top: 1px solid var(--general-section-1);
    box-shadow: 0 1px 0 0 var(--general-section-1) inset;
    background: var(--general-main-1);
    z-index: 2;

    &__icon {
        padding: 0.8rem;

        svg > g > path,
        svg > path {
            fill: var(--text-prominent);
        }

        @include mobile-or-tablet-screen {
            padding: 0;
        }
    }

    &__vertical-line {
        width: 0.1rem;
        height: 1.6rem;
        background-color: var(--general-section-1);
        margin: 0 0.8rem;
    }

    &__language {
        display: flex;
        align-items: center;
        padding: 0.8rem;

        & span {
            margin-inline-start: 0.4rem;
        }
    }

    &__network-status {
        width: 1rem;
        height: 1rem;
        border-radius: 100%;

        &-online {
            background-color: $color-green-1;
        }

        &-offline {
            background-color: $color-red;
        }

        &-blinking {
            animation: blink 1s infinite alternate;
        }

        @include mobile-or-tablet-screen {
            margin-inline-start: 1.2rem;
        }
    }

    &__endpoint {
        margin-inline-end: 2rem;

        &-text {
            color: $color-blue;
            text-decoration: underline;
        }
    }
}

.languages-modal__body {
    padding-inline: 1.6rem;

    &-button {
        height: 8.8rem;
        width: 13.3rem;
        display: flex;
        align-items: center;
        flex-direction: column;
        padding: 0.8rem;

        &-selected {
            border-radius: 4px;
            border: 1px solid var(--brand-blue, $color-green);
        }
    }
}

@keyframes blink {
    0% {
        opacity: 1;
    }

    100% {
        opacity: 0.2;
    }
}
