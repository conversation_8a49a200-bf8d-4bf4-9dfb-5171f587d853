#!/usr/bin/env node

/**
 * Domain Testing Helper
 * Quick access to all domain test URLs
 */

const { execSync } = require('child_process');
const os = require('os');

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    green: '\x1b[32m',
    blue: '\x1b[34m',
    yellow: '\x1b[33m',
    red: '\x1b[31m',
    cyan: '\x1b[36m',
    magenta: '\x1b[35m',
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function getLocalIP() {
    const interfaces = os.networkInterfaces();
    for (const name of Object.keys(interfaces)) {
        for (const interface of interfaces[name]) {
            if (interface.family === 'IPv4' && !interface.internal) {
                return interface.address;
            }
        }
    }
    return 'localhost';
}

const domains = [
    { name: 'Kenyan<PERSON><PERSON>ssy', domain: 'kenyanhennessy.site', bots: 5, specialty: 'Matrix & Sniper Bots' },
    { name: 'WallaceTraders', domain: 'wallacetraders.site', bots: 6, specialty: 'Speed Bots' },
    { name: 'TraderMaster', domain: 'tradermaster.site', bots: 8, specialty: 'State-based Strategies' },
    { name: 'GleTraders', domain: 'gletraders.site', bots: 25, specialty: 'Largest Collection' },
    { name: 'Legoo', domain: 'legoo.site', bots: 10, specialty: 'Premium AI Bots' },
    { name: 'KingsTraders', domain: 'kingstraders.site', bots: 5, specialty: 'Recovery & Execution' },
    { name: 'MasterHunter', domain: 'masterhunter.site', bots: 4, specialty: 'Hunter Series' },
    { name: 'D-Analysis', domain: 'd-analysis.site', bots: 25, specialty: 'Mirror Collection' },
    { name: 'SwiftPro', domain: 'swiftpro.site', bots: 43, specialty: 'Swift Pro Collection' },
    { name: 'PromoClub', domain: 'promoclub.site', bots: 0, specialty: 'Fallback/Default' },
    { name: 'DbotPrinters', domain: 'dbotprinters.site', bots: 0, specialty: 'Fallback/Default' },
];

function displayAllDomains() {
    const port = process.env.PORT || 3000;
    const baseUrl = `http://localhost:${port}`;

    log('\n🌐 All Domain Test URLs', 'bright');
    log('═══════════════════════════════════════════════════════════', 'cyan');

    domains.forEach((domain, index) => {
        const url = `${baseUrl}/free-bots?bots_domain=${domain.domain}`;
        const botInfo = domain.bots > 0 ? `${domain.bots} bots` : 'empty';

        log(`${index + 1}. ${domain.name}`, 'yellow');
        log(`   ${url}`, 'green');
        log(`   📊 ${botInfo} - ${domain.specialty}`, 'blue');
        log('');
    });

    log('💡 Copy any URL above and paste in your browser to test that domain', 'cyan');
}

function displayTopDomains() {
    const port = process.env.PORT || 3000;
    const baseUrl = `http://localhost:${port}`;

    log('\n🎯 Top Domains for Testing', 'bright');
    log('═══════════════════════════════════════', 'cyan');

    const topDomains = domains.filter(d => d.bots > 5).sort((a, b) => b.bots - a.bots);

    topDomains.forEach((domain, index) => {
        const url = `${baseUrl}/free-bots?bots_domain=${domain.domain}`;
        log(`${index + 1}. ${domain.name} (${domain.bots} bots)`, 'yellow');
        log(`   ${url}`, 'green');
    });
}

function displayMobileUrls() {
    const localIP = getLocalIP();
    const port = process.env.PORT || 3000;
    const baseUrl = `http://${localIP}:${port}`;

    log('\n📱 Mobile Testing URLs', 'bright');
    log('═══════════════════════════════════════', 'cyan');
    log(`Use these URLs on mobile devices connected to the same WiFi:`, 'yellow');
    log('');

    domains.slice(0, 5).forEach((domain, index) => {
        const url = `${baseUrl}/free-bots?bots_domain=${domain.domain}`;
        log(`${index + 1}. ${domain.name}`, 'yellow');
        log(`   ${url}`, 'green');
    });

    log(`\n💡 Your local IP: ${localIP}`, 'cyan');
}

function openDomain(domainName) {
    const domain = domains.find(
        d => d.name.toLowerCase() === domainName.toLowerCase() || d.domain.toLowerCase() === domainName.toLowerCase()
    );

    if (!domain) {
        log(`❌ Domain "${domainName}" not found`, 'red');
        log('Available domains:', 'yellow');
        domains.forEach(d => log(`  - ${d.name} (${d.domain})`, 'blue'));
        return;
    }

    const port = process.env.PORT || 3000;
    const url = `http://localhost:${port}/free-bots?bots_domain=${domain.domain}`;

    log(`🚀 Opening ${domain.name}...`, 'green');
    log(`📊 ${domain.bots} bots - ${domain.specialty}`, 'blue');
    log(`🔗 ${url}`, 'cyan');

    try {
        if (process.platform === 'win32') {
            execSync(`start ${url}`, { stdio: 'ignore' });
        } else if (process.platform === 'darwin') {
            execSync(`open ${url}`, { stdio: 'ignore' });
        } else {
            execSync(`xdg-open ${url}`, { stdio: 'ignore' });
        }
        log('✅ Opened in browser', 'green');
    } catch (error) {
        log('❌ Could not open browser automatically', 'red');
        log('Please copy the URL above and paste in your browser', 'yellow');
    }
}

function displayHelp() {
    log('\n🛠️  Domain Testing Helper', 'bright');
    log('\nUsage:', 'cyan');
    log('  node scripts/domain-tester.js [command] [domain]', 'green');
    log('\nCommands:', 'cyan');
    log('  all, list      Show all domain URLs (default)', 'yellow');
    log('  top            Show top domains with most bots', 'yellow');
    log('  mobile         Show mobile testing URLs', 'yellow');
    log('  open <domain>  Open specific domain in browser', 'yellow');
    log('  --help, -h     Show this help message', 'yellow');
    log('\nExamples:', 'cyan');
    log('  node scripts/domain-tester.js', 'green');
    log('  node scripts/domain-tester.js top', 'green');
    log('  node scripts/domain-tester.js open wallacetraders', 'green');
    log('  node scripts/domain-tester.js open gletraders.site', 'green');
}

// Parse command line arguments
const args = process.argv.slice(2);
const command = args[0];
const domainArg = args[1];

if (args.includes('--help') || args.includes('-h')) {
    displayHelp();
    process.exit(0);
}

switch (command) {
    case 'top':
        displayTopDomains();
        break;

    case 'mobile':
        displayMobileUrls();
        break;

    case 'open':
        if (!domainArg) {
            log('❌ Please specify a domain to open', 'red');
            log('Example: node scripts/domain-tester.js open wallacetraders', 'yellow');
            process.exit(1);
        }
        openDomain(domainArg);
        break;

    case 'all':
    case 'list':
    default:
        displayAllDomains();
        break;
}
