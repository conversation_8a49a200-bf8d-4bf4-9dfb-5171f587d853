[{"name": "1 2025 $Orginal DollarPrinterBot  2025 Version $ (1)", "file": "1 2025 $Orginal DollarPrinterBot  2025 Version $ (1).xml"}, {"name": "2025 Alpha Version 2025", "file": "2025 Alpha Version 2025.xml"}, {"name": "3 Updated Version Of Candle Mine🇬🇧", "file": "3 Updated Version Of Candle Mine🇬🇧.xml"}, {"name": "AUTO ANALYSIS BOT", "file": "AUTO ANALYSIS BOT.xml"}, {"name": "COOLKID", "file": "COOLKID.xml"}, {"name": "Candle mine version 3.1", "file": "Candle mine version 3.1.xml"}, {"name": "Deriv wizard 1", "file": "Deriv wizard 1.xml"}, {"name": "Digit hyper", "file": "Digit hyper.xml"}, {"name": "Even odd speed bot", "file": "Even odd speed bot.xml"}, {"name": "FALCON BOT", "file": "FALCON BOT.xml"}, {"name": "GIBUU V8 PRO", "file": "GIBUU V8 PRO.xml"}, {"name": "M27 Original version", "file": "M27 Original version.xml"}, {"name": "MATCHES AND DIFFERS BOT", "file": "MATCHES AND DIFFERS BOT.xml"}, {"name": "MEGA PRO BOT", "file": "MEGA PRO BOT.xml"}, {"name": "Mask evenodd bot", "file": "Mask evenodd bot.xml"}, {"name": "NIGHT  CAP PRINTER BOT", "file": "NIGHT  CAP PRINTER BOT.xml"}, {"name": "SCAPLEX   Ai  SN4 (1) (1)", "file": "SCAPLEX   Ai  SN4 (1) (1).xml"}, {"name": "SCAUCER SPEED BOT 🇱🇷", "file": "SCAUCER SPEED BOT 🇱🇷.xml"}, {"name": "THE DOLLAR PRO", "file": "THE DOLLAR PRO.xml"}, {"name": "THE TREND LOVER", "file": "THE TREND LOVER.xml"}, {"name": "TRADE CITY BOT VERSION 2.1", "file": "TRADE CITY BOT VERSION 2.1.xml"}, {"name": "The Over 19 switcher5", "file": "The Over 19 switcher5.xml"}, {"name": "ULTRA AI 2025", "file": "ULTRA AI 2025.xml"}, {"name": "mask matches speed bot 📈", "file": "mask matches speed bot 📈.xml"}]