<xml xmlns="https://developers.google.com/blockly/xml" is_dbot="true" collection="false">
  <variables>
    <variable id="W4$:ZQCmEz#8+=4ysv5Y">Loss</variable>
    <variable id="j}8O`Vs+RJljIwPu-_:_">Stake</variable>
    <variable id="xpEyvXGN[Zk^sUWD7nYK">text</variable>
    <variable id="OLlf3ee;!2u@65HcIm#f">text1</variable>
    <variable id="[_5Td!lAU9~;R424(8fn">text2</variable>
    <variable id="mXtFswo{p,|%W1:V-$+r">Target Profit</variable>
    <variable id="%L?;380E6Lr^3b.%}t5Q">stake 2</variable>
    <variable id="6F[$1McEdIyAzpbK,f{d">text3</variable>
  </variables>
  <block type="trade_definition" id="FjiS-;U!Gth.#}3imHFv" deletable="false" x="0" y="50">
    <statement name="TRADE_OPTIONS">
      <block type="trade_definition_market" id="Z-]r$^~$r@Rp~CYY/*xw" deletable="false" movable="false">
        <field name="MARKET_LIST">synthetic_index</field>
        <field name="SUBMARKET_LIST">random_index</field>
        <field name="SYMBOL_LIST">R_100</field>
        <next>
          <block type="trade_definition_tradetype" id="@KOSZRcqC*PWykpK~}M-" deletable="false" movable="false">
            <field name="TRADETYPECAT_LIST">digits</field>
            <field name="TRADETYPE_LIST">overunder</field>
            <next>
              <block type="trade_definition_contracttype" id="hZh]p%x_LugyFe;.-*3k" deletable="false" movable="false">
                <field name="TYPE_LIST">DIGITOVER</field>
                <next>
                  <block type="trade_definition_candleinterval" id="l*-=/5om[.v$HHcC{z%k" deletable="false" movable="false">
                    <field name="CANDLEINTERVAL_LIST">60</field>
                    <next>
                      <block type="trade_definition_restartbuysell" id="4}MXWVHW6.x(3H$yZw`E" deletable="false" movable="false">
                        <field name="TIME_MACHINE_ENABLED">FALSE</field>
                        <next>
                          <block type="trade_definition_restartonerror" id="8xwyz!.KY}|;Sp%,6;^u" deletable="false" movable="false">
                            <field name="RESTARTONERROR">TRUE</field>
                          </block>
                        </next>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="INITIALIZATION">
      <block type="text_print" id="2|P(]XWNF6}S5S;4AT,)" collapsed="true">
        <value name="TEXT">
          <shadow type="text" id="O}H)i:}8ioJIAh(%g)l{">
            <field name="TEXT">About To Print Dollars 💵 All The Best</field>
          </shadow>
        </value>
        <next>
          <block type="variables_set" id="c[v_cDl1HB[$GtX:Wj*6">
            <field name="VAR" id="W4$:ZQCmEz#8+=4ysv5Y">Loss</field>
            <value name="VALUE">
              <block type="math_number" id="8Ib0BfaKAsIZY}(lP5co">
                <field name="NUM">1000</field>
              </block>
            </value>
            <next>
              <block type="variables_set" id=".{f=DdMOmwB8#2v#;|~^">
                <field name="VAR" id="mXtFswo{p,|%W1:V-$+r">Target Profit</field>
                <value name="VALUE">
                  <block type="math_number" id="w6t0vFs(;#;)dzKG(YdG">
                    <field name="NUM">607</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="vKQhHliZijnjU=:Bg?Vu">
                    <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">Stake</field>
                    <value name="VALUE">
                      <block type="math_number" id="*(ruXWju+C#@y)FX~N=m">
                        <field name="NUM">2</field>
                      </block>
                    </value>
                    <next>
                      <block type="variables_set" id="|1s?2J9y$lU-3lO(hWIq">
                        <field name="VAR" id="%L?;380E6Lr^3b.%}t5Q">stake 2</field>
                        <value name="VALUE">
                          <block type="math_number" id="?/_BbYy[hP~B%i|!cj(!">
                            <field name="NUM">2</field>
                          </block>
                        </value>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
    <statement name="SUBMARKET">
      <block type="trade_definition_tradeoptions" id="rCtIQ|TplM1r~R2}*w#E">
        <mutation xmlns="http://www.w3.org/1999/xhtml" has_first_barrier="false" has_second_barrier="false" has_prediction="true"></mutation>
        <field name="DURATIONTYPE_LIST">t</field>
        <value name="DURATION">
          <block type="math_number" id="zJP.(b^fzwSK+$Pcfr|i">
            <field name="NUM">1</field>
          </block>
        </value>
        <value name="AMOUNT">
          <block type="variables_get" id="9W$0#Cx(0N*Bt..-C(1%">
            <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">Stake</field>
          </block>
        </value>
        <value name="PREDICTION">
          <shadow type="math_number" id="o;4A88]A~}LU(wxzSt1=">
            <field name="NUM">1</field>
          </shadow>
        </value>
      </block>
    </statement>
  </block>
  <block type="during_purchase" id="BSjK?lRw2O/~z4_OdZBJ" collapsed="true" x="714" y="50"></block>
  <block type="after_purchase" id="Ld)LY,Hlft/14gLD_:LS" x="714" y="196">
    <statement name="AFTERPURCHASE_STACK">
      <block type="controls_if" id="ypbTe|Lzu.8iC#[^iLQa">
        <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
        <value name="IF0">
          <block type="contract_check_result" id="EWP5)I#;)^x?}:_9%69O">
            <field name="CHECK_RESULT">win</field>
          </block>
        </value>
        <statement name="DO0">
          <block type="text_join" id="uFmElYTIlAimTUF(--)/">
            <field name="VARIABLE" id="xpEyvXGN[Zk^sUWD7nYK">text</field>
            <statement name="STACK">
              <block type="text_statement" id="TewlSz%QIw~UIw)Zz+AM">
                <value name="TEXT">
                  <shadow type="text" id="yhuKcHc!,)~`U;3X0ylD">
                    <field name="TEXT"></field>
                  </shadow>
                  <block type="text" id="_scRoB/2R%NlJS}~k*64">
                    <field name="TEXT">Ganhou: </field>
                  </block>
                </value>
                <next>
                  <block type="text_statement" id="Yb8dEW/Zpr=!=/|Go/cb">
                    <value name="TEXT">
                      <shadow type="text" id="d_/6ZO5YXGz)UyLBLq$H">
                        <field name="TEXT"></field>
                      </shadow>
                      <block type="read_details" id="]?Qr^rNs.UEl|;i/VWn-">
                        <field name="DETAIL_INDEX">4</field>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </statement>
            <next>
              <block type="traderlegoonotify" id="m+H#b2e33/QdfGz`/KyV">
                <field name="NOTIFICATION_TYPE">success</field>
                <field name="NOTIFICATION_SOUND">silent</field>
                <value name="MESSAGE">
                  <block type="variables_get" id="rCJ5-QU,=Z|}2LBmLQv/">
                    <field name="VAR" id="xpEyvXGN[Zk^sUWD7nYK">text</field>
                  </block>
                </value>
                <next>
                  <block type="variables_set" id="A4m1124L~G543-f^;-BR">
                    <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">Stake</field>
                    <value name="VALUE">
                      <block type="variables_get" id="|[w;}(^J7xWTBX6Y_=1(">
                        <field name="VAR" id="%L?;380E6Lr^3b.%}t5Q">stake 2</field>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </statement>
        <statement name="ELSE">
          <block type="text_join" id="|7;UiCnf7=/#+*:%#X;-">
            <field name="VARIABLE" id="OLlf3ee;!2u@65HcIm#f">text1</field>
            <statement name="STACK">
              <block type="text_statement" id="hQfuiK~UgxFhLnGGRMy!">
                <value name="TEXT">
                  <shadow type="text" id="P/n?OtB?xg?srUV*DrsT">
                    <field name="TEXT"></field>
                  </shadow>
                  <block type="text" id="vNr-Xdh,^h$3K!WAW{x4">
                    <field name="TEXT">Perdeu: </field>
                  </block>
                </value>
                <next>
                  <block type="text_statement" id="%ZUg95Z%Z4(G@kg+nYdh">
                    <value name="TEXT">
                      <shadow type="text" id="m:[tfg$!qy$rLCKUj.Zv">
                        <field name="TEXT"></field>
                      </shadow>
                      <block type="math_single" id="i{iGUNZ9_fKmeVQ]1xpn">
                        <field name="OP">ABS</field>
                        <value name="NUM">
                          <shadow type="math_number" id="UHg)JRUz*!A#Sy)S80Aa">
                            <field name="NUM">9</field>
                          </shadow>
                          <block type="read_details" id="QQa%G*j4XnXn$?Z0FqkQ">
                            <field name="DETAIL_INDEX">4</field>
                          </block>
                        </value>
                      </block>
                    </value>
                  </block>
                </next>
              </block>
            </statement>
            <next>
              <block type="traderlegoonotify" id="SoNsJGbHNjJ=q+5dPG#8">
                <field name="NOTIFICATION_TYPE">warn</field>
                <field name="NOTIFICATION_SOUND">silent</field>
                <value name="MESSAGE">
                  <block type="variables_get" id="ZO%V|7S-KDwWx6s9+o6N">
                    <field name="VAR" id="OLlf3ee;!2u@65HcIm#f">text1</field>
                  </block>
                </value>
                <next>
                  <block type="math_change" id="r],}1f)[mi^D^_j9{aWV">
                    <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">Stake</field>
                    <value name="DELTA">
                      <shadow type="math_number" id=".rd?UW7#]D1]j#)Q4`tX">
                        <field name="NUM">1</field>
                      </shadow>
                      <block type="math_arithmetic" id="5GFYbOQg2WZFR:/!~xH:">
                        <field name="OP">MULTIPLY</field>
                        <value name="A">
                          <shadow type="math_number" id="Er^a+K,%kV6]b+n?o:wX">
                            <field name="NUM">1</field>
                          </shadow>
                          <block type="math_single" id="gDr5[?anS!hsWSFC)0.P">
                            <field name="OP">ABS</field>
                            <value name="NUM">
                              <shadow type="math_number" id="x6azM1g8UKV`:{mv%C.7">
                                <field name="NUM">9</field>
                              </shadow>
                              <block type="read_details" id="{P1dNMOnr1qPSW%T)c]Y">
                                <field name="DETAIL_INDEX">4</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <value name="B">
                          <shadow type="math_number" id="Yn,ZlPlAZL5~0l1k1TJ%">
                            <field name="NUM">1</field>
                          </shadow>
                          <block type="math_number" id="#lysfu@G?CmAuMX3/KVH">
                            <field name="NUM">1</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <next>
                      <block type="controls_if" id="%-(Y}]Z??^_]|IA25:aJ">
                        <value name="IF0">
                          <block type="logic_compare" id=",v`iChq$vswg15SV0L.|">
                            <field name="OP">GTE</field>
                            <value name="A">
                              <block type="math_single" id=",)o3^3CnaR[|oa}azByV">
                                <field name="OP">ABS</field>
                                <value name="NUM">
                                  <shadow type="math_number" id=".yy=0H3@-A+#qg@Ei}uS">
                                    <field name="NUM">9</field>
                                  </shadow>
                                  <block type="read_details" id="m:SFZD9Aw=TfEus?iV-Z">
                                    <field name="DETAIL_INDEX">4</field>
                                  </block>
                                </value>
                              </block>
                            </value>
                            <value name="B">
                              <block type="variables_get" id="^U77OiMw*2.HGXfLPrrk">
                                <field name="VAR" id="W4$:ZQCmEz#8+=4ysv5Y">Loss</field>
                              </block>
                            </value>
                          </block>
                        </value>
                        <statement name="DO0">
                          <block type="variables_set" id="@(#Bg.MR^U-R%jUK(3=X">
                            <field name="VAR" id="j}8O`Vs+RJljIwPu-_:_">Stake</field>
                            <value name="VALUE">
                              <block type="variables_get" id="FI7:hUX+Ed]/rJ=py6Hy">
                                <field name="VAR" id="%L?;380E6Lr^3b.%}t5Q">stake 2</field>
                              </block>
                            </value>
                          </block>
                        </statement>
                      </block>
                    </next>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </statement>
        <next>
          <block type="text_join" id="L(qnP%l!`KhFm(6@dzze">
            <field name="VARIABLE" id="[_5Td!lAU9~;R424(8fn">text2</field>
            <statement name="STACK">
              <block type="text_statement" id="%A:@aeWl6xZ~D2%@F/`Y">
                <value name="TEXT">
                  <shadow type="text" id="DW1)oG/%qT8`3RDQ%aqH">
                    <field name="TEXT"></field>
                  </shadow>
                  <block type="text" id="0+VzOaaYI.[Vs:Nb[Gz]">
                    <field name="TEXT">Total Profit: </field>
                  </block>
                </value>
                <next>
                  <block type="text_statement" id="Th04lPkt?Uxw|rcc=~zj">
                    <value name="TEXT">
                      <shadow type="text" id="*kpx[VpY=mdmbLLY3R8$">
                        <field name="TEXT"></field>
                      </shadow>
                      <block type="total_profit" id="%[MZSGL[9g7I!/VNn8Ou"></block>
                    </value>
                  </block>
                </next>
              </block>
            </statement>
            <next>
              <block type="traderlegoonotify" id="cKEE8,.#%wc(Y}K)2{4%">
                <field name="NOTIFICATION_TYPE">info</field>
                <field name="NOTIFICATION_SOUND">silent</field>
                <value name="MESSAGE">
                  <block type="variables_get" id="+0~scL,/@RY~83k4vZcb">
                    <field name="VAR" id="[_5Td!lAU9~;R424(8fn">text2</field>
                  </block>
                </value>
                <next>
                  <block type="controls_if" id="/i{aOP~lI9|@~BDNn*J$">
                    <mutation xmlns="http://www.w3.org/1999/xhtml" else="1"></mutation>
                    <value name="IF0">
                      <block type="logic_compare" id="Z{W[Z%(J1q^^`1n=!!$`">
                        <field name="OP">LT</field>
                        <value name="A">
                          <block type="total_profit" id="NOWG@5LF+i:4?2RN/A(C"></block>
                        </value>
                        <value name="B">
                          <block type="variables_get" id="ER=`!s@3^4s%n.rg2W4J">
                            <field name="VAR" id="mXtFswo{p,|%W1:V-$+r">Target Profit</field>
                          </block>
                        </value>
                      </block>
                    </value>
                    <statement name="DO0">
                      <block type="trade_again" id="KoQ$%RE|_;a3$VMVW?x1"></block>
                    </statement>
                    <statement name="ELSE">
                      <block type="text_join" id="0JW4AmH;HBljdFAulA}S">
                        <field name="VARIABLE" id="6F[$1McEdIyAzpbK,f{d">text3</field>
                        <statement name="STACK">
                          <block type="text_statement" id="1f`=o1|3vGY*An6rjh(a">
                            <value name="TEXT">
                              <shadow type="text" id="YXk5YH!r{^hH?!7K_fnf">
                                <field name="TEXT"></field>
                              </shadow>
                              <block type="text" id="R11n6S|a)!CI7niTGR9a">
                                <field name="TEXT">Dollars Printed Successfully : </field>
                              </block>
                            </value>
                            <next>
                              <block type="text_statement" id=")56t:tB2[UG7zJtK/=?z">
                                <value name="TEXT">
                                  <shadow type="text" id="k)V7r_43OPSXE@Eb=t[0">
                                    <field name="TEXT"></field>
                                  </shadow>
                                  <block type="total_profit" id="0^W=4roPH=?B~6xlWEh{"></block>
                                </value>
                              </block>
                            </next>
                          </block>
                        </statement>
                        <next>
                          <block type="text_print" id=",[yU.^d4VD_NKh:+M`@N">
                            <value name="TEXT">
                              <shadow type="text" id="t;t_afcH$mU6v-fFCdZ9">
                                <field name="TEXT">abc</field>
                              </shadow>
                              <block type="variables_get" id="*I.RJ}n.Eo)3v9UjAa3x">
                                <field name="VAR" id="6F[$1McEdIyAzpbK,f{d">text3</field>
                              </block>
                            </value>
                          </block>
                        </next>
                      </block>
                    </statement>
                  </block>
                </next>
              </block>
            </next>
          </block>
        </next>
      </block>
    </statement>
  </block>
  <block type="before_purchase" id="y41{274IG/$y@6H15n?6" collapsed="true" deletable="false" x="0" y="908">
    <statement name="BEFOREPURCHASE_STACK">
      <block type="traderlegoo_purchase" id="HV%bo9`,*naIv[~-V~`K">
        <field name="PURCHASE_LIST">DIGITOVER</field>
      </block>
    </statement>
  </block>
  <block type="math_number" id="j_+90K@@XL(`ML.0d6Ci" disabled="true" x="0" y="1796">
    <field name="NUM">5</field>
  </block>
  <block type="text" id="Rl/)KOd$9Yr:BN:8G{-I" collapsed="true" disabled="true" x="0" y="1884">
    <field name="TEXT">Expert  Speed Bot</field>
  </block>
</xml>